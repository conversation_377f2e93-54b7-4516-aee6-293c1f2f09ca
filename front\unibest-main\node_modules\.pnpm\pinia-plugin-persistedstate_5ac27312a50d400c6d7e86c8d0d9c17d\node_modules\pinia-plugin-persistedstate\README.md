# `pinia-plugin-persistedstate`

> Pinia plugin to persist stores

## 🚀 Quickstart

1. Install with your favorite package manager:
   - **pnpm** : `pnpm i pinia-plugin-persistedstate`
   - npm : `npm i pinia-plugin-persistedstate`
   - yarn : `yarn add pinia-plugin-persistedstate`

2. Add the plugin to pinia:
```ts
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
```


## 🔧 Configuration

Usage and configuration informations can be found in [the documentation](https://prazdevs.github.io/pinia-plugin-persistedstate/guide/)


## 📝 License

Copyright © 2022 [<PERSON><PERSON>](https://github.com/prazdevs).  
This project is under [MIT](https://github.com/prazdevs/pinia-plugin-persistedstate/blob/main/LICENSE) license.
