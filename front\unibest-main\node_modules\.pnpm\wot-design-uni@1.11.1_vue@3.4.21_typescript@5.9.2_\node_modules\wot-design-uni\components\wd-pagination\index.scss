@import "./../common/abstracts/_mixin.scss";
@import "./../common/abstracts/variable.scss";

.wot-theme-dark {
  @include b(pager) {
    background-color: $-dark-background;
    @include e(message) {
      color: $-dark-color3;
    }
  }
}

@include b(pager) {
  user-select: none;
  background-color: #fff;

  @include edeep(icon) {
    font-size: $-pagination-icon-size;
  }

  @include e(content) {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: $-pagination-content-padding;
  }
  @include e(message) {
    text-align: center;
    color:$-pagination-message-color;
    font-size: $-pagination-message-fs;
    padding: $-pagination-message-padding;
  }
  @include edeep(nav) {
    min-width: $-pagination-nav-width;
    @include m(active){
      color: rgba(0,0,0,0.65)      
    }
    @include m(disabled){
      color: rgba(0,0,0,0.15)      
    }
  }
  @include e(size){
    flex: 1;
    text-align: center;
    font-size: $-pagination-nav-content-fs;
  }
  @include e(separator){
    padding: $-pagination-nav-sepatator-padding;
  }
  @include edeep(left){
    transform: rotate(180deg) translateY(1px);
    display: inline-block;
  }
  @include e(current){
    color: $-pagination-nav-current-color;
  }
}