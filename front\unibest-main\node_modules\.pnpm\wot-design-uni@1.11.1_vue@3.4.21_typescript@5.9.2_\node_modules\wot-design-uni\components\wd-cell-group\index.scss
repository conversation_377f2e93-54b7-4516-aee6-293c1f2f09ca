@import '../common/abstracts/variable.scss';
@import '../common/abstracts/_mixin.scss';

.wot-theme-dark {
  @include b(cell-group) {
    background-color: $-dark-background2;

    @include when(border) {
      .wd-cell-group__title {
        @include halfPixelBorder('bottom', 0, $-dark-border-color);
      }
    }

    @include e(title) {
      background: $-dark-background2;
      color: $-dark-color;
    }

    @include e(right) {
      color: $-dark-color3;
    }

    @include e(body) {
      background: $-dark-background2;
    }

  }
}

@include b(cell-group) {
  background-color: $-color-white;

  @include when(border) {
    .wd-cell-group__title {
      @include halfPixelBorder;
    }
  }
  @include e(title) {
    position: relative;
    display: flex;
    justify-content: space-between;
    padding: $-cell-group-padding;
    background: $-color-white;
    font-size: $-cell-group-title-fs;
    color: $-cell-group-title-color;
    font-weight: $-fw-medium;
    line-height: 1.43;
  }
  @include e(right) {
    color: $-cell-group-value-color;
    font-size: $-cell-group-value-fs;
  }
  @include e(body) {
    background: $-color-white;
  }
}