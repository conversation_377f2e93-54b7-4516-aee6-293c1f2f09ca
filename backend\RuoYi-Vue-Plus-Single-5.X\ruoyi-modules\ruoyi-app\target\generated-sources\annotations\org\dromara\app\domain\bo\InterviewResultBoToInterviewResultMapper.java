package org.dromara.app.domain.bo;

import io.github.linpeilie.AutoMapperConfig__129;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.InterviewResult;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__129.class,
    uses = {},
    imports = {}
)
public interface InterviewResultBoToInterviewResultMapper extends BaseMapper<InterviewResultBo, InterviewResult> {
}
