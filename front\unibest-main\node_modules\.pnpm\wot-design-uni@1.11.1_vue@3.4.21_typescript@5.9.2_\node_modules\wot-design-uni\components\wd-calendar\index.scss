@import '../common/abstracts/variable';
@import '../common/abstracts/mixin';

.wot-theme-dark {
  @include b(calendar) {

    @include e(title) {
      color: $-dark-color;
    }

    :deep(.wd-calendar__arrow),
    :deep(.wd-calendar__close),
    :deep(.wd-calendar__clear) {
      color: $-dark-color;
    }

    @include e(range-label-item) {
      color: $-dark-color;

      @include when(placeholder) {
        color: $-dark-color-gray;
      }
    }

    @include e(range-sperator) {
      color: $-dark-color-gray;
    }

    :deep(.wd-calendar__cell--placeholder) {
      .wd-cell__value {
        color: $-dark-color-gray;
      }
    }
  }
}

@include b(calendar) {

  @include e(header) {
    position: relative;
    overflow: hidden;
  }

  @include e(title) {
    color: $-action-sheet-color;
    height: $-action-sheet-title-height;
    line-height: $-action-sheet-title-height;
    text-align: center;
    font-size: $-action-sheet-title-fs;
    font-weight: $-action-sheet-weight;
  }

  @include edeep(close) {
    position: absolute;
    top: $-action-sheet-close-top;
    right: $-action-sheet-close-right;
    color: $-action-sheet-close-color;
    font-size: $-action-sheet-close-fs;
    transform: rotate(-45deg);
    line-height: 1.1;
  }

  @include e(tabs) {
    width: 222px;
    margin: 10px auto 12px;
  }

  @include e(shortcuts) {
    padding: 20px 0;
    text-align: center;
  }

  @include edeep(tag) {
    margin-right: 8px;
  }

  @include e(view) {
    @include when(show-confirm) {
      height: 394px;

      @include when(range) {
        height: 384px;
      }
    }
  }

  @include e(range-label) {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;

    @include when(monthrange) {
      padding-bottom: 10px;
      box-shadow: 0px 4px 8px 0 rgba(0, 0, 0, 0.02);
    }
  }

  @include e(range-label-item) {
    flex: 1;
    color: rgba(0, 0, 0, 0.85);

    @include when(placeholder) {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  @include e(range-sperator) {
    margin: 0 24px;
    color: rgba(0, 0, 0, 0.25);
  }

  @include e(confirm) {
    padding: 12px 25px 14px;
  }

  @include edeep(cell) {
    @include when(disabled) {
      .wd-cell__value {
        color: $-input-disabled-color;
        cursor: not-allowed;
      }
    }
    @include when(error) {
      .wd-cell__value {
        color: $-input-error-color;
      }
      :deep(.wd-calendar__arrow) {
        color: $-input-error-color;
      }
    }
    @include when(large) {
      .wd-calendar__arrow {
        font-size: $-cell-icon-size-large;
      }
    }

    @include m(placeholder) {
      .wd-cell__value {
        color: $-input-placeholder-color;
      }
    }
  }
  
  @include edeep(arrow) {
    display: block;
    font-size: $-cell-icon-size;
    color: $-cell-arrow-color;
    line-height: $-cell-line-height;
  }

  @include edeep(clear) {
    display: block;
    font-size: $-cell-icon-size;
    color: $-cell-clear-color;
    line-height: $-cell-line-height;
  }
}