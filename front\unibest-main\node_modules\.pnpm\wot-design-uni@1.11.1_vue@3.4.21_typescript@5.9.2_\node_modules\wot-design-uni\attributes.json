{"wd-action-sheet/v-model": {"type": "boolean", "description": "设置菜单显示隐藏\n\n[Docs](https://wot-design-uni.cn/component/action-sheet.html#attributes)"}, "wd-action-sheet/actions": {"type": "array", "description": "菜单选项, default: [].\n\n[Docs](https://wot-design-uni.cn/component/action-sheet.html#attributes)"}, "wd-action-sheet/panels": {"type": "array", "description": "自定义面板项,可以为字符串数组，也可以为对象数组，如果为二维数组，则为多行展示, default: [].\n\n[Docs](https://wot-design-uni.cn/component/action-sheet.html#attributes)"}, "wd-action-sheet/title": {"type": "string", "description": "标题\n\n[Docs](https://wot-design-uni.cn/component/action-sheet.html#attributes)"}, "wd-action-sheet/cancel-text": {"type": "string", "description": "取消按钮文案\n\n[Docs](https://wot-design-uni.cn/component/action-sheet.html#attributes)"}, "wd-action-sheet/close-on-click-action": {"type": "boolean", "description": "点击选项后是否关闭菜单, default: true.\n\n[Docs](https://wot-design-uni.cn/component/action-sheet.html#attributes)"}, "wd-action-sheet/close-on-click-modal": {"type": "boolean", "description": "点击遮罩是否关闭, default: true.\n\n[Docs](https://wot-design-uni.cn/component/action-sheet.html#attributes)"}, "wd-action-sheet/duration": {"type": "number", "description": "动画持续时间, default: 200(ms).\n\n[Docs](https://wot-design-uni.cn/component/action-sheet.html#attributes)"}, "wd-action-sheet/z-index": {"type": "number", "description": "菜单层级, default: 10.\n\n[Docs](https://wot-design-uni.cn/component/action-sheet.html#attributes)"}, "wd-action-sheet/lazy-render": {"type": "boolean", "description": "弹层内容懒渲染，触发展示时才渲染内容, default: true.\n\n[Docs](https://wot-design-uni.cn/component/action-sheet.html#attributes)"}, "wd-action-sheet/safe-area-inset-bottom": {"type": "boolean", "description": "弹出面板是否设置底部安全距离（iphone X 类型的机型）, default: true.\n\n[Docs](https://wot-design-uni.cn/component/action-sheet.html#attributes)"}, "wd-action-sheet/select": {"type": "event", "description": "点击选项时触发\n\n[Docs](https://wot-design-uni.cn/component/action-sheet.html#events)"}, "wd-action-sheet/open": {"type": "event", "description": "弹出层打开时触发\n\n[Docs](https://wot-design-uni.cn/component/action-sheet.html#events)"}, "wd-action-sheet/opened": {"type": "event", "description": "弹出层打开动画结束时触发\n\n[Docs](https://wot-design-uni.cn/component/action-sheet.html#events)"}, "wd-action-sheet/close": {"type": "event", "description": "弹出层关闭时触发\n\n[Docs](https://wot-design-uni.cn/component/action-sheet.html#events)"}, "wd-action-sheet/closed": {"type": "event", "description": "弹出层关闭动画结束时触发\n\n[Docs](https://wot-design-uni.cn/component/action-sheet.html#events)"}, "wd-action-sheet/click-modal": {"type": "event", "description": "点击遮罩时触发\n\n[Docs](https://wot-design-uni.cn/component/action-sheet.html#events)"}, "wd-action-sheet/cancel": {"type": "event", "description": "点击取消按钮时触发\n\n[Docs](https://wot-design-uni.cn/component/action-sheet.html#events)"}, "wd-backtop/scroll-top": {"type": "number", "description": "页面滚动距离\n\n[Docs](https://wot-design-uni.cn/component/backtop.html#attributes)"}, "wd-backtop/top": {"type": "number", "description": "距离顶部多少距离时显示，单位`px`, default: 300.\n\n[Docs](https://wot-design-uni.cn/component/backtop.html#attributes)"}, "wd-backtop/duration": {"type": "number", "description": "返回顶部滚动时间，单位`ms`, default: 100.\n\n[Docs](https://wot-design-uni.cn/component/backtop.html#attributes)"}, "wd-backtop/z-index": {"type": "number", "description": "组件z-index属性, default: 10.\n\n[Docs](https://wot-design-uni.cn/component/backtop.html#attributes)"}, "wd-backtop/icon-style": {"type": "string", "description": "自定义`icon`样式\n\n[Docs](https://wot-design-uni.cn/component/backtop.html#attributes)"}, "wd-backtop/shape": {"type": "string", "options": ["square"], "description": "按钮形状, default: circle.\n\n[Docs](https://wot-design-uni.cn/component/backtop.html#attributes)"}, "wd-backtop/bottom": {"type": "number", "description": "距离屏幕底部的距离，单位`px`, default: 100.\n\n[Docs](https://wot-design-uni.cn/component/backtop.html#attributes)"}, "wd-backtop/right": {"type": "number", "description": "距离屏幕右边距离，单位`px`, default: 20.\n\n[Docs](https://wot-design-uni.cn/component/backtop.html#attributes)"}, "wd-badge/v-model": {"type": "string | number", "description": "显示值\n\n[Docs](https://wot-design-uni.cn/component/badge.html#attributes)"}, "wd-badge/max": {"type": "number", "description": "最大值，超过最大值会显示 '{max}+'，要求 value 是 Number 类型\n\n[Docs](https://wot-design-uni.cn/component/badge.html#attributes)"}, "wd-badge/top": {"type": "number", "description": "为正时，角标向下偏移对应的像素\n\n[Docs](https://wot-design-uni.cn/component/badge.html#attributes)"}, "wd-badge/right": {"type": "number", "description": "为正时，角标向左偏移对应的像素\n\n[Docs](https://wot-design-uni.cn/component/badge.html#attributes)"}, "wd-badge/is-dot": {"type": "boolean", "description": "红色点状标注, default: false.\n\n[Docs](https://wot-design-uni.cn/component/badge.html#attributes)"}, "wd-badge/hidden": {"type": "boolean", "description": "隐藏 badge, default: false.\n\n[Docs](https://wot-design-uni.cn/component/badge.html#attributes)"}, "wd-badge/type": {"type": "string", "options": ["primary", "success", "warning", "danger", "info"], "description": "类型\n\n[Docs](https://wot-design-uni.cn/component/badge.html#attributes)"}, "wd-badge/bg-color": {"type": "string", "options": ["各种颜色的css写法"], "description": "背景色\n\n[Docs](https://wot-design-uni.cn/component/badge.html#attributes)"}, "wd-badge/show-zero": {"type": "boolean", "description": "是否显示0, default: false.\n\n[Docs](https://wot-design-uni.cn/component/badge.html#attributes)"}, "wd-button/type": {"type": "string", "options": ["primary", "success", "info", "warning", "error", "text", "icon"], "description": "按钮类型, default: primary.\n\n[Docs](https://wot-design-uni.cn/component/button.html#attributes)"}, "wd-button/round": {"type": "boolean", "description": "圆角按钮, default: true.\n\n[Docs](https://wot-design-uni.cn/component/button.html#attributes)"}, "wd-button/plain": {"type": "boolean", "description": "幽灵按钮, default: false.\n\n[Docs](https://wot-design-uni.cn/component/button.html#attributes)"}, "wd-button/hairline": {"type": "boolean", "description": "是否细边框, default: false.\n\n[Docs](https://wot-design-uni.cn/component/button.html#attributes)"}, "wd-button/loading": {"type": "boolean", "description": "加载中按钮, default: false.\n\n[Docs](https://wot-design-uni.cn/component/button.html#attributes)"}, "wd-button/block": {"type": "boolean", "description": "块状按钮, default: false.\n\n[Docs](https://wot-design-uni.cn/component/button.html#attributes)"}, "wd-button/size": {"type": "string", "options": ["small", "medium", "large"], "description": "按钮尺寸, default: medium.\n\n[Docs](https://wot-design-uni.cn/component/button.html#attributes)"}, "wd-button/disabled": {"type": "boolean", "description": "禁用按钮, default: false.\n\n[Docs](https://wot-design-uni.cn/component/button.html#attributes)"}, "wd-button/icon": {"type": "string", "description": "图标类名\n\n[Docs](https://wot-design-uni.cn/component/button.html#attributes)"}, "wd-button/loading-color": {"type": "string", "description": "加载图标颜色\n\n[Docs](https://wot-design-uni.cn/component/button.html#attributes)"}, "wd-button/open-type": {"type": "string", "description": "微信开放能力\n\n[Docs](https://wot-design-uni.cn/component/button.html#attributes)"}, "wd-button/hover-stop-propagation": {"type": "boolean", "description": "指定是否阻止本节点的祖先节点出现点击态, default: false.\n\n[Docs](https://wot-design-uni.cn/component/button.html#attributes)"}, "wd-button/lang": {"type": "string", "options": ["zh_CN", "zh_TW"], "description": "指定返回用户信息的语言，zh_CN 简体中文，zh_TW 繁体中文，en 英文, default: en.\n\n[Docs](https://wot-design-uni.cn/component/button.html#attributes)"}, "wd-button/session-from": {"type": "string", "description": "会话来源，open-type=\"contact\"时有效\n\n[Docs](https://wot-design-uni.cn/component/button.html#attributes)"}, "wd-button/session-message-title": {"type": "string", "description": "会话内消息卡片标题，open-type=\"contact\"时有效, default: 当前标题.\n\n[Docs](https://wot-design-uni.cn/component/button.html#attributes)"}, "wd-button/session-message-path": {"type": "string", "description": "会话内消息卡片点击跳转小程序路径，open-type=\"contact\"时有效, default: 当前分享路径.\n\n[Docs](https://wot-design-uni.cn/component/button.html#attributes)"}, "wd-button/send-message-img": {"type": "string", "description": "会话内消息卡片图片，open-type=\"contact\"时有效, default: 截图.\n\n[Docs](https://wot-design-uni.cn/component/button.html#attributes)"}, "wd-button/app-parameter": {"type": "string", "description": "打开 APP 时，向 APP 传递的参数，open-type=launchApp 时有效\n\n[Docs](https://wot-design-uni.cn/component/button.html#attributes)"}, "wd-button/show-message-card": {"type": "boolean", "description": "是否显示会话内消息卡片，设置此参数为 true，用户进入客服会话会在右下角显示\"可能要发送的小程序\"提示，用户点击后可以快速发送小程序消息，open-type=\"contact\"时有效, default: false.\n\n[Docs](https://wot-design-uni.cn/component/button.html#attributes)"}, "wd-button/class-prefix": {"type": "string", "description": "类名前缀，用于使用自定义图标，参见[icon](/component/icon#自定义图标), default: wd-icon.\n\n[Docs](https://wot-design-uni.cn/component/button.html#attributes)"}, "wd-button/button-id": {"type": "string", "description": "按钮的唯一标识，可用于设置隐私同意授权按钮的 id\n\n[Docs](https://wot-design-uni.cn/component/button.html#attributes)"}, "wd-button/scope": {"type": "ButtonScope", "options": ["phoneNumber", "userInfo"], "description": "支付宝小程序使用，当 open-type 为 getAuthorize 时有效。\n\n[Docs](https://wot-design-uni.cn/component/button.html#attributes)"}, "wd-button/click": {"type": "event", "description": "点击事件\n\n[Docs](https://wot-design-uni.cn/component/button.html#events)"}, "wd-button/getuserinfo": {"type": "event", "description": "获取用户信息\n\n[Docs](https://wot-design-uni.cn/component/button.html#events)"}, "wd-button/contact": {"type": "event", "description": "客服消息回调，open-type=\"contact\"时有效\n\n[Docs](https://wot-design-uni.cn/component/button.html#events)"}, "wd-button/getphonenumber": {"type": "event", "description": "获取用户手机号回调，open-type=getPhoneNumber 时有效\n\n[Docs](https://wot-design-uni.cn/component/button.html#events)"}, "wd-button/error": {"type": "event", "description": "当使用开放能力时，发生错误的回调，open-type=launchApp 时有效\n\n[Docs](https://wot-design-uni.cn/component/button.html#events)"}, "wd-button/launchapp": {"type": "event", "description": "打开 APP 成功的回调，open-type=launchApp 时有效\n\n[Docs](https://wot-design-uni.cn/component/button.html#events)"}, "wd-button/opensetting": {"type": "event", "description": "在打开授权设置页后回调，open-type=openSetting 时有效\n\n[Docs](https://wot-design-uni.cn/component/button.html#events)"}, "wd-calendar-view/v-model": {"type": "null | number | array", "description": "选中值，为 13 位时间戳或时间戳数组\n\n[Docs](https://wot-design-uni.cn/component/calendar-view.html#attributes)"}, "wd-calendar-view/type": {"type": "string", "options": ["date", "dates", "datetime", "week", "month", "daterange", "datetimerange", "weekrange", "monthrange"], "description": "日期类型, default: date.\n\n[Docs](https://wot-design-uni.cn/component/calendar-view.html#attributes)"}, "wd-calendar-view/min-date": {"type": "number", "description": "最小日期，为 13 位时间戳, default: 当前日期往前推 6 个月.\n\n[Docs](https://wot-design-uni.cn/component/calendar-view.html#attributes)"}, "wd-calendar-view/max-date": {"type": "number", "description": "最大日期，为 13 位时间戳, default: 当前日期往后推 6 个月.\n\n[Docs](https://wot-design-uni.cn/component/calendar-view.html#attributes)"}, "wd-calendar-view/first-day-of-week": {"type": "number", "description": "周起始天, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/calendar-view.html#attributes)"}, "wd-calendar-view/formatter": {"type": "function", "description": "日期格式化函数\n\n[Docs](https://wot-design-uni.cn/component/calendar-view.html#attributes)"}, "wd-calendar-view/max-range": {"type": "number", "description": "type 为范围选择时有效，最大日期范围\n\n[Docs](https://wot-design-uni.cn/component/calendar-view.html#attributes)"}, "wd-calendar-view/range-prompt": {"type": "string", "description": "type 为范围选择时有效，选择超出最大日期范围时的错误提示文案, default: 选择天数不能超过 x 天.\n\n[Docs](https://wot-design-uni.cn/component/calendar-view.html#attributes)"}, "wd-calendar-view/allow-same-day": {"type": "boolean", "description": "type 为范围选择时有效，是否允许选择同一天, default: false.\n\n[Docs](https://wot-design-uni.cn/component/calendar-view.html#attributes)"}, "wd-calendar-view/show-panel-title": {"type": "boolean", "description": "是否展示面板标题，自动计算当前滚动的日期月份, default: true.\n\n[Docs](https://wot-design-uni.cn/component/calendar-view.html#attributes)"}, "wd-calendar-view/default-time": {"type": "string | array", "description": "选中日期所使用的当日内具体时刻, default: 00:00:00.\n\n[Docs](https://wot-design-uni.cn/component/calendar-view.html#attributes)"}, "wd-calendar-view/panel-height": {"type": "number", "description": "可滚动面板的高度, default: 378.\n\n[Docs](https://wot-design-uni.cn/component/calendar-view.html#attributes)"}, "wd-calendar-view/time-filter": {"type": "function", "description": "type 为 'datetime' 或 'datetimerange' 时有效，用于过滤时间选择器的数据\n\n[Docs](https://wot-design-uni.cn/component/calendar-view.html#attributes)"}, "wd-calendar-view/hide-second": {"type": "boolean", "description": "type 为 'datetime' 或 'datetimerange' 时有效，是否不展示秒修改, default: false.\n\n[Docs](https://wot-design-uni.cn/component/calendar-view.html#attributes)"}, "wd-calendar-view/immediate-change": {"type": "boolean", "description": "type 为 'datetime' 或 'datetimerange' 时有，是否在手指松开时立即触发 picker-view 的 change 事件。若不开启则会在滚动动画结束后触发 change 事件，1.2.25 版本起提供，仅微信小程序和支付宝小程序支持。, default: false.\n\n[Docs](https://wot-design-uni.cn/component/calendar-view.html#attributes)"}, "wd-calendar-view/change": {"type": "event", "description": "绑定值变化时触发\n\n[Docs](https://wot-design-uni.cn/component/calendar-view.html#events)"}, "wd-calendar/v-model": {"type": "null | number | array", "description": "选中值，为 13 位时间戳或时间戳数组\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/type": {"type": "string", "options": ["date", "dates", "datetime", "week", "month", "daterange", "datetimerange", "weekrange", "monthrange"], "description": "日期类型, default: date.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/min-date": {"type": "number", "description": "最小日期，为 13 位时间戳, default: 当前日期往前推 6 个月.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/max-date": {"type": "number", "description": "最大日期，为 13 位时间戳, default: 当前日期往后推 6 个月.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/first-day-of-week": {"type": "number", "description": "周起始天, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/formatter": {"type": "function", "description": "日期格式化函数\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/max-range": {"type": "number", "description": "type 为范围选择时有效，最大日期范围\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/range-prompt": {"type": "string", "description": "type 为范围选择时有效，选择超出最大日期范围时的错误提示文案, default: 选择天数不能超过 x 天.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/allow-same-day": {"type": "boolean", "description": "type 为范围选择时有效，是否允许选择同一天, default: false.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/default-time": {"type": "string | array", "description": "选中日期所使用的当日内具体时刻, default: 00:00:00.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/time-filter": {"type": "function", "description": "type 为 'datetime' 或 'datetimerange' 时有效，用于过滤时间选择器的数据\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/hide-second": {"type": "boolean", "description": "type 为 'datetime' 或 'datetimerange' 时有效，是否不展示秒修改, default: false.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/show-confirm": {"type": "boolean", "description": "是否显示确定按钮, default: true.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/show-type-switch": {"type": "boolean", "description": "是否显示类型切换功能, default: false.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/shortcuts": {"type": "array", "description": "快捷选项，为对象数组，其中对象的 `text` 必传\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/title": {"type": "string", "description": "弹出层标题, default: 选择日期.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/label": {"type": "string", "description": "选择器左侧文案\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/placeholder": {"type": "string", "description": "选择器占位符, default: 请选择.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/disabled": {"type": "boolean", "description": "禁用, default: false.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/readonly": {"type": "boolean", "description": "只读, default: false.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/display-format": {"type": "function", "description": "自定义展示文案的格式化函数，返回一个字符串\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/inner-display-format": {"type": "function", "description": "自定义范围选择类型的面板内部回显，返回一个字符串\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/size": {"type": "string", "options": ["large"], "description": "设置选择器大小\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/label-width": {"type": "string", "description": "设置左侧标题宽度, default: 33%.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/error": {"type": "boolean", "description": "是否为错误状态，错误状态时右侧内容为红色, default: false.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/required": {"type": "boolean", "description": "必填样式, default: false.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/center": {"type": "boolean", "description": "是否垂直居中, default: false.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/ellipsis": {"type": "boolean", "description": "是否超出隐藏, default: false.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/align-right": {"type": "boolean", "description": "选择器的值靠右展示, default: false.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/before-confirm": {"type": "function", "description": "确定前校验函数，接收 { value, resolve } 参数，通过 resolve 继续执行，resolve 接收 1 个 boolean 参数\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/suse-default-slots": {"type": "boolean", "description": "<s>使用默认插槽时设置该选项</s>，已废弃直接使用默认插槽即可。, default: false.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/suse-label-slots": {"type": "boolean", "description": "<s>使用 label 插槽时设置该选项</s>，已废弃直接使用 label 插槽即可。, default: false.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/close-on-click-modal": {"type": "boolean", "description": "点击遮罩是否关闭, default: true.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/z-index": {"type": "number", "description": "弹窗层级, default: 15.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/safe-area-inset-bottom": {"type": "boolean", "description": "弹出面板是否设置底部安全距离（iphone X 类型的机型）, default: true.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/prop": {"type": "string", "description": "表单域 `model` 字段名，在使用表单校验功能的情况下，该属性是必填的\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/rules": {"type": "`FormItemRule []`", "description": "表单验证规则，结合`wd-form`组件使用, default: [].\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/immediate-change": {"type": "boolean", "description": "type 为 'datetime' 或 'datetimerange' 时有，是否在手指松开时立即触发 picker-view 的 change 事件。若不开启则会在滚动动画结束后触发 change 事件，1.2.25 版本起提供，仅微信小程序和支付宝小程序支持。, default: false.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/with-cell": {"type": "boolean", "description": "是否使用内置 cell 选择器, default: true.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/clearable": {"type": "boolean", "description": "显示清空按钮, default: false.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/root-portal": {"type": "boolean", "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题, default: false.\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#attributes)"}, "wd-calendar/confirm": {"type": "event", "description": "绑定值变化时触发\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#events)"}, "wd-calendar/change": {"type": "event", "description": "点击面板日期时触发\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#events)"}, "wd-calendar/cancel": {"type": "event", "description": "点击关闭按钮或者蒙层时触发\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#events)"}, "wd-calendar/open": {"type": "event", "description": "日历打开时触发\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#events)"}, "wd-calendar/clear": {"type": "event", "description": "点击清空按钮时触发\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#events)"}, "wd-card/title": {"type": "string", "description": "卡片标题\n\n[Docs](https://wot-design-uni.cn/component/card.html#attributes)"}, "wd-card/type": {"type": "string", "options": ["rectangle"], "description": "卡片类型\n\n[Docs](https://wot-design-uni.cn/component/card.html#attributes)"}, "wd-cell-group/title": {"type": "string", "description": "分组标题\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cellgroup-attributes)"}, "wd-cell-group/value": {"type": "string", "description": "分组右侧内容\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cellgroup-attributes)"}, "wd-cell-group/border": {"type": "boolean", "description": "是否展示边框线\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cellgroup-attributes)"}, "wd-cell-group/use-slot": {"type": "boolean", "description": "分组启用插槽, default: false.\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cellgroup-attributes)"}, "wd-cell/title": {"type": "string", "description": "标题\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cell-attributes)"}, "wd-cell/value": {"type": "string", "description": "右侧内容\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cell-attributes)"}, "wd-cell/icon": {"type": "string", "description": "图标类名\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cell-attributes)"}, "wd-cell/label": {"type": "string", "description": "描述信息\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cell-attributes)"}, "wd-cell/is-link": {"type": "boolean", "description": "是否为跳转链接, default: false.\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cell-attributes)"}, "wd-cell/to": {"type": "string", "description": "跳转地址\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cell-attributes)"}, "wd-cell/clickable": {"type": "boolean", "description": "点击反馈，开启 is-link 时，默认开启此选项, default: false.\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cell-attributes)"}, "wd-cell/replace": {"type": "boolean", "description": "跳转时是否替换栈顶页面, default: false.\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cell-attributes)"}, "wd-cell/size": {"type": "string", "options": ["large"], "description": "设置单元格大小\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cell-attributes)"}, "wd-cell/title-width": {"type": "string", "description": "设置左侧标题宽度\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cell-attributes)"}, "wd-cell/center": {"type": "boolean", "description": "是否垂直居中，默认顶部居中, default: false.\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cell-attributes)"}, "wd-cell/required": {"type": "boolean", "description": "表单属性，必填, default: false.\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cell-attributes)"}, "wd-cell/vertical": {"type": "boolean", "description": "表单属性，上下结构, default: false.\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cell-attributes)"}, "wd-cell/ellipsis": {"type": "boolean", "description": "内容省略，右侧内容超出时会以省略号显示, default: false.\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cell-attributes)"}, "wd-cell/use-title-slot": {"type": "boolean", "description": "是否启用title插槽，默认启用，用来解决插槽传递时v-slot和v-if冲突问题, default: true.\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cell-attributes)"}, "wd-cell/prop": {"type": "string", "description": "表单域 `model` 字段名，在使用表单校验功能的情况下，该属性是必填的\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cell-attributes)"}, "wd-cell/rules": {"type": "`FormItemRule []`", "description": "表单验证规则，结合`wd-form`组件使用, default: [].\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cell-attributes)"}, "wd-cell/border": {"type": "boolean", "description": "是否展示边框线，优先级高于`cell-group`的`border`\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cell-attributes)"}, "wd-cell/click": {"type": "event", "description": "当 clickable 或 is-link 为 true 时点击单元格触发\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cell-events)"}, "wd-checkbox/v-model": {"type": "string | number | boolean", "description": "单选框选中时的值\n\n[Docs](https://wot-design-uni.cn/component/checkbox.html#checkbox-attributes)"}, "wd-checkbox/shape": {"type": "string", "options": ["circle", "square", "button"], "description": "单选框形状, default: circle.\n\n[Docs](https://wot-design-uni.cn/component/checkbox.html#checkbox-attributes)"}, "wd-checkbox/checked-color": {"type": "string", "description": "选中的颜色, default: #4D80F0.\n\n[Docs](https://wot-design-uni.cn/component/checkbox.html#checkbox-attributes)"}, "wd-checkbox/disabled": {"type": "boolean", "description": "禁用, default: false.\n\n[Docs](https://wot-design-uni.cn/component/checkbox.html#checkbox-attributes)"}, "wd-checkbox/max-width": {"type": "string", "description": "文字位置最大宽度\n\n[Docs](https://wot-design-uni.cn/component/checkbox.html#checkbox-attributes)"}, "wd-checkbox/true-value": {"type": "string | number", "description": "选中值，在 checkbox-group 中使用无效，需同 false-value 一块使用, default: true.\n\n[Docs](https://wot-design-uni.cn/component/checkbox.html#checkbox-attributes)"}, "wd-checkbox/false-value": {"type": "string |number", "description": "非选中时的值，在 checkbox-group 中使用无效，需同 true-value 一块使用, default: false.\n\n[Docs](https://wot-design-uni.cn/component/checkbox.html#checkbox-attributes)"}, "wd-checkbox/size": {"type": "string", "options": ["large"], "description": "设置大小\n\n[Docs](https://wot-design-uni.cn/component/checkbox.html#checkbox-attributes)"}, "wd-checkbox/change": {"type": "event", "description": "绑定值变化时触发，当为复选框组时参数为 boolean，表示该复选框是否选中\n\n[Docs](https://wot-design-uni.cn/component/checkbox.html#checkbox-events)"}, "wd-checkbox-group/v-model": {"type": "Array", "description": "绑定值\n\n[Docs](https://wot-design-uni.cn/component/checkbox.html#checkboxgroup-attributes)"}, "wd-checkbox-group/shape": {"type": "string", "options": ["circle", "square", "button"], "description": "单选框形状, default: circle.\n\n[Docs](https://wot-design-uni.cn/component/checkbox.html#checkboxgroup-attributes)"}, "wd-checkbox-group/cell": {"type": "boolean", "description": "表单模式, default: false.\n\n[Docs](https://wot-design-uni.cn/component/checkbox.html#checkboxgroup-attributes)"}, "wd-checkbox-group/checked-color": {"type": "string", "description": "选中的颜色, default: #4D80F0.\n\n[Docs](https://wot-design-uni.cn/component/checkbox.html#checkboxgroup-attributes)"}, "wd-checkbox-group/disabled": {"type": "boolean", "description": "禁用, default: false.\n\n[Docs](https://wot-design-uni.cn/component/checkbox.html#checkboxgroup-attributes)"}, "wd-checkbox-group/min": {"type": "number", "description": "最小选中的数量, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/checkbox.html#checkboxgroup-attributes)"}, "wd-checkbox-group/max": {"type": "number", "description": "最大选中的数量，0 为无限数量，默认为 0, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/checkbox.html#checkboxgroup-attributes)"}, "wd-checkbox-group/inline": {"type": "boolean", "description": "同行展示, default: false.\n\n[Docs](https://wot-design-uni.cn/component/checkbox.html#checkboxgroup-attributes)"}, "wd-checkbox-group/size": {"type": "string", "options": ["large"], "description": "设置大小\n\n[Docs](https://wot-design-uni.cn/component/checkbox.html#checkboxgroup-attributes)"}, "wd-checkbox-group/change": {"type": "event", "description": "绑定值变化时触发\n\n[Docs](https://wot-design-uni.cn/component/checkbox.html#checkboxgroup-events)"}, "wd-circle/v-model": {"type": "number", "description": "当前进度, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/circle.html#attributes)"}, "wd-circle/custom-class": {"type": "string", "description": "自定义class\n\n[Docs](https://wot-design-uni.cn/component/circle.html#attributes)"}, "wd-circle/custom-style": {"type": "string", "description": "自定义style\n\n[Docs](https://wot-design-uni.cn/component/circle.html#attributes)"}, "wd-circle/size": {"type": "number", "description": "圆环直径，默认单位为 px, default: 100.\n\n[Docs](https://wot-design-uni.cn/component/circle.html#attributes)"}, "wd-circle/color": {"type": "string | Record<string, string>", "description": "进度条颜色, default: #4d80f0.\n\n[Docs](https://wot-design-uni.cn/component/circle.html#attributes)"}, "wd-circle/layer-color": {"type": "string", "description": "轨道颜色, default: #EBEEF5.\n\n[Docs](https://wot-design-uni.cn/component/circle.html#attributes)"}, "wd-circle/fill": {"type": "string", "description": "填充颜色, default: #ffffff.\n\n[Docs](https://wot-design-uni.cn/component/circle.html#attributes)"}, "wd-circle/speed": {"type": "number", "description": "动画速度（单位为 rate/s）, default: 50.\n\n[Docs](https://wot-design-uni.cn/component/circle.html#attributes)"}, "wd-circle/text": {"type": "string", "description": "文字\n\n[Docs](https://wot-design-uni.cn/component/circle.html#attributes)"}, "wd-circle/stroke-width": {"type": "number", "description": "进度条宽度，单位px, default: 10.\n\n[Docs](https://wot-design-uni.cn/component/circle.html#attributes)"}, "wd-circle/stroke-linecap": {"type": "string", "options": ["butt", "round", "square"], "description": "进度条端点的形状, default: round.\n\n[Docs](https://wot-design-uni.cn/component/circle.html#attributes)"}, "wd-circle/clockwise": {"type": "boolean", "description": "是否顺时针增加, default: true.\n\n[Docs](https://wot-design-uni.cn/component/circle.html#attributes)"}, "wd-col-picker/v-model": {"type": "array", "description": "选中项\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/columns": {"type": "array", "description": "选择器数据，二维数组\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/value-key": {"type": "string", "description": "选项对象中，value 对应的 key, default: value.\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/label-key": {"type": "string", "description": "选项对象中，展示的文本对应的 key, default: label.\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/tip-key": {"type": "string", "description": "选项对象中，提示文案对应的 key, default: tip.\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/title": {"type": "string", "description": "弹出层标题\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/label": {"type": "string", "description": "选择器左侧文案\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/placeholder": {"type": "string", "description": "选择器占位符, default: 请选择.\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/disabled": {"type": "boolean", "description": "禁用, default: false.\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/readonly": {"type": "boolean", "description": "只读, default: false.\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/display-format": {"type": "function", "description": "自定义展示文案的格式化函数，返回一个字符串\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/column-change": {"type": "function", "description": "接收当前列的选中项 item、当前列下标、当前列选中项下标下一列数据处理函数 resolve、结束选择 finish\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/size": {"type": "string", "options": ["large"], "description": "设置选择器大小\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/label-width": {"type": "string", "description": "设置左侧标题宽度, default: 33%.\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/error": {"type": "boolean", "description": "是否为错误状态，错误状态时右侧内容为红色, default: false.\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/required": {"type": "boolean", "description": "必填样式, default: false.\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/align-right": {"type": "boolean", "description": "选择器的值靠右展示, default: false.\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/before-confirm": {"type": "function", "description": "确定前校验函数，接收 (value, resolve) 参数，通过 resolve 继续执行 picker，resolve 接收 1 个 boolean 参数\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/loading-color": {"type": "string", "description": "loading 图标的颜色, default: #4D80F0.\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/use-default-slot": {"type": "boolean", "description": "使用默认插槽时设置该选项, default: false.\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/use-label-slot": {"type": "boolean", "description": "使用 label 插槽时设置该选项, default: false.\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/close-on-click-modal": {"type": "boolean", "description": "点击遮罩是否关闭, default: true.\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/auto-complete": {"type": "-", "options": ["false"], "description": "自动触发 column-change 事件来补全数据，当 columns 为空数组或者 columns 数组长度小于 value 数组长度时，会自动触发 column-change\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/z-index": {"type": "number", "description": "弹窗层级, default: 15.\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/safe-area-inset-bottom": {"type": "boolean", "description": "弹出面板是否设置底部安全距离（iphone X 类型的机型）, default: true.\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/ellipsis": {"type": "boolean", "description": "是否超出隐藏, default: false.\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/prop": {"type": "string", "description": "表单域 `model` 字段名，在使用表单校验功能的情况下，该属性是必填的\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/rules": {"type": "`FormItemRule []`", "description": "表单验证规则，结合`wd-form`组件使用, default: [].\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/line-width": {"type": "number", "description": "底部条宽度，单位像素\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/line-height": {"type": "number", "description": "底部条高度，单位像素\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/root-portal": {"type": "boolean", "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题, default: false.\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#attributes)"}, "wd-col-picker/confirm": {"type": "event", "description": "最后一列选项选中时触发\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#events)"}, "wd-col-picker/close": {"type": "event", "description": "点击关闭按钮或者蒙层时触发\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#events)"}, "wd-collapse-item/name": {"type": "string", "description": "折叠栏的标识符\n\n[Docs](https://wot-design-uni.cn/component/collapse.html#collapseitem-attributes)"}, "wd-collapse-item/title": {"type": "string", "description": "折叠栏的标题, 支持同名 slot 自定义内容\n\n[Docs](https://wot-design-uni.cn/component/collapse.html#collapseitem-attributes)"}, "wd-collapse-item/disabled": {"type": "boolean", "description": "禁用折叠栏, default: false.\n\n[Docs](https://wot-design-uni.cn/component/collapse.html#collapseitem-attributes)"}, "wd-collapse-item/before-expend": {"type": "Function", "description": "打开前的回调函数，返回 false 可以阻止打开，支持返回 Promise\n\n[Docs](https://wot-design-uni.cn/component/collapse.html#collapseitem-attributes)"}, "wd-collapse/value": {"type": "string | array | boolean", "description": "绑定值\n\n[Docs](https://wot-design-uni.cn/component/collapse.html#collapse-attributes)"}, "wd-collapse/accordion": {"type": "boolean", "description": "手风琴, default: false.\n\n[Docs](https://wot-design-uni.cn/component/collapse.html#collapse-attributes)"}, "wd-collapse/viewmore": {"type": "boolean", "description": "查看更多的折叠面板, default: false.\n\n[Docs](https://wot-design-uni.cn/component/collapse.html#collapse-attributes)"}, "wd-collapse/use-more-slot": {"type": "boolean", "description": "查看更多的自定义插槽使用标志, default: false.\n\n[Docs](https://wot-design-uni.cn/component/collapse.html#collapse-attributes)"}, "wd-collapse/line-num": {"type": "number", "description": "查看更多的折叠面板，收起时的显示行数, default: 2.\n\n[Docs](https://wot-design-uni.cn/component/collapse.html#collapse-attributes)"}, "wd-collapse/change": {"type": "event", "description": "绑定值变化时触发\n\n[Docs](https://wot-design-uni.cn/component/collapse.html#collapse-events)"}, "wd-config-provider/theme": {"type": "string", "options": ["dark", "light"], "description": "主题风格，设置为 `dark` 来开启深色模式，全局生效\n\n[Docs](https://wot-design-uni.cn/component/config-provider.html#attributes)"}, "wd-config-provider/theme-vars": {"type": "`ConfigProviderThemeVars`", "description": "自定义主题变量\n\n[Docs](https://wot-design-uni.cn/component/config-provider.html#attributes)"}, "wd-count-down/time": {"type": "Number", "description": "倒计时时长，单位毫秒, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/count-down.html#attributes)"}, "wd-count-down/millisecond": {"type": "Boolean", "description": "是否开启毫秒级渲染, default: false.\n\n[Docs](https://wot-design-uni.cn/component/count-down.html#attributes)"}, "wd-count-down/auto-start": {"type": "Boolean", "description": "是否自动开始倒计时, default: true.\n\n[Docs](https://wot-design-uni.cn/component/count-down.html#attributes)"}, "wd-count-down/format": {"type": "String", "description": "倒计时格式化字符串, default: HH:mm:ss.\n\n[Docs](https://wot-design-uni.cn/component/count-down.html#attributes)"}, "wd-count-down/finish": {"type": "event", "description": "倒计时结束时触发\n\n[Docs](https://wot-design-uni.cn/component/count-down.html#events)"}, "wd-count-down/change": {"type": "event", "description": "倒计时变化时触发\n\n[Docs](https://wot-design-uni.cn/component/count-down.html#events)"}, "wd-count-to/font-size": {"type": "number", "options": ["16"], "description": "字体大小, default: default.\n\n[Docs](https://wot-design-uni.cn/component/count-to.html#attributes)"}, "wd-count-to/color": {"type": "string", "description": "文本颜色\n\n[Docs](https://wot-design-uni.cn/component/count-to.html#attributes)"}, "wd-count-to/type": {"type": "string", "options": ["primary'", "'error'", "'warning'", "'success"], "description": "主题类型, default: default.\n\n[Docs](https://wot-design-uni.cn/component/count-to.html#attributes)"}, "wd-count-to/start-val": {"type": "number", "description": "起始值, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/count-to.html#attributes)"}, "wd-count-to/end-val": {"type": "number", "description": "最终值, default: 2024.\n\n[Docs](https://wot-design-uni.cn/component/count-to.html#attributes)"}, "wd-count-to/duration": {"type": "number", "description": "从起始值到结束值数字变动的时间, default: 3000.\n\n[Docs](https://wot-design-uni.cn/component/count-to.html#attributes)"}, "wd-count-to/autoplay": {"type": "boolean", "description": "是否自动播放, default: true.\n\n[Docs](https://wot-design-uni.cn/component/count-to.html#attributes)"}, "wd-count-to/decimals": {"type": "number", "options": ["(需大于等于 0)"], "description": "保留的小数位数, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/count-to.html#attributes)"}, "wd-count-to/decimal": {"type": "string", "description": "小数点符号, default: ..\n\n[Docs](https://wot-design-uni.cn/component/count-to.html#attributes)"}, "wd-count-to/separator": {"type": "string", "description": "三位三位的隔开效果, default: ,.\n\n[Docs](https://wot-design-uni.cn/component/count-to.html#attributes)"}, "wd-count-to/prefix": {"type": "string", "description": "前缀\n\n[Docs](https://wot-design-uni.cn/component/count-to.html#attributes)"}, "wd-count-to/suffix": {"type": "string", "description": "后缀\n\n[Docs](https://wot-design-uni.cn/component/count-to.html#attributes)"}, "wd-count-to/use-easing": {"type": "boolean", "description": "是否具有连贯性, default: true.\n\n[Docs](https://wot-design-uni.cn/component/count-to.html#attributes)"}, "wd-count-to/finish": {"type": "event", "description": "动画完成时触发\n\n[Docs](https://wot-design-uni.cn/component/count-to.html#events)"}, "wd-count-to/mounted": {"type": "event", "description": "组件加载完成时时触发\n\n[Docs](https://wot-design-uni.cn/component/count-to.html#events)"}, "wd-curtain/value": {"type": "boolean", "description": "绑定值，展示/关闭幕帘（已废弃，请使用 modelValue）\n\n[Docs](https://wot-design-uni.cn/component/curtain.html#attributes)"}, "wd-curtain/model-value": {"type": "boolean", "description": "绑定值，展示/关闭幕帘\n\n[Docs](https://wot-design-uni.cn/component/curtain.html#attributes)"}, "wd-curtain/src": {"type": "string", "description": "幕帘图片地址，必须使用网络地址\n\n[Docs](https://wot-design-uni.cn/component/curtain.html#attributes)"}, "wd-curtain/width": {"type": "number", "description": "幕帘图片宽度，默认单位 px\n\n[Docs](https://wot-design-uni.cn/component/curtain.html#attributes)"}, "wd-curtain/to": {"type": "string", "description": "幕帘图片点击链接\n\n[Docs](https://wot-design-uni.cn/component/curtain.html#attributes)"}, "wd-curtain/close-position": {"type": "string", "options": ["inset", "top", "bottom", "top-left", "top-right", "bottom-left", "bottom-right"], "description": "关闭按钮位置, default: inset.\n\n[Docs](https://wot-design-uni.cn/component/curtain.html#attributes)"}, "wd-curtain/close-on-click-modal": {"type": "boolean", "description": "点击遮罩是否关闭, default: false.\n\n[Docs](https://wot-design-uni.cn/component/curtain.html#attributes)"}, "wd-curtain/hide-when-close": {"type": "boolean", "description": "是否当关闭时将弹出层隐藏（display: none）, default: true.\n\n[Docs](https://wot-design-uni.cn/component/curtain.html#attributes)"}, "wd-curtain/z-index": {"type": "number", "description": "设置层级, default: 10.\n\n[Docs](https://wot-design-uni.cn/component/curtain.html#attributes)"}, "wd-curtain/root-portal": {"type": "boolean", "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题, default: false.\n\n[Docs](https://wot-design-uni.cn/component/curtain.html#attributes)"}, "wd-curtain/click": {"type": "event", "description": "点击幕帘时触发\n\n[Docs](https://wot-design-uni.cn/component/curtain.html#events)"}, "wd-curtain/close": {"type": "event", "description": "弹出层关闭时触发\n\n[Docs](https://wot-design-uni.cn/component/curtain.html#events)"}, "wd-curtain/click-modal": {"type": "event", "description": "点击遮罩时触发\n\n[Docs](https://wot-design-uni.cn/component/curtain.html#events)"}, "wd-curtain/beforeenter": {"type": "event", "description": "进入前触发\n\n[Docs](https://wot-design-uni.cn/component/curtain.html#events)"}, "wd-curtain/enter": {"type": "event", "description": "进入时触发\n\n[Docs](https://wot-design-uni.cn/component/curtain.html#events)"}, "wd-curtain/afterenter": {"type": "event", "description": "进入后触发\n\n[Docs](https://wot-design-uni.cn/component/curtain.html#events)"}, "wd-curtain/beforeleave": {"type": "event", "description": "离开前触发\n\n[Docs](https://wot-design-uni.cn/component/curtain.html#events)"}, "wd-curtain/leave": {"type": "event", "description": "离开时触发\n\n[Docs](https://wot-design-uni.cn/component/curtain.html#events)"}, "wd-curtain/afterleave": {"type": "event", "description": "离开后触发\n\n[Docs](https://wot-design-uni.cn/component/curtain.html#events)"}, "wd-curtain/load": {"type": "event", "description": "图片加载完成事件\n\n[Docs](https://wot-design-uni.cn/component/curtain.html#events)"}, "wd-curtain/error": {"type": "event", "description": "图片加载失败事件，若图片加载失败，则不会展示幕帘组件，即使设置 `value` 为 true\n\n[Docs](https://wot-design-uni.cn/component/curtain.html#events)"}, "wd-datetime-picker-view/v-model": {"type": "`string` | `timestamp`", "description": "选中项，当 type 为 time 时，类型为字符串，否则为 `timestamp`\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker-view.html#attributes)"}, "wd-datetime-picker-view/type": {"type": "string", "options": ["date", "year-month", "time", "year"], "description": "选择器类型, default: datetime.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker-view.html#attributes)"}, "wd-datetime-picker-view/loading": {"type": "boolean", "description": "加载中, default: false.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker-view.html#attributes)"}, "wd-datetime-picker-view/loading-color": {"type": "string", "description": "加载的颜色，只能使用十六进制的色值写法，且不能使用缩写, default: #4D80F0.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker-view.html#attributes)"}, "wd-datetime-picker-view/columns-height": {"type": "number", "description": "picker内部滚筒高, default: 231.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker-view.html#attributes)"}, "wd-datetime-picker-view/formatter": {"type": "function", "description": "自定义弹出层选项文案的格式化函数，返回一个字符串\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker-view.html#attributes)"}, "wd-datetime-picker-view/filter": {"type": "function", "description": "自定义过滤选项的函数，返回列的选项数组\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker-view.html#attributes)"}, "wd-datetime-picker-view/min-date": {"type": "`timestamp`", "description": "最小日期，13 位的时间戳, default: 当前日期 - 10年.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker-view.html#attributes)"}, "wd-datetime-picker-view/max-date": {"type": "`timestamp`", "description": "最大日期，13 位的时间戳, default: 当前日期 + 10年.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker-view.html#attributes)"}, "wd-datetime-picker-view/min-hour": {"type": "number", "description": "最小小时，time类型时生效, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker-view.html#attributes)"}, "wd-datetime-picker-view/max-hour": {"type": "number", "description": "最大小时，time类型时生效, default: 23.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker-view.html#attributes)"}, "wd-datetime-picker-view/min-minute": {"type": "number", "description": "最小分钟，time类型时生效, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker-view.html#attributes)"}, "wd-datetime-picker-view/max-minute": {"type": "number", "description": "最大分钟，time类型时生效, default: 59.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker-view.html#attributes)"}, "wd-datetime-picker-view/immediate-change": {"type": "boolean", "description": "是否在手指松开时立即触发picker-view的 change 事件。若不开启则会在滚动动画结束后触发 change 事件，1.2.25版本起提供，仅微信小程序和支付宝小程序支持。, default: false.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker-view.html#attributes)"}, "wd-datetime-picker-view/use-second": {"type": "boolean", "description": "是否显示秒选择，仅在 time 和 datetime 类型下生效, default: false.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker-view.html#attributes)"}, "wd-datetime-picker-view/change": {"type": "event", "description": "切换选项时触发\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker-view.html#events)"}, "wd-datetime-picker-view/pickstart": {"type": "event", "description": "当滚动选择开始时候触发事件\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker-view.html#events)"}, "wd-datetime-picker-view/pickend": {"type": "event", "description": "当滚动选择结束时候触发事件\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker-view.html#events)"}, "wd-datetime-picker/v-model": {"type": "`string` | `timestamp` | `array`", "description": "选中项，当 type 为 time 时，类型为字符串；当 type 为 Array 时，类型为范围选择；否则为 `timestamp`\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/default-value": {"type": "`string` | `timestamp` | `array`", "description": "默认日期，类型保持与 value 一致，打开面板时面板自动选到默认日期\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/type": {"type": "string", "options": ["date", "year-month", "time", "year"], "description": "选择器类型, default: datetime.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/loading": {"type": "boolean", "description": "加载中, default: false.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/loading-color": {"type": "string", "description": "加载的颜色，只能使用十六进制的色值写法，且不能使用缩写, default: #4D80F0.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/columns-height": {"type": "number", "description": "picker内部滚筒高, default: 231.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/title": {"type": "string", "description": "弹出层标题\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/cancel-button-text": {"type": "string", "description": "取消按钮文案, default: 取消.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/confirm-button-text": {"type": "string", "description": "确认按钮文案, default: 完成.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/label": {"type": "string", "description": "选择器左侧文案，label可以不传\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/placeholder": {"type": "string", "description": "选择器占位符, default: 请选择.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/disabled": {"type": "boolean", "description": "禁用, default: false.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/readonly": {"type": "boolean", "description": "只读, default: false.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/display-format": {"type": "function", "description": "自定义展示文案的格式化函数，返回一个字符串\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/formatter": {"type": "function", "description": "自定义弹出层选项文案的格式化函数，返回一个字符串\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/filter": {"type": "function", "description": "自定义过滤选项的函数，返回列的选项数组\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/display-format-tab-label": {"type": "function", "description": "在区域选择模式下，自定义展示tab标签文案的格式化函数，返回一个字符串\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/min-date": {"type": "`timestamp`", "description": "最小日期，13 位的时间戳, default: 当前日期 - 10年.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/max-date": {"type": "`timestamp`", "description": "最大日期，13 位的时间戳, default: 当前日期 + 10年.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/min-hour": {"type": "number", "description": "最小小时，time类型时生效, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/max-hour": {"type": "number", "description": "最大小时，time类型时生效, default: 23.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/min-minute": {"type": "number", "description": "最小分钟，time类型时生效, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/max-minute": {"type": "number", "description": "最大分钟，time类型时生效, default: 59.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/required": {"type": "boolean", "description": "表单属性，必填, default: false.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/size": {"type": "string", "options": ["large"], "description": "设置选择器大小\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/label-width": {"type": "string", "description": "设置左侧标题宽度, default: 33%.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/error": {"type": "boolean", "description": "是否为错误状态，错误状态时右侧内容为红色, default: false.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/align-right": {"type": "boolean", "description": "选择器的值靠右展示, default: false.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/suse-label-slots": {"type": "boolean", "description": "<s>label 使用插槽</s>，已废弃，直接使用label插槽即可, default: false.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/suse-default-slots": {"type": "boolean", "description": "<s>使用默认插槽</s>，已废弃，直接使用默认插槽即可, default: false.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/before-confirm": {"type": "function", "description": "确定前校验函数，接收 (value, resolve, picker) 参数，通过 resolve 继续执行 picker，resolve 接收1个boolean参数\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/close-on-click-modal": {"type": "boolean", "description": "点击遮罩是否关闭, default: true.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/z-index": {"type": "number", "description": "弹窗层级, default: 15.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/safe-area-inset-bottom": {"type": "boolean", "description": "弹出面板是否设置底部安全距离（iphone X 类型的机型）, default: true.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/ellipsis": {"type": "boolean", "description": "是否超出隐藏, default: false.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/prop": {"type": "string", "description": "表单域 `model` 字段名，在使用表单校验功能的情况下，该属性是必填的\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/rules": {"type": "`FormItemRule []`", "description": "表单验证规则，结合`wd-form`组件使用, default: [].\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/immediate-change": {"type": "boolean", "description": "是否在手指松开时立即触发picker-view的 change 事件。若不开启则会在滚动动画结束后触发 change 事件，1.2.25版本起提供，仅微信小程序和支付宝小程序支持。, default: false.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/use-second": {"type": "boolean", "description": "是否显示秒选择，仅在 time 和 datetime 类型下生效, default: false.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/clearable": {"type": "boolean", "description": "显示清空按钮, default: false.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/root-portal": {"type": "boolean", "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题, default: false.\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#attributes)"}, "wd-datetime-picker/confirm": {"type": "event", "description": "点击右侧按钮触发\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#events)"}, "wd-datetime-picker/cancel": {"type": "event", "description": "点击左侧按钮触发\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#events)"}, "wd-datetime-picker/toggle": {"type": "event", "description": "在区域选择模式下，tab标签切换时触发\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#events)"}, "wd-datetime-picker/clear": {"type": "event", "description": "点击清空按钮触发\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#events)"}, "wd-divider/color": {"type": "string", "description": "自定义颜色，支持所有颜色的写法\n\n[Docs](https://wot-design-uni.cn/component/divider.html#attributes)"}, "wd-divider/hairline": {"type": "boolean", "description": "是否显示边框, default: true.\n\n[Docs](https://wot-design-uni.cn/component/divider.html#attributes)"}, "wd-divider/dashed": {"type": "boolean", "description": "是否显示为虚线, default: false.\n\n[Docs](https://wot-design-uni.cn/component/divider.html#attributes)"}, "wd-divider/content-position": {"type": "string", "options": ["left", "center", "right"], "description": "内容位置, default: center.\n\n[Docs](https://wot-design-uni.cn/component/divider.html#attributes)"}, "wd-divider/vertical": {"type": "boolean", "description": "是否显示为垂直分割线, default: false.\n\n[Docs](https://wot-design-uni.cn/component/divider.html#attributes)"}, "wd-drop-menu/direction": {"type": "string", "options": ["up", "down"], "description": "菜单展开方向，可选值为`up` 或 `down`, default: down.\n\n[Docs](https://wot-design-uni.cn/component/drop-menu.html#dropmenu-attributes)"}, "wd-drop-menu/modal": {"type": "boolean", "description": "是否展示蒙层, default: true.\n\n[Docs](https://wot-design-uni.cn/component/drop-menu.html#dropmenu-attributes)"}, "wd-drop-menu/close-on-click-modal": {"type": "boolean", "description": "是否点击蒙层时关闭, default: true.\n\n[Docs](https://wot-design-uni.cn/component/drop-menu.html#dropmenu-attributes)"}, "wd-drop-menu/duration": {"type": "number", "description": "菜单展开收起动画时间，单位 ms, default: 200.\n\n[Docs](https://wot-design-uni.cn/component/drop-menu.html#dropmenu-attributes)"}, "wd-drop-menu-item/v-model": {"type": "string | number", "description": "当前选中项对应选中的 value\n\n[Docs](https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes)"}, "wd-drop-menu-item/disabled": {"type": "boolean", "description": "禁用菜单, default: false.\n\n[Docs](https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes)"}, "wd-drop-menu-item/options": {"type": "array", "description": "列表数据，对应数据结构 `[{text: '标题', value: '0', tip: '提示文字'}]`\n\n[Docs](https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes)"}, "wd-drop-menu-item/icon-name": {"type": "string", "description": "选中的图标名称(可选名称在 wd-icon 组件中), default: check.\n\n[Docs](https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes)"}, "wd-drop-menu-item/title": {"type": "string", "description": "菜单标题\n\n[Docs](https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes)"}, "wd-drop-menu-item/icon": {"type": "string", "description": "菜单图标, default: arrow-down.\n\n[Docs](https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes)"}, "wd-drop-menu-item/icon-size": {"type": "string", "description": "菜单图标尺寸, default: 14px.\n\n[Docs](https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes)"}, "wd-drop-menu-item/before-toggle": {"type": "function({ status, resolve })", "description": "下拉菜单打开或者关闭前触发，`reslove(true)`时继续执行打开或关闭操作\n\n[Docs](https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes)"}, "wd-drop-menu-item/value-key": {"type": "string", "description": "选项对象中，value 对应的 key, default: value.\n\n[Docs](https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes)"}, "wd-drop-menu-item/label-key": {"type": "string", "description": "选项对象中，展示的文本对应的 key, default: label.\n\n[Docs](https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes)"}, "wd-drop-menu-item/tip-key": {"type": "string", "description": "选项对象中，选项说明对应的 key, default: tip.\n\n[Docs](https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes)"}, "wd-drop-menu-item/root-portal": {"type": "boolean", "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题, default: false.\n\n[Docs](https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes)"}, "wd-fab/v-model:active": {"type": "boolean", "description": "是否激活, default: false.\n\n[Docs](https://wot-design-uni.cn/component/fab.html#attributes)"}, "wd-fab/type": {"type": "FabType", "options": ["primary' &#124; 'success' &#124; 'info' &#124; 'warning' &#124; 'error' &#124; 'default"], "description": "类型, default: primary.\n\n[Docs](https://wot-design-uni.cn/component/fab.html#attributes)"}, "wd-fab/position": {"type": "FabPosition", "options": ["left-top' &#124; 'right-top' &#124; 'left-bottom' &#124; 'right-bottom' &#124; left-center &#124; right-center &#124; top-center &#124; bottom-center"], "description": "悬浮按钮位置, default: right-bottom.\n\n[Docs](https://wot-design-uni.cn/component/fab.html#attributes)"}, "wd-fab/draggable": {"type": "boolean", "description": "按钮能否拖动, default: false.\n\n[Docs](https://wot-design-uni.cn/component/fab.html#attributes)"}, "wd-fab/direction": {"type": "FabDirection", "options": ["top' &#124; 'right' &#124; 'bottom' &#124; 'left"], "description": "悬浮按钮菜单弹出方向, default: top.\n\n[Docs](https://wot-design-uni.cn/component/fab.html#attributes)"}, "wd-fab/disabled": {"type": "boolean", "description": "是否禁用, default: false.\n\n[Docs](https://wot-design-uni.cn/component/fab.html#attributes)"}, "wd-fab/inactive-icon": {"type": "string", "description": "悬浮按钮未展开时的图标, default: add.\n\n[Docs](https://wot-design-uni.cn/component/fab.html#attributes)"}, "wd-fab/active-icon": {"type": "string", "description": "悬浮按钮展开时的图标, default: close.\n\n[Docs](https://wot-design-uni.cn/component/fab.html#attributes)"}, "wd-fab/z-index": {"type": "number", "description": "自定义悬浮按钮层级, default: 99.\n\n[Docs](https://wot-design-uni.cn/component/fab.html#attributes)"}, "wd-fab/gap": {"type": "FabGap", "description": "自定义悬浮按钮与可视区域边缘的间距, default: \\{ top: 16, left: 16, right: 16, bottom: 16 \\}.\n\n[Docs](https://wot-design-uni.cn/component/fab.html#attributes)"}, "wd-fab/custom-style": {"type": "string", "description": "自定义样式\n\n[Docs](https://wot-design-uni.cn/component/fab.html#attributes)"}, "wd-fab/expandable": {"type": "boolean", "description": "用于控制点击时是否展开菜单，设置为 false 时触发 click, default: true.\n\n[Docs](https://wot-design-uni.cn/component/fab.html#attributes)"}, "wd-fab/click": {"type": "event", "description": "expandable 设置为 false 时，点击悬浮按钮触发\n\n[Docs](https://wot-design-uni.cn/component/fab.html#events)"}, "wd-floating-panel/v-model:height": {"type": "number", "description": "当前面板的显示高度, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/floating-panel.html#attributes)"}, "wd-floating-panel/anchors": {"type": "number[]", "description": "设置自定义锚点, 单位 `px`, default: [100, windowHeight  0.6].\n\n[Docs](https://wot-design-uni.cn/component/floating-panel.html#attributes)"}, "wd-floating-panel/duration": {"type": "number", "description": "动画时长，单位`ms`，设置为 `0` 可以禁用动画, default: 300.\n\n[Docs](https://wot-design-uni.cn/component/floating-panel.html#attributes)"}, "wd-floating-panel/content-draggable": {"type": "boolean", "description": "允许拖拽内容容器, default: true.\n\n[Docs](https://wot-design-uni.cn/component/floating-panel.html#attributes)"}, "wd-floating-panel/safe-area-inset-bottom": {"type": "boolean", "description": "是否开启底部安全区适配, default: false.\n\n[Docs](https://wot-design-uni.cn/component/floating-panel.html#attributes)"}, "wd-floating-panel/show-scrollbar": {"type": "boolean", "description": "是否开启滚动条, default: true.\n\n[Docs](https://wot-design-uni.cn/component/floating-panel.html#attributes)"}, "wd-form/model": {"type": "`Record<string, any>`", "description": "表单数据对象\n\n[Docs](https://wot-design-uni.cn/component/form.html#attributes)"}, "wd-form/rules": {"type": "`FormRules`", "description": "表单验证规则\n\n[Docs](https://wot-design-uni.cn/component/form.html#attributes)"}, "wd-form/reset-on-change": {"type": "`boolean`", "description": "表单数据变化时是否重置表单提示信息（设置为 false 时需要开发者单独对变更项进行校验）, default: true.\n\n[Docs](https://wot-design-uni.cn/component/form.html#attributes)"}, "wd-form/error-type": {"type": "`toast|message|none`", "description": "校验错误提示方式, default: message.\n\n[Docs](https://wot-design-uni.cn/component/form.html#attributes)"}, "wd-form/validate": {"type": "event", "description": "验证表单，支持传入一个 prop 来验证单个表单项，不传入 prop 时，会验证所有表单项，1.6.0 版本起支持传入数组\n\n[Docs](https://wot-design-uni.cn/component/form.html#events)"}, "wd-form/reset": {"type": "event", "description": "重置校验结果\n\n[Docs](https://wot-design-uni.cn/component/form.html#events)"}, "wd-gap/height": {"type": "`string`|`number`", "description": "高度, default: 15.\n\n[Docs](https://wot-design-uni.cn/component/gap.html#attributes)"}, "wd-gap/bg-color": {"type": "string", "description": "背景颜色, default: transparent.\n\n[Docs](https://wot-design-uni.cn/component/gap.html#attributes)"}, "wd-gap/safe-area-bottom": {"type": "boolean", "options": ["true", "false"], "description": "开启底部安全区, default: false.\n\n[Docs](https://wot-design-uni.cn/component/gap.html#attributes)"}, "wd-grid/column": {"type": "number", "description": "列数\n\n[Docs](https://wot-design-uni.cn/component/grid.html#grid-attributes)"}, "wd-grid/border": {"type": "boolean", "description": "是否显示边框, default: false.\n\n[Docs](https://wot-design-uni.cn/component/grid.html#grid-attributes)"}, "wd-grid/gutter": {"type": "number", "description": "格子之间的间距，默认单位为`px`\n\n[Docs](https://wot-design-uni.cn/component/grid.html#grid-attributes)"}, "wd-grid/square": {"type": "boolean", "description": "是否将格子固定为正方形, default: false.\n\n[Docs](https://wot-design-uni.cn/component/grid.html#grid-attributes)"}, "wd-grid/clickable": {"type": "boolean", "description": "是否开启格子点击反馈, default: false.\n\n[Docs](https://wot-design-uni.cn/component/grid.html#grid-attributes)"}, "wd-grid/bg-color": {"type": "string", "description": "背景颜色设置, default: #ffffff.\n\n[Docs](https://wot-design-uni.cn/component/grid.html#grid-attributes)"}, "wd-grid/hover-class": {"type": "string", "description": "指定grid-item按下去的样式类, default: wd-grid-item__content--hover.\n\n[Docs](https://wot-design-uni.cn/component/grid.html#grid-attributes)"}, "wd-grid-item/text": {"type": "string", "description": "文字 value\n\n[Docs](https://wot-design-uni.cn/component/grid.html#griditem-attributes)"}, "wd-grid-item/icon": {"type": "string", "description": "图标名称，可选值见 `wd-icon` 组件\n\n[Docs](https://wot-design-uni.cn/component/grid.html#griditem-attributes)"}, "wd-grid-item/is-dot": {"type": "boolean", "description": "是否显示图标右上角小红点, default: false.\n\n[Docs](https://wot-design-uni.cn/component/grid.html#griditem-attributes)"}, "wd-grid-item/type": {"type": "string", "options": ["primary", "success", "warning", "danger", "info"], "description": "图标右上角显示的 `badge` 类型\n\n[Docs](https://wot-design-uni.cn/component/grid.html#griditem-attributes)"}, "wd-grid-item/value": {"type": "string, number", "description": "图标右上角 `badge` 显示值\n\n[Docs](https://wot-design-uni.cn/component/grid.html#griditem-attributes)"}, "wd-grid-item/max": {"type": "number", "description": "图标右上角 `badge` 最大值，超过最大值会显示 '{max}+'，要求 value 是 Number 类型\n\n[Docs](https://wot-design-uni.cn/component/grid.html#griditem-attributes)"}, "wd-grid-item/url": {"type": "string", "description": "点击后跳转的链接地址\n\n[Docs](https://wot-design-uni.cn/component/grid.html#griditem-attributes)"}, "wd-grid-item/link-type": {"type": "string", "options": ["navigateTo", "switchTab", "reLaunch"], "description": "页面跳转方式, 参考[微信小程序路由文档](https://developers.weixin.qq.com/miniprogram/dev/framework/app-service/route.html)\n\n[Docs](https://wot-design-uni.cn/component/grid.html#griditem-attributes)"}, "wd-grid-item/use-slot": {"type": "boolean", "description": "是否开启 `GridItem` 内容插槽, default: false.\n\n[Docs](https://wot-design-uni.cn/component/grid.html#griditem-attributes)"}, "wd-grid-item/use-icon-slot": {"type": "boolean", "description": "是否开启 `GridItem` icon 插槽, default: false.\n\n[Docs](https://wot-design-uni.cn/component/grid.html#griditem-attributes)"}, "wd-grid-item/use-text-slot": {"type": "boolean", "description": "是否开启 `GridItem` text 内容插槽, default: false.\n\n[Docs](https://wot-design-uni.cn/component/grid.html#griditem-attributes)"}, "wd-grid-item/icon-size": {"type": "string", "description": "图标大小, default: 26px.\n\n[Docs](https://wot-design-uni.cn/component/grid.html#griditem-attributes)"}, "wd-grid-item/badge-props": {"type": "BadgeProps", "description": "自定义徽标的属性，传入的对象会被透传给 [Badge 组件的 props](/component/badge#attributes)\n\n[Docs](https://wot-design-uni.cn/component/grid.html#griditem-attributes)"}, "wd-icon/name": {"type": "string", "description": "使用的图标名字，可以使用链接图片\n\n[Docs](https://wot-design-uni.cn/component/icon.html#attributes)"}, "wd-icon/color": {"type": "string", "description": "图标的颜色, default: inherit.\n\n[Docs](https://wot-design-uni.cn/component/icon.html#attributes)"}, "wd-icon/size": {"type": "string", "description": "图标的字体大小, default: inherit.\n\n[Docs](https://wot-design-uni.cn/component/icon.html#attributes)"}, "wd-icon/class-prefix": {"type": "string", "description": "类名前缀，用于使用自定义图标, default: wd-icon.\n\n[Docs](https://wot-design-uni.cn/component/icon.html#attributes)"}, "wd-icon/custom-style": {"type": "string", "description": "根节点样式\n\n[Docs](https://wot-design-uni.cn/component/icon.html#attributes)"}, "wd-img-cropper/v-model": {"type": "boolean", "description": "打开图片裁剪组件, default: false.\n\n[Docs](https://wot-design-uni.cn/component/img-cropper.html#attributes)"}, "wd-img-cropper/img-src": {"type": "string", "description": "图片资源链接\n\n[Docs](https://wot-design-uni.cn/component/img-cropper.html#attributes)"}, "wd-img-cropper/img-width": {"type": "number | string", "description": "截屏预览图片的初始宽度; `1、设置宽度不设置高度，按照宽度等比缩放；2、如果都不设置，预览时图片大小会根据裁剪框大小进行等比缩放，进行锁边处理；`; string 类型只支持 % 单位，number 类型时单位为 px\n\n[Docs](https://wot-design-uni.cn/component/img-cropper.html#attributes)"}, "wd-img-cropper/img-height": {"type": "number | string", "description": "截屏预览图片的初始高度; `1、设置高度不设置宽度，按照高度等比缩放；2、如果都不设置，预览时图片大小会根据裁剪框大小进行等比缩放，进行锁边处理；`; string 类型只支持 % 单位，number 类型时单位为 px\n\n[Docs](https://wot-design-uni.cn/component/img-cropper.html#attributes)"}, "wd-img-cropper/disabled-rotate": {"type": "boolean", "description": "禁止图片旋转, default: false.\n\n[Docs](https://wot-design-uni.cn/component/img-cropper.html#attributes)"}, "wd-img-cropper/export-scale": {"type": "number", "description": "设置导出图片尺寸, default: 2.\n\n[Docs](https://wot-design-uni.cn/component/img-cropper.html#attributes)"}, "wd-img-cropper/max-scale": {"type": "number", "description": "最大缩放倍数, default: 3.\n\n[Docs](https://wot-design-uni.cn/component/img-cropper.html#attributes)"}, "wd-img-cropper/cancel-button-text": {"type": "string", "description": "取消按钮文案, default: 取消.\n\n[Docs](https://wot-design-uni.cn/component/img-cropper.html#attributes)"}, "wd-img-cropper/confirm-button-text": {"type": "string", "description": "确认按钮文案, default: 完成.\n\n[Docs](https://wot-design-uni.cn/component/img-cropper.html#attributes)"}, "wd-img-cropper/quality": {"type": "number", "options": ["0", "1"], "description": "生成的图片质量 [wx.canvasToTempFilePath属性介绍](https://developers.weixin.qq.com/miniprogram/dev/api/canvas/wx.canvasToTempFilePath.html#%E5%8F%82%E6%95%B0), default: 1.\n\n[Docs](https://wot-design-uni.cn/component/img-cropper.html#attributes)"}, "wd-img-cropper/file-type": {"type": "string", "description": "目标文件的类型，[wx.canvasToTempFilePath属性介绍](https://developers.weixin.qq.com/miniprogram/dev/api/canvas/wx.canvasToTempFilePath.html#%E5%8F%82%E6%95%B0), default: png.\n\n[Docs](https://wot-design-uni.cn/component/img-cropper.html#attributes)"}, "wd-img-cropper/aspect-ratio": {"type": "string", "description": "裁剪框宽高比，格式为 width:height, default: 1:1.\n\n[Docs](https://wot-design-uni.cn/component/img-cropper.html#attributes)"}, "wd-img-cropper/confirm": {"type": "event", "description": "完成截图时触发\n\n[Docs](https://wot-design-uni.cn/component/img-cropper.html#events)"}, "wd-img-cropper/cancel": {"type": "event", "description": "当取消截图时触发\n\n[Docs](https://wot-design-uni.cn/component/img-cropper.html#events)"}, "wd-img-cropper/imgloaderror": {"type": "event", "description": "当图片加载错误时触发\n\n[Docs](https://wot-design-uni.cn/component/img-cropper.html#events)"}, "wd-img-cropper/imgloaded": {"type": "event", "description": "当图片加载完成时触发\n\n[Docs](https://wot-design-uni.cn/component/img-cropper.html#events)"}, "wd-img/src": {"type": "string", "description": "图片链接\n\n[Docs](https://wot-design-uni.cn/component/img.html#attributes)"}, "wd-img/width": {"type": "number | string", "description": "宽度，默认单位为 px\n\n[Docs](https://wot-design-uni.cn/component/img.html#attributes)"}, "wd-img/height": {"type": "number | string", "description": "高度，默认单位为 px\n\n[Docs](https://wot-design-uni.cn/component/img.html#attributes)"}, "wd-img/mode": {"type": "ImageMode", "options": ["top left'", "'top right'", "'bottom left'", "'bottom right'", "'right'", "'left'", "'center'", "'bottom'", "'top'", "'heightFix'", "'widthFix'", "'aspectFill'", "'aspectFit'", "'scaleToFill"], "description": "填充模式, default: scaleToFill.\n\n[Docs](https://wot-design-uni.cn/component/img.html#attributes)"}, "wd-img/round": {"type": "boolean", "description": "是否显示为圆形, default: false.\n\n[Docs](https://wot-design-uni.cn/component/img.html#attributes)"}, "wd-img/radius": {"type": "number | string", "description": "圆角大小，默认单位为 px\n\n[Docs](https://wot-design-uni.cn/component/img.html#attributes)"}, "wd-img/enable-preview": {"type": "boolean", "description": "是否支持点击预览, default: false.\n\n[Docs](https://wot-design-uni.cn/component/img.html#attributes)"}, "wd-img/show-menu-by-longpress": {"type": "boolean", "description": "开启长按图片显示识别小程序码菜单，仅微信小程序支持, default: false.\n\n[Docs](https://wot-design-uni.cn/component/img.html#attributes)"}, "wd-img/preview-src": {"type": "string", "description": "预览图片链接\n\n[Docs](https://wot-design-uni.cn/component/img.html#attributes)"}, "wd-img/click": {"type": "event", "description": "点击事件\n\n[Docs](https://wot-design-uni.cn/component/img.html#events)"}, "wd-img/load": {"type": "event", "description": "当图片载入完毕时触发\n\n[Docs](https://wot-design-uni.cn/component/img.html#events)"}, "wd-img/error": {"type": "event", "description": "当错误发生时触发\n\n[Docs](https://wot-design-uni.cn/component/img.html#events)"}, "wd-index-anchor/index": {"type": "string | number", "description": "索引字符\n\n[Docs](https://wot-design-uni.cn/component/index-bar.html#indexanchor-attributes)"}, "wd-index-bar/sticky": {"type": "boolean", "description": "索引是否吸顶, default: false.\n\n[Docs](https://wot-design-uni.cn/component/index-bar.html#attributes)"}, "wd-input-number/v-model": {"type": "number | string", "description": "绑定值\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#attributes)"}, "wd-input-number/min": {"type": "number", "description": "最小值, default: 1.\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#attributes)"}, "wd-input-number/max": {"type": "number", "description": "最大值, default: Infinity.\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#attributes)"}, "wd-input-number/step": {"type": "number", "description": "步数, default: 1.\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#attributes)"}, "wd-input-number/step-strictly": {"type": "boolean", "description": "严格值为步数的倍数, default: false.\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#attributes)"}, "wd-input-number/precision": {"type": "number", "description": "小数精度, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#attributes)"}, "wd-input-number/disabled": {"type": "boolean", "description": "禁用, default: false.\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#attributes)"}, "wd-input-number/without-input": {"type": "boolean", "description": "不显示输入框, default: false.\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#attributes)"}, "wd-input-number/input-width": {"type": "string", "description": "输入框宽度, default: 36px.\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#attributes)"}, "wd-input-number/allow-null": {"type": "boolean", "description": "是否允许输入的值为空，设置为 `true` 后允许传入空字符串, default: false.\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#attributes)"}, "wd-input-number/placeholder": {"type": "string", "description": "占位文本\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#attributes)"}, "wd-input-number/disable-input": {"type": "boolean", "description": "禁用输入框, default: false.\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#attributes)"}, "wd-input-number/disable-plus": {"type": "boolean", "description": "禁用增加按钮, default: false.\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#attributes)"}, "wd-input-number/disable-minus": {"type": "boolean", "description": "禁用减少按钮, default: false.\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#attributes)"}, "wd-input-number/adjust-position": {"type": "boolean", "description": "原生属性，键盘弹起时，是否自动上推页面, default: true.\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#attributes)"}, "wd-input-number/before-change": {"type": "`(value: number | string) => boolean | Promise<boolean>`", "description": "输入框值改变前触发，返回 false 会阻止输入框值改变，支持返回 `Promise`\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#attributes)"}, "wd-input-number/long-press": {"type": "boolean", "description": "是否允许长按进行加减, default: false.\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#attributes)"}, "wd-input-number/immediate-change": {"type": "boolean", "description": "是否立即响应输入变化，false 时仅在失焦和按钮点击时更新, default: true.\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#attributes)"}, "wd-input-number/update-on-init": {"type": "boolean", "description": "是否在初始化时更新 v-model 为修正后的值, default: true.\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#attributes)"}, "wd-input-number/input-type": {"type": "string", "options": ["number", "digit"], "description": "输入框类型, default: digit.\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#attributes)"}, "wd-input-number/change": {"type": "event", "description": "值修改事件\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#events)"}, "wd-input-number/focus": {"type": "event", "description": "输入框获取焦点事件\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#events)"}, "wd-input-number/blur": {"type": "event", "description": "输入框失去焦点事件\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#events)"}, "wd-input/type": {"type": "string", "options": ["text", "number", "digit", "idcard", "safe-password", "nickname", "tel"], "description": "类型, default: text.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/v-model": {"type": "string | number", "description": "绑定值\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/placeholder": {"type": "string", "description": "占位文本, default: 请输入....\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/clearable": {"type": "boolean", "description": "显示清空按钮, default: false.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/maxlength": {"type": "number", "description": "原生属性，最大长度, default: 支付宝小程序无默认值，其余平台默认为-1.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/show-password": {"type": "boolean", "description": "显示为密码框, default: false.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/disabled": {"type": "boolean", "description": "原生属性，禁用, default: false.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/readonly": {"type": "boolean", "description": "只读, default: false.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/prefix-icon": {"type": "string", "description": "前置图标，icon组件中的图标类名\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/suffix-icon": {"type": "string", "description": "后置图标，icon组件中的图标类名\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/show-word-limit": {"type": "boolean", "description": "显示字数限制，需要同时设置 maxlength, default: false.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/confirm-type": {"type": "string", "options": ["done", "go", "next", "search", "send"], "description": "设置键盘右下角按钮的文字，仅在type='text'时生效, default: done.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/confirm-hold": {"type": "Boolean", "description": "点击键盘右下角按钮时是否保持键盘不收起, default: false.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/always-embed": {"type": "Boolean", "description": "微信小程序原生属性，强制 input 处于同层状态，默认 focus 时 input 会切到非同层状态 (仅在 iOS 下生效), default: false.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/placeholder-style": {"type": "string", "description": "原生属性，指定 placeholder 的样式，目前仅支持color,font-size和font-weight\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/placeholder-class": {"type": "string", "description": "原生属性，指定 placeholder 的样式类\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/focus": {"type": "event", "description": "监听输入框focus事件\n\n[Docs](https://wot-design-uni.cn/component/input.html#events)"}, "wd-input/cursor-spacing": {"type": "number", "description": "原生属性，指定光标与键盘的距离。取 input 距离底部的距离和cursor-spacing指定的距离的最小值作为光标与键盘的距离, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/cursor": {"type": "number", "description": "原生属性，指定focus时的光标位置, default: -1.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/selection-start": {"type": "number", "description": "原生属性，光标起始位置，自动聚集时有效，需与selection-end搭配使用, default: -1.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/selection-end": {"type": "number", "description": "原生属性，光标结束位置，自动聚集时有效，需与selection-start搭配使用, default: -1.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/adjust-position": {"type": "boolean", "description": "原生属性，键盘弹起时，是否自动上推页面, default: true.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/label": {"type": "string", "description": "设置左侧标题\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/size": {"type": "string", "description": "设置输入框大小\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/error": {"type": "boolean", "description": "设置输入框错误状态，错误状态时为红色, default: false.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/center": {"type": "boolean", "description": "当有label属性时，设置标题和输入框垂直居中，默认为顶部居中, default: false.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/label-width": {"type": "string", "description": "设置左侧标题宽度, default: 33%.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/required": {"type": "boolean", "description": "cell 类型下必填样式, default: false.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/no-border": {"type": "boolean", "description": "非 cell 类型下是否隐藏下划线, default: false.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/prop": {"type": "string", "description": "表单域 `model` 字段名，在使用表单校验功能的情况下，该属性是必填的\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/rules": {"type": "`FormItemRule []`", "description": "表单验证规则，结合`wd-form`组件使用, default: [].\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/clear-trigger": {"type": "`InputClearTrigger`", "options": ["focus", "always"], "description": "显示清除图标的时机，always 表示输入框不为空时展示，focus 表示输入框聚焦且不为空时展示, default: always.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/focus-when-clear": {"type": "boolean", "description": "是否在点击清除按钮时聚焦输入框, default: true.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/ignore-composition-event": {"type": "boolean", "description": "是否忽略组件内对文本合成系统事件的处理。为 false 时将触发 compositionstart、compositionend、compositionupdate 事件，且在文本合成期间会触发 input 事件。, default: true.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/inputmode": {"type": "InputMode", "description": "提供用户在编辑元素或其内容时可能输入的数据类型的提示。, default: text.\n\n[Docs](https://wot-design-uni.cn/component/input.html#attributes)"}, "wd-input/input": {"type": "event", "description": "监听输入框input事件\n\n[Docs](https://wot-design-uni.cn/component/input.html#events)"}, "wd-input/blur": {"type": "event", "description": "监听输入框blur事件\n\n[Docs](https://wot-design-uni.cn/component/input.html#events)"}, "wd-input/clear": {"type": "event", "description": "监听输入框清空按钮事件\n\n[Docs](https://wot-design-uni.cn/component/input.html#events)"}, "wd-input/confirm": {"type": "event", "description": "点击完成时， 触发 confirm 事件\n\n[Docs](https://wot-design-uni.cn/component/input.html#events)"}, "wd-input/keyboardheightchange": {"type": "event", "description": "键盘高度发生变化的时候触发此事件\n\n[Docs](https://wot-design-uni.cn/component/input.html#events)"}, "wd-input/clickprefixicon": {"type": "event", "description": "点击前置图标时触发\n\n[Docs](https://wot-design-uni.cn/component/input.html#events)"}, "wd-input/clicksuffixicon": {"type": "event", "description": "点击后置图标时触发\n\n[Docs](https://wot-design-uni.cn/component/input.html#events)"}, "wd-keyboard/v-model:visible": {"type": "`boolean`", "description": "是否展开, default: false.\n\n[Docs](https://wot-design-uni.cn/component/keyboard.html#attributes)"}, "wd-keyboard/v-model": {"type": "`string`", "description": "绑定的值\n\n[Docs](https://wot-design-uni.cn/component/keyboard.html#attributes)"}, "wd-keyboard/title": {"type": "`string`", "description": "标题\n\n[Docs](https://wot-design-uni.cn/component/keyboard.html#attributes)"}, "wd-keyboard/mode": {"type": "`string`", "options": ["default, car, custom"], "description": "键盘模式, default: default.\n\n[Docs](https://wot-design-uni.cn/component/keyboard.html#attributes)"}, "wd-keyboard/z-index": {"type": "`number`", "description": "层级, default: 100.\n\n[Docs](https://wot-design-uni.cn/component/keyboard.html#attributes)"}, "wd-keyboard/maxlength": {"type": "`number`", "description": "最大长度, default: Infinity.\n\n[Docs](https://wot-design-uni.cn/component/keyboard.html#attributes)"}, "wd-keyboard/show-delete-key": {"type": "`boolean`", "description": "是否显示删除键, default: true.\n\n[Docs](https://wot-design-uni.cn/component/keyboard.html#attributes)"}, "wd-keyboard/random-key-order": {"type": "`boolean`", "description": "是否随机键盘按键顺序, default: false.\n\n[Docs](https://wot-design-uni.cn/component/keyboard.html#attributes)"}, "wd-keyboard/close-text": {"type": "`string`", "description": "确认按钮文本\n\n[Docs](https://wot-design-uni.cn/component/keyboard.html#attributes)"}, "wd-keyboard/delete-text": {"type": "`string`", "description": "删除按钮文本\n\n[Docs](https://wot-design-uni.cn/component/keyboard.html#attributes)"}, "wd-keyboard/close-button-loading": {"type": "`boolean`", "description": "关闭按钮是否显示加载状态, default: false.\n\n[Docs](https://wot-design-uni.cn/component/keyboard.html#attributes)"}, "wd-keyboard/modal": {"type": "`boolean`", "description": "是否显示蒙层遮罩, default: false.\n\n[Docs](https://wot-design-uni.cn/component/keyboard.html#attributes)"}, "wd-keyboard/hide-on-click-outside": {"type": "`boolean`", "description": "是否在点击外部时收起键盘, default: true.\n\n[Docs](https://wot-design-uni.cn/component/keyboard.html#attributes)"}, "wd-keyboard/lock-scroll": {"type": "`boolean`", "description": "是否锁定背景滚动，锁定时蒙层里的内容也将无法滚动, default: true.\n\n[Docs](https://wot-design-uni.cn/component/keyboard.html#attributes)"}, "wd-keyboard/safe-area-inset-bottom": {"type": "`boolean`", "description": "是否在底部安全区域内, default: true.\n\n[Docs](https://wot-design-uni.cn/component/keyboard.html#attributes)"}, "wd-keyboard/extra-key": {"type": "`string` | `string[]`", "description": "额外按键\n\n[Docs](https://wot-design-uni.cn/component/keyboard.html#attributes)"}, "wd-keyboard/root-portal": {"type": "`boolean`", "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题, default: false.\n\n[Docs](https://wot-design-uni.cn/component/keyboard.html#attributes)"}, "wd-keyboard/input": {"type": "event", "description": "点击按键时触发\n\n[Docs](https://wot-design-uni.cn/component/keyboard.html#events)"}, "wd-keyboard/delete": {"type": "event", "description": "点击删除键时触发\n\n[Docs](https://wot-design-uni.cn/component/keyboard.html#events)"}, "wd-keyboard/close": {"type": "event", "description": "点击关闭按钮或非键盘区域时触发\n\n[Docs](https://wot-design-uni.cn/component/keyboard.html#events)"}, "wd-row/gutter": {"type": "number", "description": "列元素之间的间距（单位为px）, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/layout.html#row-attributes)"}, "wd-col/span": {"type": "number", "description": "列元素宽度, default: 24.\n\n[Docs](https://wot-design-uni.cn/component/layout.html#col-attributes)"}, "wd-col/offset": {"type": "number", "description": "列元素偏移距离, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/layout.html#col-attributes)"}, "wd-loading/type": {"type": "string", "options": ["outline"], "description": "加载指示器类型, default: ring.\n\n[Docs](https://wot-design-uni.cn/component/loading.html#attributes)"}, "wd-loading/color": {"type": "string", "description": "设置加载指示器颜色, default: #4D80F0.\n\n[Docs](https://wot-design-uni.cn/component/loading.html#attributes)"}, "wd-loading/size": {"type": "number | string", "description": "设置加载指示器大小, default: 32px.\n\n[Docs](https://wot-design-uni.cn/component/loading.html#attributes)"}, "wd-loadmore/state": {"type": "string", "options": ["loading", "finished", "error"], "description": "加载状态\n\n[Docs](https://wot-design-uni.cn/component/loadmore.html#attributes)"}, "wd-loadmore/loading-text": {"type": "string", "description": "加载提示文案, default: 加载中....\n\n[Docs](https://wot-design-uni.cn/component/loadmore.html#attributes)"}, "wd-loadmore/finished-text": {"type": "string", "description": "全部加载完的提示文案, default: 没有更多了.\n\n[Docs](https://wot-design-uni.cn/component/loadmore.html#attributes)"}, "wd-loadmore/error-text": {"type": "string", "description": "加载失败的提示文案, default: 加载失败，点击重试.\n\n[Docs](https://wot-design-uni.cn/component/loadmore.html#attributes)"}, "wd-loadmore/loading-props": {"type": "`Partial<LoadingProps>`", "description": "loading加载组件属性\n\n[Docs](https://wot-design-uni.cn/component/loadmore.html#attributes)"}, "wd-loadmore/reload": {"type": "event", "description": "state 为 error 加载错误时，点击文案触发 reload 事件\n\n[Docs](https://wot-design-uni.cn/component/loadmore.html#events)"}, "wd-message-box/selector": {"type": "string", "description": "指定唯一标识\n\n[Docs](https://wot-design-uni.cn/component/message-box.html#attributes)"}, "wd-message-box/root-portal": {"type": "boolean", "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题, default: false.\n\n[Docs](https://wot-design-uni.cn/component/message-box.html#attributes)"}, "wd-navbar/title": {"type": "string", "description": "卡片标题\n\n[Docs](https://wot-design-uni.cn/component/navbar.html#navbar-attributes)"}, "wd-navbar/left-text": {"type": "string", "description": "左侧文案\n\n[Docs](https://wot-design-uni.cn/component/navbar.html#navbar-attributes)"}, "wd-navbar/right-text": {"type": "string", "description": "右侧文案\n\n[Docs](https://wot-design-uni.cn/component/navbar.html#navbar-attributes)"}, "wd-navbar/left-arrow": {"type": "boolean", "options": ["true, false"], "description": "显示左侧箭头, default: false.\n\n[Docs](https://wot-design-uni.cn/component/navbar.html#navbar-attributes)"}, "wd-navbar/bordered": {"type": "boolean", "options": ["true, false"], "description": "显示下边框, default: true.\n\n[Docs](https://wot-design-uni.cn/component/navbar.html#navbar-attributes)"}, "wd-navbar/fixed": {"type": "boolean", "options": ["true, false"], "description": "固定到顶部, default: false.\n\n[Docs](https://wot-design-uni.cn/component/navbar.html#navbar-attributes)"}, "wd-navbar/placeholder": {"type": "boolean", "options": ["true, false"], "description": "固定在顶部时，在标签位置生成一个等高的占位元素, default: false.\n\n[Docs](https://wot-design-uni.cn/component/navbar.html#navbar-attributes)"}, "wd-navbar/z-index": {"type": "number", "description": "导航栏 z-index, default: 1.\n\n[Docs](https://wot-design-uni.cn/component/navbar.html#navbar-attributes)"}, "wd-navbar/safe-area-inset-top": {"type": "boolean", "options": ["true, false"], "description": "开启顶部安全区适配, default: false.\n\n[Docs](https://wot-design-uni.cn/component/navbar.html#navbar-attributes)"}, "wd-navbar/left-disabled": {"type": "boolean", "options": ["true, false"], "description": "禁用左侧按钮，禁用时透明度降低，且无法点击, default: false.\n\n[Docs](https://wot-design-uni.cn/component/navbar.html#navbar-attributes)"}, "wd-navbar/right-disabled": {"type": "boolean", "options": ["true, false"], "description": "禁用右侧按钮，禁用时透明度降低，且无法点击, default: false.\n\n[Docs](https://wot-design-uni.cn/component/navbar.html#navbar-attributes)"}, "wd-navbar/click-left": {"type": "event", "description": "点击左侧按钮时触发\n\n[Docs](https://wot-design-uni.cn/component/navbar.html#navbar-events)"}, "wd-navbar/click-right": {"type": "event", "description": "点击右侧按钮时触发\n\n[Docs](https://wot-design-uni.cn/component/navbar.html#navbar-events)"}, "wd-navbar-capsule/back": {"type": "event", "description": "点击返回按钮时触发\n\n[Docs](https://wot-design-uni.cn/component/navbar.html#navbarcapsule-events)"}, "wd-navbar-capsule/back-home": {"type": "event", "description": "点击返回首页按钮时触发\n\n[Docs](https://wot-design-uni.cn/component/navbar.html#navbarcapsule-events)"}, "wd-notice-bar/text": {"type": "`string` `string[]`", "description": "设置通知栏文案\n\n[Docs](https://wot-design-uni.cn/component/notice-bar.html#attributes)"}, "wd-notice-bar/type": {"type": "`string`", "options": ["info", "warning", "danger"], "description": "设置通知栏类型, default: warning.\n\n[Docs](https://wot-design-uni.cn/component/notice-bar.html#attributes)"}, "wd-notice-bar/prefix": {"type": "`string`", "description": "设置左侧图标，使用 icon 章节中的图标名\n\n[Docs](https://wot-design-uni.cn/component/notice-bar.html#attributes)"}, "wd-notice-bar/scrollable": {"type": "`boolean`", "description": "是否可以滚动, default: true.\n\n[Docs](https://wot-design-uni.cn/component/notice-bar.html#attributes)"}, "wd-notice-bar/delay": {"type": "`number`", "description": "滚动动画初始延时，单位 秒(s), default: 1.\n\n[Docs](https://wot-design-uni.cn/component/notice-bar.html#attributes)"}, "wd-notice-bar/speed": {"type": "`number`", "description": "滚动速度，单位 px/s, default: 50.\n\n[Docs](https://wot-design-uni.cn/component/notice-bar.html#attributes)"}, "wd-notice-bar/closable": {"type": "`boolean`", "description": "是否可以关闭, default: false.\n\n[Docs](https://wot-design-uni.cn/component/notice-bar.html#attributes)"}, "wd-notice-bar/wrapable": {"type": "`boolean`", "description": "是否换行展示, default: false.\n\n[Docs](https://wot-design-uni.cn/component/notice-bar.html#attributes)"}, "wd-notice-bar/color": {"type": "`string`", "description": "文字、图标颜色\n\n[Docs](https://wot-design-uni.cn/component/notice-bar.html#attributes)"}, "wd-notice-bar/background-color": {"type": "`string`", "description": "背景颜色\n\n[Docs](https://wot-design-uni.cn/component/notice-bar.html#attributes)"}, "wd-notice-bar/direction": {"type": "`NoticeBarScrollDirection`", "options": ["horizontal vertical"], "description": "滚动方向, default: horizontal.\n\n[Docs](https://wot-design-uni.cn/component/notice-bar.html#attributes)"}, "wd-notice-bar/close": {"type": "event", "description": "关闭按钮点击时\n\n[Docs](https://wot-design-uni.cn/component/notice-bar.html#events)"}, "wd-notice-bar/next": {"type": "event", "description": "下一次滚动前触发\n\n[Docs](https://wot-design-uni.cn/component/notice-bar.html#events)"}, "wd-notice-bar/click": {"type": "event", "description": "点击时触发\n\n[Docs](https://wot-design-uni.cn/component/notice-bar.html#events)"}, "wd-notify/type": {"type": "NotifyType", "options": ["primary success warning danger"], "description": "类型, default: danger.\n\n[Docs](https://wot-design-uni.cn/component/notify.html#attributes)"}, "wd-notify/message": {"type": "string", "description": "展示文案，支持通过`\\n`换行\n\n[Docs](https://wot-design-uni.cn/component/notify.html#attributes)"}, "wd-notify/duration": {"type": "number", "description": "展示时长(ms)，值为 0 时，notify 不会消失, default: 3000.\n\n[Docs](https://wot-design-uni.cn/component/notify.html#attributes)"}, "wd-notify/z-index": {"type": "number", "description": "层级, default: 99.\n\n[Docs](https://wot-design-uni.cn/component/notify.html#attributes)"}, "wd-notify/position": {"type": "NotifyPosition", "options": ["top bottom"], "description": "弹出位置, default: top.\n\n[Docs](https://wot-design-uni.cn/component/notify.html#attributes)"}, "wd-notify/color": {"type": "string", "description": "字体颜色\n\n[Docs](https://wot-design-uni.cn/component/notify.html#attributes)"}, "wd-notify/background": {"type": "string", "description": "背景颜色\n\n[Docs](https://wot-design-uni.cn/component/notify.html#attributes)"}, "wd-notify/safe-height": {"type": "number | string", "description": "顶部安全高度\n\n[Docs](https://wot-design-uni.cn/component/notify.html#attributes)"}, "wd-notify/selector": {"type": "number", "description": "指定唯一标识\n\n[Docs](https://wot-design-uni.cn/component/notify.html#attributes)"}, "wd-notify/root-portal": {"type": "boolean", "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题, default: false.\n\n[Docs](https://wot-design-uni.cn/component/notify.html#attributes)"}, "wd-number-keyboard/v-model:visible": {"type": "`boolean`", "description": "是否展开, default: false.\n\n[Docs](https://wot-design-uni.cn/component/number-keyboard.html#attributes)"}, "wd-number-keyboard/v-model": {"type": "`string`", "description": "绑定的值\n\n[Docs](https://wot-design-uni.cn/component/number-keyboard.html#attributes)"}, "wd-number-keyboard/title": {"type": "`string`", "description": "标题\n\n[Docs](https://wot-design-uni.cn/component/number-keyboard.html#attributes)"}, "wd-number-keyboard/mode": {"type": "`string`", "options": ["default, custom"], "description": "键盘模式, default: default.\n\n[Docs](https://wot-design-uni.cn/component/number-keyboard.html#attributes)"}, "wd-number-keyboard/z-index": {"type": "`number`", "description": "层级, default: 100.\n\n[Docs](https://wot-design-uni.cn/component/number-keyboard.html#attributes)"}, "wd-number-keyboard/maxlength": {"type": "`number`", "description": "最大长度, default: Infinity.\n\n[Docs](https://wot-design-uni.cn/component/number-keyboard.html#attributes)"}, "wd-number-keyboard/show-delete-key": {"type": "`boolean`", "description": "是否显示删除键, default: true.\n\n[Docs](https://wot-design-uni.cn/component/number-keyboard.html#attributes)"}, "wd-number-keyboard/random-key-order": {"type": "`boolean`", "description": "是否随机键盘按键顺序, default: false.\n\n[Docs](https://wot-design-uni.cn/component/number-keyboard.html#attributes)"}, "wd-number-keyboard/close-text": {"type": "`string`", "description": "确认按钮文本\n\n[Docs](https://wot-design-uni.cn/component/number-keyboard.html#attributes)"}, "wd-number-keyboard/delete-text": {"type": "`string`", "description": "删除按钮文本\n\n[Docs](https://wot-design-uni.cn/component/number-keyboard.html#attributes)"}, "wd-number-keyboard/close-button-loading": {"type": "`boolean`", "description": "关闭按钮是否显示加载状态, default: false.\n\n[Docs](https://wot-design-uni.cn/component/number-keyboard.html#attributes)"}, "wd-number-keyboard/modal": {"type": "`boolean`", "description": "是否显示蒙层遮罩, default: false.\n\n[Docs](https://wot-design-uni.cn/component/number-keyboard.html#attributes)"}, "wd-number-keyboard/hide-on-click-outside": {"type": "`boolean`", "description": "是否在点击外部时收起键盘, default: true.\n\n[Docs](https://wot-design-uni.cn/component/number-keyboard.html#attributes)"}, "wd-number-keyboard/lock-scroll": {"type": "`boolean`", "description": "是否锁定背景滚动，锁定时蒙层里的内容也将无法滚动, default: true.\n\n[Docs](https://wot-design-uni.cn/component/number-keyboard.html#attributes)"}, "wd-number-keyboard/safe-area-inset-bottom": {"type": "`boolean`", "description": "是否在底部安全区域内, default: true.\n\n[Docs](https://wot-design-uni.cn/component/number-keyboard.html#attributes)"}, "wd-number-keyboard/extra-key": {"type": "`string` | `string[]`", "description": "额外按键\n\n[Docs](https://wot-design-uni.cn/component/number-keyboard.html#attributes)"}, "wd-number-keyboard/root-portal": {"type": "`boolean`", "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题, default: false.\n\n[Docs](https://wot-design-uni.cn/component/number-keyboard.html#attributes)"}, "wd-number-keyboard/input": {"type": "event", "description": "点击按键时触发\n\n[Docs](https://wot-design-uni.cn/component/number-keyboard.html#events)"}, "wd-number-keyboard/delete": {"type": "event", "description": "点击删除键时触发\n\n[Docs](https://wot-design-uni.cn/component/number-keyboard.html#events)"}, "wd-number-keyboard/close": {"type": "event", "description": "点击关闭按钮或非键盘区域时触发\n\n[Docs](https://wot-design-uni.cn/component/number-keyboard.html#events)"}, "wd-overlay/show": {"type": "`boolean`", "options": ["true"], "description": "是否展示遮罩层, default: false.\n\n[Docs](https://wot-design-uni.cn/component/overlay.html#attributes)"}, "wd-overlay/duration": {"type": "`string | number`", "description": "动画时长，单位毫秒, default: 300.\n\n[Docs](https://wot-design-uni.cn/component/overlay.html#attributes)"}, "wd-overlay/lock-scroll": {"type": "`boolean`", "options": ["false"], "description": "是否锁定背景滚动，锁定时蒙层里的内容也将无法滚动, default: true.\n\n[Docs](https://wot-design-uni.cn/component/overlay.html#attributes)"}, "wd-overlay/z-index": {"type": "`number`", "description": "层级, default: 10.\n\n[Docs](https://wot-design-uni.cn/component/overlay.html#attributes)"}, "wd-overlay/custom-style": {"type": "`string`", "description": "自定义样式\n\n[Docs](https://wot-design-uni.cn/component/overlay.html#attributes)"}, "wd-pagination/v-model": {"type": "number", "description": "绑定值\n\n[Docs](https://wot-design-uni.cn/component/pagination.html#attributes)"}, "wd-pagination/prev-text": {"type": "string", "description": "上一页按钮文字, default: 上一页.\n\n[Docs](https://wot-design-uni.cn/component/pagination.html#attributes)"}, "wd-pagination/next-text": {"type": "string", "description": "下一页按钮文字, default: 下一页.\n\n[Docs](https://wot-design-uni.cn/component/pagination.html#attributes)"}, "wd-pagination/total-page": {"type": "number", "description": "总页数，如果有total，则优先使用total计算页数, default: 根据页数计算.\n\n[Docs](https://wot-design-uni.cn/component/pagination.html#attributes)"}, "wd-pagination/page-size": {"type": "number", "description": "分页大小, default: 10.\n\n[Docs](https://wot-design-uni.cn/component/pagination.html#attributes)"}, "wd-pagination/total": {"type": "number", "description": "总数据个数\n\n[Docs](https://wot-design-uni.cn/component/pagination.html#attributes)"}, "wd-pagination/show-icon": {"type": "boolean", "description": "是否展示分页Icon, default: false.\n\n[Docs](https://wot-design-uni.cn/component/pagination.html#attributes)"}, "wd-pagination/show-message": {"type": "boolean", "description": "是否展示文字提示, default: false.\n\n[Docs](https://wot-design-uni.cn/component/pagination.html#attributes)"}, "wd-pagination/hide-if-one-page": {"type": "boolean", "description": "总页数只有一页时是否隐藏, default: true.\n\n[Docs](https://wot-design-uni.cn/component/pagination.html#attributes)"}, "wd-pagination/change": {"type": "event", "description": "值修改事件\n\n[Docs](https://wot-design-uni.cn/component/pagination.html#events)"}, "wd-password-input/v-model": {"type": "string", "description": "密码值\n\n[Docs](https://wot-design-uni.cn/component/password-input.html#attributes)"}, "wd-password-input/info": {"type": "string", "description": "输入框下方文字提示\n\n[Docs](https://wot-design-uni.cn/component/password-input.html#attributes)"}, "wd-password-input/error-info": {"type": "string", "description": "输入框下方错误提示\n\n[Docs](https://wot-design-uni.cn/component/password-input.html#attributes)"}, "wd-password-input/length": {"type": "number", "description": "密码最大长度, default: 6.\n\n[Docs](https://wot-design-uni.cn/component/password-input.html#attributes)"}, "wd-password-input/gutter": {"type": "number | string", "description": "输入框格子之间的间距，如 20px 2em，默认单位为 px, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/password-input.html#attributes)"}, "wd-password-input/mask": {"type": "boolean", "description": "是否隐藏密码内容, default: true.\n\n[Docs](https://wot-design-uni.cn/component/password-input.html#attributes)"}, "wd-password-input/focused": {"type": "boolean", "description": "是否已聚焦，聚焦时会显示光标, default: false.\n\n[Docs](https://wot-design-uni.cn/component/password-input.html#attributes)"}, "wd-picker-view/v-model": {"type": "string | number | boolean | array", "description": "选中项，如果为多列选择器，则其类型应为数组\n\n[Docs](https://wot-design-uni.cn/component/picker-view.html#attributes)"}, "wd-picker-view/columns": {"type": "array", "description": "选择器数据，可以为字符串数组，也可以为对象数组，如果为二维数组，则为多列选择器\n\n[Docs](https://wot-design-uni.cn/component/picker-view.html#attributes)"}, "wd-picker-view/loading": {"type": "boolean", "description": "加载中, default: false.\n\n[Docs](https://wot-design-uni.cn/component/picker-view.html#attributes)"}, "wd-picker-view/loading-color": {"type": "string", "description": "加载的颜色，只能使用十六进制的色值写法，且不能使用缩写, default: #4D80F0.\n\n[Docs](https://wot-design-uni.cn/component/picker-view.html#attributes)"}, "wd-picker-view/columns-height": {"type": "number", "description": "picker内部滚筒高, default: 231.\n\n[Docs](https://wot-design-uni.cn/component/picker-view.html#attributes)"}, "wd-picker-view/value-key": {"type": "string", "description": "选项对象中，value对应的 key, default: value.\n\n[Docs](https://wot-design-uni.cn/component/picker-view.html#attributes)"}, "wd-picker-view/label-key": {"type": "string", "description": "选项对象中，展示的文本对应的 key, default: label.\n\n[Docs](https://wot-design-uni.cn/component/picker-view.html#attributes)"}, "wd-picker-view/column-change": {"type": "function", "description": "接收 pickerView 实例、选中项、当前修改列的下标、resolve 作为入参，根据选中项和列下标进行判断，通过 pickerView 实例暴露出来的 `setColumnData` 方法修改其他列的数据源。\n\n[Docs](https://wot-design-uni.cn/component/picker-view.html#attributes)"}, "wd-picker-view/immediate-change": {"type": "boolean", "description": "是否在手指松开时立即触发picker-view的 change 事件。若不开启则会在滚动动画结束后触发 change 事件，1.2.25版本起提供，仅微信小程序和支付宝小程序支持。, default: false.\n\n[Docs](https://wot-design-uni.cn/component/picker-view.html#attributes)"}, "wd-picker-view/change": {"type": "event", "description": "选项值修改时触发\n\n[Docs](https://wot-design-uni.cn/component/picker-view.html#events)"}, "wd-picker-view/pickstart": {"type": "event", "description": "当滚动选择开始时候触发事件\n\n[Docs](https://wot-design-uni.cn/component/picker-view.html#events)"}, "wd-picker-view/pickend": {"type": "event", "description": "当滚动选择结束时候触发事件\n\n[Docs](https://wot-design-uni.cn/component/picker-view.html#events)"}, "wd-picker/v-model": {"type": "string|number|boolean|array", "description": "选中项，如果为多列选择器，则其类型应为数组\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/columns": {"type": "array", "description": "选择器数据，可以为字符串数组，也可以为对象数组，如果为二维数组，则为多列选择器\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/loading": {"type": "boolean", "description": "加载中, default: false.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/loading-color": {"type": "string", "description": "加载的颜色，只能使用十六进制的色值写法，且不能使用缩写, default: #4D80F0.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/columns-height": {"type": "number", "description": "picker内部滚筒高, default: 231.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/value-key": {"type": "string", "description": "选项对象中，value对应的 key, default: value.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/label-key": {"type": "string", "description": "选项对象中，展示的文本对应的 key, default: label.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/title": {"type": "string", "description": "弹出层标题\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/cancel-button-text": {"type": "string", "description": "取消按钮文案, default: 取消.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/confirm-button-text": {"type": "string", "description": "确认按钮文案, default: 完成.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/label": {"type": "string", "description": "选择器左侧文案\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/placeholder": {"type": "string", "description": "选择器占位符, default: 请选择.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/disabled": {"type": "boolean", "description": "禁用, default: false.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/readonly": {"type": "boolean", "description": "只读, default: false.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/display-format": {"type": "function", "description": "自定义展示文案的格式化函数，返回一个字符串\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/column-change": {"type": "function", "description": "接收 pickerView 实例、选中项、当前修改列的下标、resolve 作为入参，根据选中项和列下标进行判断，通过 pickerView 实例暴露出来的 `setColumnData` 方法修改其他列的数据源\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/size": {"type": "string", "options": ["large"], "description": "设置选择器大小\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/label-width": {"type": "string", "description": "设置左侧标题宽度, default: 33%.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/error": {"type": "boolean", "description": "是否为错误状态，错误状态时右侧内容为红色, default: false.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/required": {"type": "boolean", "description": "表单属性，必填, default: false.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/align-right": {"type": "boolean", "description": "选择器的值靠右展示, default: false.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/use-label-slot": {"type": "boolean", "description": "label 使用插槽, default: false.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/use-default-slot": {"type": "boolean", "description": "使用默认插槽, default: false.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/before-confirm": {"type": "function", "description": "确定前校验函数，接收 (value, resolve, picker) 参数，通过 resolve 继续执行 picker，resolve 接收1个boolean参数\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/close-on-click-modal": {"type": "boolean", "description": "点击遮罩是否关闭, default: true.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/z-index": {"type": "number", "description": "弹窗层级, default: 15.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/safe-area-inset-bottom": {"type": "boolean", "description": "弹出面板是否设置底部安全距离（iphone X 类型的机型）, default: true.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/ellipsis": {"type": "boolean", "description": "是否超出隐藏, default: false.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/prop": {"type": "string", "description": "表单域 `model` 字段名，在使用表单校验功能的情况下，该属性是必填的\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/rules": {"type": "`FormItemRule []`", "description": "表单验证规则，结合`wd-form`组件使用, default: [].\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/immediate-change": {"type": "boolean", "description": "是否在手指松开时立即触发picker-view的 change 事件。若不开启则会在滚动动画结束后触发 change 事件，1.2.25版本起提供，仅微信小程序和支付宝小程序支持, default: false.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/clearable": {"type": "boolean", "description": "显示清空按钮, default: false.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/root-portal": {"type": "boolean", "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题, default: false.\n\n[Docs](https://wot-design-uni.cn/component/picker.html#attributes)"}, "wd-picker/confirm": {"type": "event", "description": "点击右侧按钮触发\n\n[Docs](https://wot-design-uni.cn/component/picker.html#events)"}, "wd-picker/cancel": {"type": "event", "description": "点击左侧按钮触发\n\n[Docs](https://wot-design-uni.cn/component/picker.html#events)"}, "wd-picker/open": {"type": "event", "description": "打开选择器弹出层时触发\n\n[Docs](https://wot-design-uni.cn/component/picker.html#events)"}, "wd-picker/clear": {"type": "event", "description": "点击清空按钮时触发\n\n[Docs](https://wot-design-uni.cn/component/picker.html#events)"}, "wd-popover/v-model": {"type": "boolean", "description": "手动状态是否可见, default: false.\n\n[Docs](https://wot-design-uni.cn/component/popover.html#popover-attributes)"}, "wd-popover/content": {"type": "string|array（当模式为菜单模式时，content 属性格式为 Array）", "description": "显示的内容，也可以通过 `slot#content` 传入\n\n[Docs](https://wot-design-uni.cn/component/popover.html#popover-attributes)"}, "wd-popover/mode": {"type": "string", "options": ["normal（普通模式）", "menu（菜单模式）"], "description": "当前显示的模式，决定内容的展现形式, default: normal.\n\n[Docs](https://wot-design-uni.cn/component/popover.html#popover-attributes)"}, "wd-popover/placement": {"type": "string", "options": ["top", "top-start", "top-end", "bottom", "bottom-start", "bottom-end", "left", "left-start", "left-end", "right", "right-start", "right-end"], "description": "popover 的出现位置, default: bottom.\n\n[Docs](https://wot-design-uni.cn/component/popover.html#popover-attributes)"}, "wd-popover/visible-arrow": {"type": "boolean", "description": "是否显示 popover 箭头, default: true.\n\n[Docs](https://wot-design-uni.cn/component/popover.html#popover-attributes)"}, "wd-popover/disabled": {"type": "boolean", "description": "popover 是否可用, default: false.\n\n[Docs](https://wot-design-uni.cn/component/popover.html#popover-attributes)"}, "wd-popover/offset": {"type": "number", "description": "出现位置的偏移量, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/popover.html#popover-attributes)"}, "wd-popover/open": {"type": "event", "description": "显示时触发\n\n[Docs](https://wot-design-uni.cn/component/popover.html#events)"}, "wd-popover/close": {"type": "event", "description": "隐藏时触发\n\n[Docs](https://wot-design-uni.cn/component/popover.html#events)"}, "wd-popover/change": {"type": "event", "description": "pop 显隐值变化时触发\n\n[Docs](https://wot-design-uni.cn/component/popover.html#events)"}, "wd-popover/menuclick": {"type": "event", "description": "menu 模式下点击某一选项触发\n\n[Docs](https://wot-design-uni.cn/component/popover.html#events)"}, "wd-popup/v-model": {"type": "boolean", "description": "弹出层是否显示\n\n[Docs](https://wot-design-uni.cn/component/popup.html#attributes)"}, "wd-popup/position": {"type": "string", "options": ["center", "top", "right", "bottom", "left"], "description": "弹出位置, default: center.\n\n[Docs](https://wot-design-uni.cn/component/popup.html#attributes)"}, "wd-popup/closable": {"type": "boolean", "description": "关闭按钮, default: false.\n\n[Docs](https://wot-design-uni.cn/component/popup.html#attributes)"}, "wd-popup/close-on-click-modal": {"type": "boolean", "description": "点击遮罩是否关闭, default: true.\n\n[Docs](https://wot-design-uni.cn/component/popup.html#attributes)"}, "wd-popup/duration": {"type": "number | boolean", "description": "动画持续时间, default: 300(ms).\n\n[Docs](https://wot-design-uni.cn/component/popup.html#attributes)"}, "wd-popup/z-index": {"type": "number", "description": "设置层级, default: 10.\n\n[Docs](https://wot-design-uni.cn/component/popup.html#attributes)"}, "wd-popup/custom-style": {"type": "string", "description": "自定义弹出层样式\n\n[Docs](https://wot-design-uni.cn/component/popup.html#attributes)"}, "wd-popup/modal": {"type": "boolean", "description": "是否显示遮罩, default: true.\n\n[Docs](https://wot-design-uni.cn/component/popup.html#attributes)"}, "wd-popup/modal-style": {"type": "string", "description": "自定义modal蒙层样式\n\n[Docs](https://wot-design-uni.cn/component/popup.html#attributes)"}, "wd-popup/hide-when-close": {"type": "boolean", "description": "是否当关闭时将弹出层隐藏（display: none), default: true.\n\n[Docs](https://wot-design-uni.cn/component/popup.html#attributes)"}, "wd-popup/lazy-render": {"type": "boolean", "description": "弹层内容懒渲染，触发展示时才渲染内容, default: true.\n\n[Docs](https://wot-design-uni.cn/component/popup.html#attributes)"}, "wd-popup/safe-area-inset-bottom": {"type": "boolean", "description": "弹出面板是否设置底部安全距离（iphone X 类型的机型）, default: false.\n\n[Docs](https://wot-design-uni.cn/component/popup.html#attributes)"}, "wd-popup/transition": {"type": "string", "options": ["fade", "fade-up", "fade-down", "fade-left", "fade-right", "slide-up", "slide-down", "slide-left", "slide-right", "zoom-in"], "description": "动画类型，参见 wd-transition 组件的name\n\n[Docs](https://wot-design-uni.cn/component/popup.html#attributes)"}, "wd-popup/lock-scroll": {"type": "boolean", "description": "是否锁定背景滚动，锁定时蒙层里的内容也将无法滚动, default: true.\n\n[Docs](https://wot-design-uni.cn/component/popup.html#attributes)"}, "wd-popup/root-portal": {"type": "boolean", "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题, default: false.\n\n[Docs](https://wot-design-uni.cn/component/popup.html#attributes)"}, "wd-popup/close": {"type": "event", "description": "弹出层关闭时触发\n\n[Docs](https://wot-design-uni.cn/component/popup.html#events)"}, "wd-popup/click-modal": {"type": "event", "description": "点击遮罩时触发\n\n[Docs](https://wot-design-uni.cn/component/popup.html#events)"}, "wd-popup/before-enter": {"type": "event", "description": "进入前触发\n\n[Docs](https://wot-design-uni.cn/component/popup.html#events)"}, "wd-popup/enter": {"type": "event", "description": "进入时触发\n\n[Docs](https://wot-design-uni.cn/component/popup.html#events)"}, "wd-popup/after-enter": {"type": "event", "description": "进入后触发\n\n[Docs](https://wot-design-uni.cn/component/popup.html#events)"}, "wd-popup/before-leave": {"type": "event", "description": "离开前触发\n\n[Docs](https://wot-design-uni.cn/component/popup.html#events)"}, "wd-popup/leave": {"type": "event", "description": "离开时触发\n\n[Docs](https://wot-design-uni.cn/component/popup.html#events)"}, "wd-popup/after-leave": {"type": "event", "description": "离开后触发\n\n[Docs](https://wot-design-uni.cn/component/popup.html#events)"}, "wd-progress/percentage": {"type": "`number`", "description": "进度数值，最大值 100, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/progress.html#attributes)"}, "wd-progress/hide-text": {"type": "`boolean`", "description": "隐藏进度文字, default: false.\n\n[Docs](https://wot-design-uni.cn/component/progress.html#attributes)"}, "wd-progress/color": {"type": "`string | ProgressColor[] | string[]`", "description": "进度条颜色\n\n[Docs](https://wot-design-uni.cn/component/progress.html#attributes)"}, "wd-progress/status": {"type": "`string`", "options": ["success", "danger", "warning"], "description": "进度条状态\n\n[Docs](https://wot-design-uni.cn/component/progress.html#attributes)"}, "wd-progress/duration": {"type": "`number`", "description": "进度增加 1%所需毫秒数, default: 30.\n\n[Docs](https://wot-design-uni.cn/component/progress.html#attributes)"}, "wd-radio-group/v-model": {"type": "string | number | boolean", "description": "会自动选中value对应的单选框\n\n[Docs](https://wot-design-uni.cn/component/radio.html#radiogroup-attributes)"}, "wd-radio-group/shape": {"type": "string", "options": ["dot", "button", "check"], "description": "单选框形状, default: check.\n\n[Docs](https://wot-design-uni.cn/component/radio.html#radiogroup-attributes)"}, "wd-radio-group/size": {"type": "string", "options": ["large"], "description": "设置大小\n\n[Docs](https://wot-design-uni.cn/component/radio.html#radiogroup-attributes)"}, "wd-radio-group/checked-color": {"type": "string", "description": "选中的颜色, default: #4D80F0.\n\n[Docs](https://wot-design-uni.cn/component/radio.html#radiogroup-attributes)"}, "wd-radio-group/disabled": {"type": "boolean", "description": "禁用, default: false.\n\n[Docs](https://wot-design-uni.cn/component/radio.html#radiogroup-attributes)"}, "wd-radio-group/max-width": {"type": "string", "description": "文字位置最大宽度\n\n[Docs](https://wot-design-uni.cn/component/radio.html#radiogroup-attributes)"}, "wd-radio-group/inline": {"type": "boolean", "description": "同行展示, default: false.\n\n[Docs](https://wot-design-uni.cn/component/radio.html#radiogroup-attributes)"}, "wd-radio-group/cell": {"type": "boolean", "description": "表单模式, default: false.\n\n[Docs](https://wot-design-uni.cn/component/radio.html#radiogroup-attributes)"}, "wd-radio-group/icon-placement": {"type": "string", "options": ["left", "right", "auto"], "description": "勾选图标对齐方式, default: auto.\n\n[Docs](https://wot-design-uni.cn/component/radio.html#radiogroup-attributes)"}, "wd-radio-group/change": {"type": "event", "description": "绑定值变化时触发\n\n[Docs](https://wot-design-uni.cn/component/radio.html#radiogroup-events)"}, "wd-radio/value": {"type": "string | number | boolean", "description": "单选框选中时的值。会自动匹配radioGroup的value\n\n[Docs](https://wot-design-uni.cn/component/radio.html#radio-attributes)"}, "wd-radio/shape": {"type": "string", "options": ["dot", "button", "check"], "description": "单选框形状, default: check.\n\n[Docs](https://wot-design-uni.cn/component/radio.html#radio-attributes)"}, "wd-radio/checked-color": {"type": "string", "description": "选中的颜色, default: #4D80F0.\n\n[Docs](https://wot-design-uni.cn/component/radio.html#radio-attributes)"}, "wd-radio/disabled": {"type": "boolean", "description": "禁用, default: false.\n\n[Docs](https://wot-design-uni.cn/component/radio.html#radio-attributes)"}, "wd-rate/v-model": {"type": "number", "description": "当前分数\n\n[Docs](https://wot-design-uni.cn/component/rate.html#attributes)"}, "wd-rate/num": {"type": "number", "description": "评分最大值, default: 5.\n\n[Docs](https://wot-design-uni.cn/component/rate.html#attributes)"}, "wd-rate/readonly": {"type": "boolean", "description": "是否只读, default: false.\n\n[Docs](https://wot-design-uni.cn/component/rate.html#attributes)"}, "wd-rate/size": {"type": "string", "description": "图标大小, default: 16px.\n\n[Docs](https://wot-design-uni.cn/component/rate.html#attributes)"}, "wd-rate/space": {"type": "string", "description": "图标间距, default: 4px.\n\n[Docs](https://wot-design-uni.cn/component/rate.html#attributes)"}, "wd-rate/color": {"type": "string", "description": "未选中的图标颜色, default: #E8E8E8.\n\n[Docs](https://wot-design-uni.cn/component/rate.html#attributes)"}, "wd-rate/active-color": {"type": "string|array", "description": "选中的图标颜色(支持传颜色数组，共有 2 个元素，为 2 个分段所对应的颜色), default: linear-gradient(180deg, rgba(255,238,0,1) 0%,rgba(250,176,21,1) 100%).\n\n[Docs](https://wot-design-uni.cn/component/rate.html#attributes)"}, "wd-rate/icon": {"type": "string", "description": "未选中的图标类名, default: wd-icon-star-on.\n\n[Docs](https://wot-design-uni.cn/component/rate.html#attributes)"}, "wd-rate/active-icon": {"type": "string", "description": "选中的图标类名, default: wd-icon-star-on.\n\n[Docs](https://wot-design-uni.cn/component/rate.html#attributes)"}, "wd-rate/disabled": {"type": "boolean", "description": "是否禁用, default: false.\n\n[Docs](https://wot-design-uni.cn/component/rate.html#attributes)"}, "wd-rate/disabled-color": {"type": "string", "description": "禁用的图标颜色, default: linear-gradient(315deg, rgba(177,177,177,1) 0%,rgba(199,199,199,1) 100%).\n\n[Docs](https://wot-design-uni.cn/component/rate.html#attributes)"}, "wd-rate/allow-half": {"type": "boolean", "description": "是否允许半选, default: false.\n\n[Docs](https://wot-design-uni.cn/component/rate.html#attributes)"}, "wd-rate/change": {"type": "event", "description": "点击icon，修改分值事件\n\n[Docs](https://wot-design-uni.cn/component/rate.html#events)"}, "wd-resize/resize": {"type": "event", "description": "尺寸发生变化时触发\n\n[Docs](https://wot-design-uni.cn/component/resize.html#events)"}, "wd-search/placeholder": {"type": "string", "description": "搜索框占位文本, default: 搜索.\n\n[Docs](https://wot-design-uni.cn/component/search.html#attributes)"}, "wd-search/placeholder-left": {"type": "boolean", "description": "placeholder 居左边, default: false.\n\n[Docs](https://wot-design-uni.cn/component/search.html#attributes)"}, "wd-search/cancel-txt": {"type": "string", "description": "搜索框右侧文本, default: 取消.\n\n[Docs](https://wot-design-uni.cn/component/search.html#attributes)"}, "wd-search/light": {"type": "boolean", "description": "搜索框亮色（白色）, default: false.\n\n[Docs](https://wot-design-uni.cn/component/search.html#attributes)"}, "wd-search/hide-cancel": {"type": "boolean", "description": "是否隐藏右侧文本, default: false.\n\n[Docs](https://wot-design-uni.cn/component/search.html#attributes)"}, "wd-search/disabled": {"type": "boolean", "description": "是否禁用搜索框, default: false.\n\n[Docs](https://wot-design-uni.cn/component/search.html#attributes)"}, "wd-search/maxlength": {"type": "string | number", "description": "原生属性，设置最大长度。-1 表示无限制, default: -1.\n\n[Docs](https://wot-design-uni.cn/component/search.html#attributes)"}, "wd-search/v-model": {"type": "string", "description": "输入框内容，双向绑定\n\n[Docs](https://wot-design-uni.cn/component/search.html#attributes)"}, "wd-search/use-suffix-slot": {"type": "boolean", "description": "~~是否使用输入框右侧插槽~~**（已废弃，将在下一个 minor 版本被移除，直接使用插槽即可）**, default: false.\n\n[Docs](https://wot-design-uni.cn/component/search.html#attributes)"}, "wd-search/focus": {"type": "event", "description": "输入框聚焦事件\n\n[Docs](https://wot-design-uni.cn/component/search.html#events)"}, "wd-search/focus-when-clear": {"type": "boolean", "description": "是否在点击清除按钮时聚焦输入框, default: false.\n\n[Docs](https://wot-design-uni.cn/component/search.html#attributes)"}, "wd-search/placeholder-style": {"type": "string", "description": "原生属性，指定 placeholder 的样式，目前仅支持color,font-size和font-weight\n\n[Docs](https://wot-design-uni.cn/component/search.html#attributes)"}, "wd-search/placeholder-class": {"type": "string", "description": "原生属性，指定 placeholder 的样式类\n\n[Docs](https://wot-design-uni.cn/component/search.html#attributes)"}, "wd-search/blur": {"type": "event", "description": "监听输入框失焦事件\n\n[Docs](https://wot-design-uni.cn/component/search.html#events)"}, "wd-search/search": {"type": "event", "description": "监听输入框搜索事件\n\n[Docs](https://wot-design-uni.cn/component/search.html#events)"}, "wd-search/clear": {"type": "event", "description": "监听输入框清空按钮事件\n\n[Docs](https://wot-design-uni.cn/component/search.html#events)"}, "wd-search/cancel": {"type": "event", "description": "监听输入框右侧文本点击事件\n\n[Docs](https://wot-design-uni.cn/component/search.html#events)"}, "wd-search/change": {"type": "event", "description": "监听输入框内容变化事件\n\n[Docs](https://wot-design-uni.cn/component/search.html#events)"}, "wd-segmented/v-model:value": {"type": "string | number", "description": "当前选中的值\n\n[Docs](https://wot-design-uni.cn/component/segmented.html#attributes)"}, "wd-segmented/disabled": {"type": "boolean", "options": ["true", "false"], "description": "是否禁用分段器, default: false.\n\n[Docs](https://wot-design-uni.cn/component/segmented.html#attributes)"}, "wd-segmented/size": {"type": "string", "options": ["large", "middle", "small"], "description": "控件尺寸, default: middle.\n\n[Docs](https://wot-design-uni.cn/component/segmented.html#attributes)"}, "wd-segmented/options": {"type": "`string[] | number[] | SegmentedOption[]`", "description": "数据集合, default: [].\n\n[Docs](https://wot-design-uni.cn/component/segmented.html#attributes)"}, "wd-segmented/vibrate-short": {"type": "boolean", "options": ["true", "false"], "description": "切换选项时是否振动, default: false.\n\n[Docs](https://wot-design-uni.cn/component/segmented.html#attributes)"}, "wd-segmented/change": {"type": "event", "description": "选项切换时触发\n\n[Docs](https://wot-design-uni.cn/component/segmented.html#events)"}, "wd-segmented/click": {"type": "event", "description": "选项点击时触发\n\n[Docs](https://wot-design-uni.cn/component/segmented.html#events)"}, "wd-select-picker/v-model": {"type": "array|number|boolean|string", "description": "选中项，`type`为`checkbox`时类型为array；`type`为`radio`时类型为number/boolean/string\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/columns": {"type": "array", "description": "选择器数据，一维数组\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/type": {"type": "string", "options": ["checkbox", "radio"], "description": "单复选选择器类型, default: checkbox.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/value-key": {"type": "string", "description": "选项对象中value对应的key, default: value.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/label-key": {"type": "string", "description": "选项对象中展示文本对应的key, default: label.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/title": {"type": "string", "description": "弹出层标题\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/label": {"type": "string", "description": "选择器左侧文案\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/placeholder": {"type": "string", "description": "选择器占位符, default: 请选择.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/disabled": {"type": "boolean", "description": "禁用, default: false.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/loading": {"type": "boolean", "description": "加载中, default: false.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/loading-color": {"type": "String", "description": "加载颜色（十六进制，不能缩写）, default: #4D80F0.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/readonly": {"type": "boolean", "description": "只读, default: false.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/display-format": {"type": "function", "description": "自定义展示文案的格式化函数\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/confirm-button-text": {"type": "string", "description": "确认按钮文案, default: 确认.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/size": {"type": "string", "options": ["large"], "description": "选择器大小\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/label-width": {"type": "string", "description": "左侧标题宽度, default: 33%.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/error": {"type": "boolean", "description": "错误状态（右侧内容红色）, default: false.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/required": {"type": "boolean", "description": "必填样式, default: false.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/align-right": {"type": "boolean", "description": "值靠右展示, default: false.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/before-confirm": {"type": "function", "description": "确定前校验函数，接收(value,resolve)参数\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/select-size": {"type": "string", "options": ["large"], "description": "picker内部选项组尺寸\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/min": {"type": "number", "description": "最小选中数量（仅checkbox）, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/max": {"type": "number", "description": "最大选中数量（0为无限，仅checkbox）, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/checked-color": {"type": "string", "description": "选中颜色（单/复选框）, default: #4D80F0.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/use-default-slot": {"type": "boolean", "description": "使用默认插槽, default: false.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/use-label-slot": {"type": "boolean", "description": "使用label插槽, default: false.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/close-on-click-modal": {"type": "boolean", "description": "点击遮罩关闭, default: true.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/z-index": {"type": "number", "description": "弹窗层级, default: 15.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/safe-area-inset-bottom": {"type": "boolean", "description": "底部安全距离（iPhone X类机型）, default: true.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/filterable": {"type": "boolean", "description": "可搜索（仅本地）, default: false.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/filter-placeholder": {"type": "string", "description": "搜索框占位符, default: 搜索.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/ellipsis": {"type": "boolean", "description": "超出隐藏, default: false.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/scroll-into-view": {"type": "boolean", "description": "重新打开时滚动到选中项, default: true.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/show-confirm": {"type": "boolean", "description": "是否显示确认按钮（仅radio）, default: true.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/prop": {"type": "string", "description": "表单域model字段名（校验必填）\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/rules": {"type": "`FormItemRule[]`", "description": "表单验证规则（配合wd-form）, default: [].\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/clearable": {"type": "boolean", "description": "显示清空按钮, default: false.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/root-portal": {"type": "boolean", "description": "脱离页面解决fixed失效问题, default: false.\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#attributes)"}, "wd-select-picker/confirm": {"type": "event", "description": "点击确认时触发\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#events)"}, "wd-select-picker/change": {"type": "event", "description": "picker 内选项更改时触发\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#events)"}, "wd-select-picker/cancel": {"type": "event", "description": "点击关闭按钮或者蒙层时触发\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#events)"}, "wd-select-picker/close": {"type": "event", "description": "弹窗关闭时触发\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#events)"}, "wd-select-picker/open": {"type": "event", "description": "弹窗打开时触发\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#events)"}, "wd-select-picker/clear": {"type": "event", "description": "点击清空按钮时触发\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#events)"}, "wd-sidebar-item/label": {"type": "string", "description": "当前选项标题\n\n[Docs](https://wot-design-uni.cn/component/sidebar.html#sidebaritem-attributes)"}, "wd-sidebar-item/value": {"type": "`number | string`", "description": "当前选项的值，唯一标识\n\n[Docs](https://wot-design-uni.cn/component/sidebar.html#sidebaritem-attributes)"}, "wd-sidebar-item/icon": {"type": "string", "description": "图标\n\n[Docs](https://wot-design-uni.cn/component/sidebar.html#sidebaritem-attributes)"}, "wd-sidebar-item/badge": {"type": "`number | string | null`", "description": "徽标属性，徽标显示值\n\n[Docs](https://wot-design-uni.cn/component/sidebar.html#sidebaritem-attributes)"}, "wd-sidebar-item/is-dot": {"type": "boolean", "description": "徽标属性，是否点状徽标, default: false.\n\n[Docs](https://wot-design-uni.cn/component/sidebar.html#sidebaritem-attributes)"}, "wd-sidebar-item/max": {"type": "number", "description": "徽标属性，徽标最大值, default: 99.\n\n[Docs](https://wot-design-uni.cn/component/sidebar.html#sidebaritem-attributes)"}, "wd-sidebar-item/disabled": {"type": "boolean", "description": "是否禁用, default: false.\n\n[Docs](https://wot-design-uni.cn/component/sidebar.html#sidebaritem-attributes)"}, "wd-sidebar-item/badge-props": {"type": "BadgeProps", "description": "自定义徽标的属性，传入的对象会被透传给 [Badge 组件的 props](/component/badge#attributes)\n\n[Docs](https://wot-design-uni.cn/component/sidebar.html#sidebaritem-attributes)"}, "wd-sidebar/v-model": {"type": "string | number", "description": "当前导航项的索引, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/sidebar.html#attributes)"}, "wd-sidebar/before-change": {"type": "function", "description": "切换导航项前钩子，可以在切换标签前执行特定的逻辑，接收 { value, resolve } 参数，通过 resolve 继续执行，resolve 接收 1 个 boolean 参数\n\n[Docs](https://wot-design-uni.cn/component/sidebar.html#attributes)"}, "wd-sidebar/change": {"type": "event", "description": "选项切换时触发\n\n[Docs](https://wot-design-uni.cn/component/sidebar.html#events)"}, "wd-signature/pen-color": {"type": "string", "description": "签名笔颜色, default: #000000.\n\n[Docs](https://wot-design-uni.cn/component/signature.html#attributes)"}, "wd-signature/line-width": {"type": "number", "description": "签名笔宽度, default: 3.\n\n[Docs](https://wot-design-uni.cn/component/signature.html#attributes)"}, "wd-signature/height": {"type": "number", "description": "画布的高度, default: 200.\n\n[Docs](https://wot-design-uni.cn/component/signature.html#attributes)"}, "wd-signature/width": {"type": "number", "description": "画布的宽度, default: 300.\n\n[Docs](https://wot-design-uni.cn/component/signature.html#attributes)"}, "wd-signature/clear-text": {"type": "string", "description": "清空按钮的文本\n\n[Docs](https://wot-design-uni.cn/component/signature.html#attributes)"}, "wd-signature/confirm-text": {"type": "string", "description": "确认按钮的文本\n\n[Docs](https://wot-design-uni.cn/component/signature.html#attributes)"}, "wd-signature/file-type": {"type": "string", "description": "导出图片类型, default: png.\n\n[Docs](https://wot-design-uni.cn/component/signature.html#attributes)"}, "wd-signature/quality": {"type": "number", "description": "导出图片质量(0-1), default: 1.\n\n[Docs](https://wot-design-uni.cn/component/signature.html#attributes)"}, "wd-signature/export-scale": {"type": "number", "description": "导出图片的缩放比例, default: 1.\n\n[Docs](https://wot-design-uni.cn/component/signature.html#attributes)"}, "wd-signature/disabled": {"type": "boolean", "description": "是否禁用签名板, default: false.\n\n[Docs](https://wot-design-uni.cn/component/signature.html#attributes)"}, "wd-signature/background-color": {"type": "string", "description": "画板的背景色\n\n[Docs](https://wot-design-uni.cn/component/signature.html#attributes)"}, "wd-signature/disable-scroll": {"type": "boolean", "description": "是否禁用画布滚动, default: true.\n\n[Docs](https://wot-design-uni.cn/component/signature.html#attributes)"}, "wd-signature/enable-history": {"type": "boolean", "description": "是否开启历史记录, default: false.\n\n[Docs](https://wot-design-uni.cn/component/signature.html#attributes)"}, "wd-signature/step": {"type": "number", "description": "历史记录步长, default: 1.\n\n[Docs](https://wot-design-uni.cn/component/signature.html#attributes)"}, "wd-signature/pressure": {"type": "boolean", "description": "是否启用笔锋模式, default: false.\n\n[Docs](https://wot-design-uni.cn/component/signature.html#attributes)"}, "wd-signature/min-width": {"type": "number", "description": "笔锋模式最小宽度, default: 2.\n\n[Docs](https://wot-design-uni.cn/component/signature.html#attributes)"}, "wd-signature/max-width": {"type": "number", "description": "笔锋模式最大宽度, default: 6.\n\n[Docs](https://wot-design-uni.cn/component/signature.html#attributes)"}, "wd-signature/min-speed": {"type": "number", "description": "笔锋模式速度阈值, default: 1.5.\n\n[Docs](https://wot-design-uni.cn/component/signature.html#attributes)"}, "wd-signature/start": {"type": "event", "description": "开始签名时触发\n\n[Docs](https://wot-design-uni.cn/component/signature.html#events)"}, "wd-signature/end": {"type": "event", "description": "结束签名时触发\n\n[Docs](https://wot-design-uni.cn/component/signature.html#events)"}, "wd-signature/signing": {"type": "event", "description": "签名过程中触发\n\n[Docs](https://wot-design-uni.cn/component/signature.html#events)"}, "wd-signature/confirm": {"type": "event", "description": "确认签名时触发\n\n[Docs](https://wot-design-uni.cn/component/signature.html#events)"}, "wd-signature/clear": {"type": "event", "description": "清空签名时触发\n\n[Docs](https://wot-design-uni.cn/component/signature.html#events)"}, "wd-skeleton/theme": {"type": "SkeletonTheme", "options": ["text avatar paragraph image"], "description": "骨架图风格\n\n[Docs](https://wot-design-uni.cn/component/skeleton.html#attributes)"}, "wd-skeleton/row-col": {"type": "SkeletonRowCol", "description": "用于设置行列数量、宽度高度、间距等<br />【示例一】`[1, 1, 2]` 表示输出三行骨架图，第一行一列，第二行一列，第三行两列。 <br />【示例二】`[1, 1, { width: '100px' }]` 表示自定义第三行的宽度为 `100px`。 <br />【示例三】`[1, 2, [{ width, height }, { width, height, marginLeft }]]` 表示第三行有两列，且自定义宽度、高度和间距\n\n[Docs](https://wot-design-uni.cn/component/skeleton.html#attributes)"}, "wd-skeleton/loading": {"type": "boolean", "description": "是否为加载状态，如果是则显示骨架图，如果不是则显示加载完成的内容, default: true.\n\n[Docs](https://wot-design-uni.cn/component/skeleton.html#attributes)"}, "wd-skeleton/animation": {"type": "SkeletonAnimation", "options": ["gradient flashed"], "description": "动画效果\n\n[Docs](https://wot-design-uni.cn/component/skeleton.html#attributes)"}, "wd-slider/v-model": {"type": "number | array", "description": "滑块值，如果为array，则为双向滑块\n\n[Docs](https://wot-design-uni.cn/component/slider.html#attributes)"}, "wd-slider/hide-min-max": {"type": "boolean", "description": "是否显示左右的最大最小值, default: false.\n\n[Docs](https://wot-design-uni.cn/component/slider.html#attributes)"}, "wd-slider/hide-label": {"type": "boolean", "description": "是否显示当前滑块值, default: false.\n\n[Docs](https://wot-design-uni.cn/component/slider.html#attributes)"}, "wd-slider/disabled": {"type": "boolean", "description": "是否禁用, default: false.\n\n[Docs](https://wot-design-uni.cn/component/slider.html#attributes)"}, "wd-slider/max": {"type": "number", "description": "最大值, default: 100.\n\n[Docs](https://wot-design-uni.cn/component/slider.html#attributes)"}, "wd-slider/min": {"type": "number", "description": "最小值，允许负数`(1.2.19)`, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/slider.html#attributes)"}, "wd-slider/step": {"type": "number", "description": "步进值, default: 1.\n\n[Docs](https://wot-design-uni.cn/component/slider.html#attributes)"}, "wd-slider/active-color": {"type": "string", "description": "进度条激活背景颜色, default: linear-gradient(315deg, rgba(81,124,240,1) 0%,rgba(118,158,245,1) 100%).\n\n[Docs](https://wot-design-uni.cn/component/slider.html#attributes)"}, "wd-slider/inactive-color": {"type": "string", "description": "进度条未激活背景颜色, default: #e5e5e5.\n\n[Docs](https://wot-design-uni.cn/component/slider.html#attributes)"}, "wd-slider/dragstart": {"type": "event", "description": "开始移动时触发\n\n[Docs](https://wot-design-uni.cn/component/slider.html#events)"}, "wd-slider/dragmove": {"type": "event", "description": "移动滑块时触发\n\n[Docs](https://wot-design-uni.cn/component/slider.html#events)"}, "wd-slider/dragend": {"type": "event", "description": "移动结束时触发\n\n[Docs](https://wot-design-uni.cn/component/slider.html#events)"}, "wd-sort-button/v-model": {"type": "number", "options": ["-1,0,1"], "description": "选中的箭头方向：1 升序，0 重置状态，-1 降序。, default: 0或-1.\n\n[Docs](https://wot-design-uni.cn/component/sort-button.html#attributes)"}, "wd-sort-button/title": {"type": "string", "description": "排序按钮展示的文案。\n\n[Docs](https://wot-design-uni.cn/component/sort-button.html#attributes)"}, "wd-sort-button/allow-reset": {"type": "boolean", "description": "展示双箭头时，允许手动重置按钮。, default: false.\n\n[Docs](https://wot-design-uni.cn/component/sort-button.html#attributes)"}, "wd-sort-button/desc-first": {"type": "boolean", "description": "优先切换为降序，不开启则默认优先切换为升序, default: false.\n\n[Docs](https://wot-design-uni.cn/component/sort-button.html#attributes)"}, "wd-sort-button/line": {"type": "boolean", "description": "展示下划线，当只有一个排序按钮时，应该不展示下划线, default: true.\n\n[Docs](https://wot-design-uni.cn/component/sort-button.html#attributes)"}, "wd-sort-button/change": {"type": "event", "description": "监听排序修改\n\n[Docs](https://wot-design-uni.cn/component/sort-button.html#events)"}, "wd-status-tip/image": {"type": "string", "options": ["search", "network", "content", "collect", "comment", "halo", "message"], "description": "缺省图片类型，支持传入图片 URL, default: network.\n\n[Docs](https://wot-design-uni.cn/component/status-tip.html#attributes)"}, "wd-status-tip/image-size": {"type": "`string`|`number`|`ImageSize`", "description": "图片大小，默认单位为 `px`\n\n[Docs](https://wot-design-uni.cn/component/status-tip.html#attributes)"}, "wd-status-tip/tip": {"type": "string", "description": "提示文案\n\n[Docs](https://wot-design-uni.cn/component/status-tip.html#attributes)"}, "wd-status-tip/image-mode": {"type": "`ImageMode`", "description": "预览图片的 mode 属性, default: aspectFit.\n\n[Docs](https://wot-design-uni.cn/component/status-tip.html#attributes)"}, "wd-status-tip/url-prefix": {"type": "string", "description": "图片路径前缀，指向图片所在目录，用于拼接图片 URL。, default: https://registry.npmmirror.com/wot-design-uni-assets//files/.\n\n[Docs](https://wot-design-uni.cn/component/status-tip.html#attributes)"}, "wd-steps/active": {"type": "number", "description": "步骤进度, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/steps.html#steps-attributes)"}, "wd-steps/vertical": {"type": "boolean", "description": "垂直方向, default: false.\n\n[Docs](https://wot-design-uni.cn/component/steps.html#steps-attributes)"}, "wd-steps/dot": {"type": "dot", "description": "点状步骤条, default: false.\n\n[Docs](https://wot-design-uni.cn/component/steps.html#steps-attributes)"}, "wd-steps/space": {"type": "string", "description": "步骤条间距，默认为自动计算\n\n[Docs](https://wot-design-uni.cn/component/steps.html#steps-attributes)"}, "wd-steps/align-center": {"type": "boolean", "description": "是否水平居中，只对横向步骤条有效, default: false.\n\n[Docs](https://wot-design-uni.cn/component/steps.html#steps-attributes)"}, "wd-step/title": {"type": "string", "description": "标题，如果没有则为默认文案。当只有标题而没有描述时，标题的字号会小2号\n\n[Docs](https://wot-design-uni.cn/component/steps.html#step-attributes)"}, "wd-step/stitle-slots": {"type": "boolean", "description": "<s> 使用 title 插槽时需要设置该属性</s>，已废弃，直接使用title插槽即可, default: false.\n\n[Docs](https://wot-design-uni.cn/component/steps.html#step-attributes)"}, "wd-step/description": {"type": "string", "description": "描述\n\n[Docs](https://wot-design-uni.cn/component/steps.html#step-attributes)"}, "wd-step/sdescription-slots": {"type": "boolean", "description": "<s>使用 description 插槽时需要设置该属性</s>，已废弃，直接使用description插槽即可, default: false.\n\n[Docs](https://wot-design-uni.cn/component/steps.html#step-attributes)"}, "wd-step/icon": {"type": "string", "description": "图标\n\n[Docs](https://wot-design-uni.cn/component/steps.html#step-attributes)"}, "wd-step/sicon-slots": {"type": "boolean", "description": "<s>使用 icon 插槽时需要设置该属性</s>，已废弃，直接使用icon插槽即可, default: false.\n\n[Docs](https://wot-design-uni.cn/component/steps.html#step-attributes)"}, "wd-step/status": {"type": "string", "options": ["finished", "process", "error"], "description": "步骤状态\n\n[Docs](https://wot-design-uni.cn/component/steps.html#step-attributes)"}, "wd-sticky/z-index": {"type": "number", "description": "堆叠顺序, default: 1.\n\n[Docs](https://wot-design-uni.cn/component/sticky.html#sticky-attributes)"}, "wd-sticky/offset-top": {"type": "number", "description": "吸顶距离, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/sticky.html#sticky-attributes)"}, "wd-swipe-action/v-model": {"type": "string", "options": ["left", "close", "right"], "description": "滑动按钮的状态, default: close.\n\n[Docs](https://wot-design-uni.cn/component/swipe-action.html#attributes)"}, "wd-swipe-action/disabled": {"type": "boolean", "description": "是否禁用滑动操作, default: false.\n\n[Docs](https://wot-design-uni.cn/component/swipe-action.html#attributes)"}, "wd-swipe-action/before-close": {"type": "function", "description": "关闭滑动按钮前的钩子函数\n\n[Docs](https://wot-design-uni.cn/component/swipe-action.html#attributes)"}, "wd-swipe-action/click": {"type": "event", "description": "当滑动按钮打开时，点击整个滑动操作容器触发 click 事件\n\n[Docs](https://wot-design-uni.cn/component/swipe-action.html#events)"}, "wd-swiper/autoplay": {"type": "`boolean`", "description": "是否自动播放, default: true.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/v-model:current": {"type": "`number`", "description": "控制当前轮播在哪一项（下标）, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/direction": {"type": "`DirectionType`", "options": ["horizontal, vertical"], "description": "轮播滑动方向, default: horizontal.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/display-multiple-items": {"type": "`number`", "description": "同时显示的滑块数量, default: 1.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/duration": {"type": "`number`", "description": "滑动动画时长, default: 300.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/easing-function": {"type": "`EasingType`", "description": "切换缓动动画类型（微信小程序、快手小程序、京东小程序）, default: default.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/height": {"type": "`string | number`", "description": "轮播的高度, default: 192.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/interval": {"type": "`number`", "description": "轮播间隔时间, default: 5000.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/list": {"type": "`string[] | SwiperList[]`", "description": "图片列表\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/loop": {"type": "`boolean`", "description": "是否循环播放, default: true.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/next-margin": {"type": "`string | number`", "description": "后边距, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/indicator-position": {"type": "`IndicatorPositionType`", "options": ["left, top-left, top, top-right, bottom-left, bottom, bottom-right, right"], "description": "指示器展示位置, default: bottom.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/previous-margin": {"type": "`string | number`", "description": "前边距, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/snap-to-edge": {"type": "`boolean`", "description": "边距是否应用到第一个、最后一个元素, default: false.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/indicator": {"type": "`SwiperIndicatorProps | boolean`", "description": "指示器全部配置, default: true.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/image-mode": {"type": "`string`", "options": ["参考官方文档[mode](https:", "", "uniapp.dcloud.net.cn", "component", "image.html#mode-%E6%9C%89%E6%95%88%E5%80%BC)"], "description": "图片裁剪、缩放的模式, default: aspectFill.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/autoplay-video": {"type": "`boolean`", "description": "视频是否自动播放，默认自动播放, default: true.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/stop-previous-video": {"type": "`boolean`", "description": "切换轮播项时是否停止上一个视频的播放，默认切换时停止播放上一个视频, default: true.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/stop-autoplay-when-video-play": {"type": "`boolean`", "description": "视频播放时是否停止自动轮播, default: false.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/custom-style": {"type": "`string`", "description": "外部自定义样式\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/value-key": {"type": "`string`", "description": "选项对象中，value 对应的 key, default: value.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/text-key": {"type": "`string`", "description": "选项对象中，标题 text 对应的 key, default: text.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/adjust-height": {"type": "`string`", "options": ["first'", "'current'", "'highest'", "'none"], "description": "自动以指定滑块的高度为整个容器的高度。当 vertical 为 true 时，默认不调整，仅支付宝小程序支持。, default: highest.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/adjust-vertical-height": {"type": "`boolean`", "description": "vertical 为 true 时强制使 adjust-height 生效。仅支付宝小程序支持。, default: false.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/ muted": {"type": "`boolean`", "description": "视频是否静音播放, default: true.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/video-loop": {"type": "`boolean`", "description": "视频是否循环播放, default: true.\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#attributes)"}, "wd-swiper/click": {"type": "event", "description": "点击轮播项时触发\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#events)"}, "wd-swiper/change": {"type": "event", "description": "轮播切换时触发\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#events)"}, "wd-switch/v-model": {"type": "boolean | string | number", "description": "绑定值\n\n[Docs](https://wot-design-uni.cn/component/switch.html#attributes)"}, "wd-switch/disabled": {"type": "boolean", "description": "禁用, default: false.\n\n[Docs](https://wot-design-uni.cn/component/switch.html#attributes)"}, "wd-switch/active-value": {"type": "boolean | string | number", "description": "打开时的值, default: true.\n\n[Docs](https://wot-design-uni.cn/component/switch.html#attributes)"}, "wd-switch/inactive-value": {"type": "boolean | string | number", "description": "关闭时的值, default: false.\n\n[Docs](https://wot-design-uni.cn/component/switch.html#attributes)"}, "wd-switch/active-color": {"type": "string", "description": "打开时的背景色, default: #4D80F0.\n\n[Docs](https://wot-design-uni.cn/component/switch.html#attributes)"}, "wd-switch/inactive-color": {"type": "string", "description": "关闭时的背景色，默认为白色，所以有灰色边框，如果设置了该值，则会自动去除灰色边框, default: #fff.\n\n[Docs](https://wot-design-uni.cn/component/switch.html#attributes)"}, "wd-switch/size": {"type": "string|number", "description": "开关大小，可以为任何单位的字符串尺寸, default: 28px.\n\n[Docs](https://wot-design-uni.cn/component/switch.html#attributes)"}, "wd-switch/before-change": {"type": "function", "description": "修改前钩子\n\n[Docs](https://wot-design-uni.cn/component/switch.html#attributes)"}, "wd-switch/change": {"type": "event", "description": "值修改事件\n\n[Docs](https://wot-design-uni.cn/component/switch.html#events)"}, "wd-tabbar-item/title": {"type": "string", "description": "标签页的标题\n\n[Docs](https://wot-design-uni.cn/component/tabbar.html#tabbaritem-attributes)"}, "wd-tabbar-item/name": {"type": "string | number", "description": "唯一标识符\n\n[Docs](https://wot-design-uni.cn/component/tabbar.html#tabbaritem-attributes)"}, "wd-tabbar-item/icon": {"type": "string", "description": "图标\n\n[Docs](https://wot-design-uni.cn/component/tabbar.html#tabbaritem-attributes)"}, "wd-tabbar-item/value": {"type": "number | string", "description": "徽标显示值\n\n[Docs](https://wot-design-uni.cn/component/tabbar.html#tabbaritem-attributes)"}, "wd-tabbar-item/is-dot": {"type": "boolean", "description": "是否点状徽标, default: false.\n\n[Docs](https://wot-design-uni.cn/component/tabbar.html#tabbaritem-attributes)"}, "wd-tabbar-item/max": {"type": "number", "description": "徽标最大值, default: 99.\n\n[Docs](https://wot-design-uni.cn/component/tabbar.html#tabbaritem-attributes)"}, "wd-tabbar-item/badge-props": {"type": "BadgeProps", "description": "自定义徽标的属性，传入的对象会被透传给 [Badge 组件的 props](/component/badge#attributes)\n\n[Docs](https://wot-design-uni.cn/component/tabbar.html#tabbaritem-attributes)"}, "wd-tabbar/v-model": {"type": "number | string", "description": "选中标签的索引值或者名称, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/tabbar.html#attributes)"}, "wd-tabbar/fixed": {"type": "boolean", "description": "是否固定在底部, default: false.\n\n[Docs](https://wot-design-uni.cn/component/tabbar.html#attributes)"}, "wd-tabbar/safe-area-inset-bottom": {"type": "boolean", "description": "是否设置底部安全距离（iPhone X 类型的机型）, default: false.\n\n[Docs](https://wot-design-uni.cn/component/tabbar.html#attributes)"}, "wd-tabbar/bordered": {"type": "boolean", "description": "是否显示顶部边框, default: true.\n\n[Docs](https://wot-design-uni.cn/component/tabbar.html#attributes)"}, "wd-tabbar/shape": {"type": "<PERSON><PERSON>r<PERSON>ha<PERSON>", "options": ["default'", "'round"], "description": "标签栏的形状, default: default.\n\n[Docs](https://wot-design-uni.cn/component/tabbar.html#attributes)"}, "wd-tabbar/active-color": {"type": "string", "description": "激活标签的颜色\n\n[Docs](https://wot-design-uni.cn/component/tabbar.html#attributes)"}, "wd-tabbar/inactive-color": {"type": "string", "description": "未激活标签的颜色\n\n[Docs](https://wot-design-uni.cn/component/tabbar.html#attributes)"}, "wd-tabbar/placeholder": {"type": "boolean", "description": "固定在底部时，是否在标签位置生成一个等高的占位元素, default: false.\n\n[Docs](https://wot-design-uni.cn/component/tabbar.html#attributes)"}, "wd-tabbar/z-index": {"type": "number", "description": "tabbar组件的层级, default: 500.\n\n[Docs](https://wot-design-uni.cn/component/tabbar.html#attributes)"}, "wd-tabbar/change": {"type": "event", "description": "tabbar标签切换时触发\n\n[Docs](https://wot-design-uni.cn/component/tabbar.html#events)"}, "wd-table-column/prop": {"type": "string", "description": "字段名称,对应列内容的字段名\n\n[Docs](https://wot-design-uni.cn/component/table.html#tablecolumn-attributes)"}, "wd-table-column/label": {"type": "string", "description": "显示的标题\n\n[Docs](https://wot-design-uni.cn/component/table.html#tablecolumn-attributes)"}, "wd-table-column/width": {"type": "number | string", "description": "对应列的宽度，单位为 px, default: 100.\n\n[Docs](https://wot-design-uni.cn/component/table.html#tablecolumn-attributes)"}, "wd-table-column/sortable": {"type": "boolean", "description": "是否开启列排序, default: false.\n\n[Docs](https://wot-design-uni.cn/component/table.html#tablecolumn-attributes)"}, "wd-table-column/fixed": {"type": "boolean", "description": "是否固定本列, default: false.\n\n[Docs](https://wot-design-uni.cn/component/table.html#tablecolumn-attributes)"}, "wd-table-column/align": {"type": "AlignType", "options": ["left, center, right"], "description": "列的对齐方式, default: left.\n\n[Docs](https://wot-design-uni.cn/component/table.html#tablecolumn-attributes)"}, "wd-table/data": {"type": "Array", "description": "显示的数据\n\n[Docs](https://wot-design-uni.cn/component/table.html#attributes)"}, "wd-table/border": {"type": "boolean", "description": "是否带有边框, default: true.\n\n[Docs](https://wot-design-uni.cn/component/table.html#attributes)"}, "wd-table/stripe": {"type": "boolean", "description": "是否为斑马纹表, default: true.\n\n[Docs](https://wot-design-uni.cn/component/table.html#attributes)"}, "wd-table/height": {"type": "`number | string`", "description": "Table 的高度，无默认值，设置后自动开启固定表头。\n\n[Docs](https://wot-design-uni.cn/component/table.html#attributes)"}, "wd-table/row-height": {"type": "`number | string`", "description": "行高, default: 50.\n\n[Docs](https://wot-design-uni.cn/component/table.html#attributes)"}, "wd-table/show-header": {"type": "boolean", "description": "是否显示表头, default: true.\n\n[Docs](https://wot-design-uni.cn/component/table.html#attributes)"}, "wd-table/ellipsis": {"type": "boolean", "description": "是否超出 2 行隐藏, default: true.\n\n[Docs](https://wot-design-uni.cn/component/table.html#attributes)"}, "wd-table/index": {"type": "`boolean | TableColumnProps`", "description": "是否显示索引列，可传入`boolean`也可传入 column 配置, default: false.\n\n[Docs](https://wot-design-uni.cn/component/table.html#attributes)"}, "wd-table/fixed-header": {"type": "boolean", "description": "是否固定表头，需要结合`height`才可以实现固定表头的效果。, default: true.\n\n[Docs](https://wot-design-uni.cn/component/table.html#attributes)"}, "wd-table/sort-method": {"type": "event", "description": "指定数据按照哪个属性进行排序，仅当 sortable 设置为 true 的时候有效\n\n[Docs](https://wot-design-uni.cn/component/table.html#events)"}, "wd-table/row-click": {"type": "event", "description": "当某一行被点击时会触发该事件\n\n[Docs](https://wot-design-uni.cn/component/table.html#events)"}, "wd-tabs/v-model": {"type": "string | number", "description": "绑定值\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tabs-attributes)"}, "wd-tabs/slidable-num": {"type": "number", "description": "可滑动的标签数阈值，`slidable`设置为`auto`时生效, default: 6.\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tabs-attributes)"}, "wd-tabs/map-num": {"type": "number", "description": "显示导航地图的标签数阈值, default: 10.\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tabs-attributes)"}, "wd-tabs/map-title": {"type": "string", "description": "导航地图标题\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tabs-attributes)"}, "wd-tabs/sticky": {"type": "boolean", "description": "粘性布局, default: false.\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tabs-attributes)"}, "wd-tabs/offset-top": {"type": "number", "description": "粘性布局时距离窗口顶部距离, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tabs-attributes)"}, "wd-tabs/swipeable": {"type": "boolean", "description": "开启手势滑动, default: false.\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tabs-attributes)"}, "wd-tabs/auto-line-width": {"type": "boolean", "description": "底部条宽度跟随文字，指定`lineWidth`时此选项不生效, default: false.\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tabs-attributes)"}, "wd-tabs/line-width": {"type": "number", "description": "底部条宽度，单位像素, default: 19.\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tabs-attributes)"}, "wd-tabs/line-height": {"type": "number", "description": "底部条高度，单位像素, default: 3.\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tabs-attributes)"}, "wd-tabs/color": {"type": "string", "description": "文字颜色\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tabs-attributes)"}, "wd-tabs/inactive-color": {"type": "string", "description": "非活动标签文字颜色\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tabs-attributes)"}, "wd-tabs/animated": {"type": "boolean", "description": "是否开启切换标签内容时的转场动画, default: false.\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tabs-attributes)"}, "wd-tabs/duration": {"type": "number", "description": "切换动画过渡时间，单位毫秒, default: 300.\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tabs-attributes)"}, "wd-tabs/slidable": {"type": "TabsSlidable", "options": ["always"], "description": "是否开启滚动导航, default: auto.\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tabs-attributes)"}, "wd-tabs/badge-props": {"type": "BadgeProps", "description": "自定义徽标的属性，传入的对象会被透传给 [Badge 组件的 props](/component/badge#attributes)\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tabs-attributes)"}, "wd-tabs/change": {"type": "event", "description": "绑定值变化时触发\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tabs-events)"}, "wd-tabs/click": {"type": "event", "description": "点击标题时触发\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tabs-events)"}, "wd-tabs/disabled": {"type": "event", "description": "点击禁用的标题时触发\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tabs-events)"}, "wd-tab/name": {"type": "string", "description": "标签页名称\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tab-attributes)"}, "wd-tab/title": {"type": "string", "description": "标题\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tab-attributes)"}, "wd-tab/disabled": {"type": "boolean", "description": "禁用, default: false.\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tab-attributes)"}, "wd-tab/lazy": {"type": "boolean", "description": "延迟渲染，默认开启，开启`animated`后此选项始终为`false`, default: true.\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tab-attributes)"}, "wd-tag/type": {"type": "string", "options": ["default", "primary", "danger", "warning", "success"], "description": "标签类型, default: default.\n\n[Docs](https://wot-design-uni.cn/component/tag.html#attributes)"}, "wd-tag/plain": {"type": "boolean", "description": "幽灵类型, default: false.\n\n[Docs](https://wot-design-uni.cn/component/tag.html#attributes)"}, "wd-tag/mark": {"type": "boolean", "description": "标记类型, default: false.\n\n[Docs](https://wot-design-uni.cn/component/tag.html#attributes)"}, "wd-tag/round": {"type": "boolean", "description": "圆角类型, default: false.\n\n[Docs](https://wot-design-uni.cn/component/tag.html#attributes)"}, "wd-tag/icon": {"type": "string", "description": "左侧图标\n\n[Docs](https://wot-design-uni.cn/component/tag.html#attributes)"}, "wd-tag/color": {"type": "string", "description": "文字颜色\n\n[Docs](https://wot-design-uni.cn/component/tag.html#attributes)"}, "wd-tag/bg-color": {"type": "string", "description": "背景色和边框色\n\n[Docs](https://wot-design-uni.cn/component/tag.html#attributes)"}, "wd-tag/closable": {"type": "boolean", "description": "可关闭(只对圆角类型支持), default: false.\n\n[Docs](https://wot-design-uni.cn/component/tag.html#attributes)"}, "wd-tag/use-icon-slot": {"type": "boolean", "description": "开启图标插槽, default: false.\n\n[Docs](https://wot-design-uni.cn/component/tag.html#attributes)"}, "wd-tag/dynamic": {"type": "boolean", "description": "是否为新增标签, default: false.\n\n[Docs](https://wot-design-uni.cn/component/tag.html#attributes)"}, "wd-tag/click": {"type": "event", "description": "标签点击时触发\n\n[Docs](https://wot-design-uni.cn/component/tag.html#events)"}, "wd-tag/close": {"type": "event", "description": "点击关闭按钮时触发\n\n[Docs](https://wot-design-uni.cn/component/tag.html#events)"}, "wd-tag/confirm": {"type": "event", "description": "新增标签输入内容确定后触发\n\n[Docs](https://wot-design-uni.cn/component/tag.html#events)"}, "wd-text/type": {"type": "string", "options": ["primary'", "'error'", "'warning'", "'success"], "description": "主题类型, default: default.\n\n[Docs](https://wot-design-uni.cn/component/text.html#attributes)"}, "wd-text/text": {"type": "string | number", "description": "文字\n\n[Docs](https://wot-design-uni.cn/component/text.html#attributes)"}, "wd-text/size": {"type": "string", "description": "字体大小\n\n[Docs](https://wot-design-uni.cn/component/text.html#attributes)"}, "wd-text/mode": {"type": "string", "options": ["text-普通文本'", "'date - 日期'", "'phone - 手机号'", "'name - 姓名'", "'price - 金额"], "description": "文本处理的匹配模式, default: text.\n\n[Docs](https://wot-design-uni.cn/component/text.html#attributes)"}, "wd-text/bold": {"type": "boolean", "description": "是否粗体，默认 normal, default: false.\n\n[Docs](https://wot-design-uni.cn/component/text.html#attributes)"}, "wd-text/format": {"type": "boolean", "options": ["当 mode 为 phone 和 name 时生效"], "description": "是否脱敏, default: false.\n\n[Docs](https://wot-design-uni.cn/component/text.html#attributes)"}, "wd-text/color": {"type": "string", "description": "文字颜色\n\n[Docs](https://wot-design-uni.cn/component/text.html#attributes)"}, "wd-text/lines": {"type": "Number", "description": "文本显示的行数，如果设置，超出此行数，将会显示省略号。最大值为 5。\n\n[Docs](https://wot-design-uni.cn/component/text.html#attributes)"}, "wd-text/line-height": {"type": "string", "description": "文本行高\n\n[Docs](https://wot-design-uni.cn/component/text.html#attributes)"}, "wd-text/decoration": {"type": "string", "options": ["underline", "line-through", "overline"], "description": "文字装饰，下划线，中划线等\n\n[Docs](https://wot-design-uni.cn/component/text.html#attributes)"}, "wd-text/prefix": {"type": "string", "description": "前置插槽\n\n[Docs](https://wot-design-uni.cn/component/text.html#attributes)"}, "wd-text/suffix": {"type": "string", "description": "后置插槽\n\n[Docs](https://wot-design-uni.cn/component/text.html#attributes)"}, "wd-text/click": {"type": "event", "description": "标签点击时触发\n\n[Docs](https://wot-design-uni.cn/component/text.html#events)"}, "wd-textarea/v-model": {"type": "string | number", "description": "绑定值\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/placeholder": {"type": "string", "description": "占位文本, default: 请输入....\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/placeholder-style": {"type": "string", "description": "原生属性，指定 placeholder 的样式\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/placeholder-class": {"type": "string", "description": "原生属性，指定 placeholder 的样式类\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/disabled": {"type": "boolean", "description": "原生属性，禁用, default: false.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/maxlength": {"type": "number", "description": "原生属性，最大输入长度，设置为 -1 时不限制最大长度\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/auto-focus": {"type": "boolean", "description": "原生属性，自动聚焦，拉起键盘, default: false.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/focus": {"type": "event", "description": "监听输入框 focus 事件\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#events)"}, "wd-textarea/auto-height": {"type": "boolean", "description": "原生属性，是否自动增高（设置时 style.height 不生效）, default: false.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/fixed": {"type": "boolean", "description": "在 position:fixed 区域时需要设置为 true, default: false.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/cursor-spacing": {"type": "number", "description": "原生属性，指定光标与键盘的距离（取 textarea 底部距离和该值的最小值）, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/cursor": {"type": "number", "description": "原生属性，指定 focus 时的光标位置, default: -1.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/confirm-type": {"type": "string", "options": ["done", "go", "next", "search", "send"], "description": "设置键盘右下角按钮的文字\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/confirm-hold": {"type": "boolean", "description": "点击键盘右下角按钮时是否保持键盘不收起, default: false.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/show-confirm-bar": {"type": "boolean", "description": "是否显示键盘上方\"完成\"栏, default: true.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/selection-start": {"type": "number", "description": "原生属性，光标起始位置（需与 selection-end 搭配使用）, default: -1.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/selection-end": {"type": "number", "description": "原生属性，光标结束位置（需与 selection-start 搭配使用）, default: -1.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/adjust-position": {"type": "boolean", "description": "原生属性，键盘弹起时是否自动上推页面, default: true.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/disable-default-padding": {"type": "boolean", "description": "原生属性，是否去掉 iOS 默认内边距, default: false.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/hold-keyboard": {"type": "boolean", "description": "原生属性，focus 时点击页面不收起键盘, default: false.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/show-password": {"type": "boolean", "description": "显示为密码框, default: false.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/clearable": {"type": "boolean", "description": "显示清空按钮, default: false.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/readonly": {"type": "boolean", "description": "只读, default: false.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/prefix-icon": {"type": "string", "description": "前置图标（使用 icon 组件类名）\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/show-word-limit": {"type": "boolean", "description": "显示字数限制（需设置 maxlength）, default: false.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/label": {"type": "string", "description": "设置左侧标题\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/label-width": {"type": "string", "description": "设置左侧标题宽度, default: 33%.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/size": {"type": "string", "description": "设置输入框大小\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/error": {"type": "boolean", "description": "设置输入框错误状态（红色标识）, default: false.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/center": {"type": "boolean", "description": "有 label 时设置标题和输入框垂直居中（默认顶部居中）, default: false.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/no-border": {"type": "boolean", "description": "非 cell 类型下是否隐藏下划线, default: false.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/required": {"type": "boolean", "description": "cell 类型下必填样式, default: false.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/prop": {"type": "string", "description": "表单域 `model` 字段名（表单校验必填）\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/rules": {"type": "FormItemRule[]", "description": "表单验证规则, default: [].\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/clear-trigger": {"type": "InputClearTrigger", "options": ["focus", "always"], "description": "显示清除图标的时机：always（输入框非空时展示）/ focus（聚焦且非空时展示）, default: always.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/focus-when-clear": {"type": "boolean", "description": "点击清除按钮时是否聚焦输入框, default: true.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/ignore-composition-event": {"type": "boolean", "description": "是否忽略文本合成系统事件处理（为 false 时触发 composition 相关事件，且在合成期间触发 input 事件）, default: true.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/inputmode": {"type": "InputMode", "description": "输入数据类型提示, default: text.\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#attributes)"}, "wd-textarea/input": {"type": "event", "description": "监听输入框 input 事件\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#events)"}, "wd-textarea/blur": {"type": "event", "description": "监听输入框 blur 事件\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#events)"}, "wd-textarea/clear": {"type": "event", "description": "监听输入框清空按钮事件\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#events)"}, "wd-textarea/linechange": {"type": "event", "description": "监听输入框行数变化\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#events)"}, "wd-textarea/confirm": {"type": "event", "description": "点击完成时， 触发 confirm 事件\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#events)"}, "wd-textarea/keyboardheightchange": {"type": "event", "description": "键盘高度发生变化的时候触发此事件\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#events)"}, "wd-textarea/clickprefixicon": {"type": "event", "description": "点击前置图标时触发\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#events)"}, "wd-textarea/clicksuffixicon": {"type": "event", "description": "点击后置图标时触发\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#events)"}, "wd-toast/selector": {"type": "string", "description": "选择器\n\n[Docs](https://wot-design-uni.cn/component/toast.html#attributes)"}, "wd-toast/msg": {"type": "string", "description": "提示信息\n\n[Docs](https://wot-design-uni.cn/component/toast.html#attributes)"}, "wd-toast/direction": {"type": "string", "options": ["vertical", "horizontal"], "description": "排列方向, default: horizontal.\n\n[Docs](https://wot-design-uni.cn/component/toast.html#attributes)"}, "wd-toast/icon-name": {"type": "string", "options": ["success", "error", "warning", "loading", "info"], "description": "图标类型\n\n[Docs](https://wot-design-uni.cn/component/toast.html#attributes)"}, "wd-toast/icon-size": {"type": "number", "description": "图标大小\n\n[Docs](https://wot-design-uni.cn/component/toast.html#attributes)"}, "wd-toast/loading-type": {"type": "string", "options": ["outline", "ring"], "description": "加载类型, default: outline.\n\n[Docs](https://wot-design-uni.cn/component/toast.html#attributes)"}, "wd-toast/loading-color": {"type": "string", "description": "加载颜色, default: #4D80F0.\n\n[Docs](https://wot-design-uni.cn/component/toast.html#attributes)"}, "wd-toast/loading-size": {"type": "number", "description": "加载大小\n\n[Docs](https://wot-design-uni.cn/component/toast.html#attributes)"}, "wd-toast/icon-color": {"type": "string", "description": "图标颜色\n\n[Docs](https://wot-design-uni.cn/component/toast.html#attributes)"}, "wd-toast/position": {"type": "string", "options": ["top", "middle-top", "middle", "bottom"], "description": "提示信息框的位置, default: middle-top.\n\n[Docs](https://wot-design-uni.cn/component/toast.html#attributes)"}, "wd-toast/z-index": {"type": "number", "description": "层级, default: 100.\n\n[Docs](https://wot-design-uni.cn/component/toast.html#attributes)"}, "wd-toast/cover": {"type": "boolean", "description": "是否存在遮罩层, default: false.\n\n[Docs](https://wot-design-uni.cn/component/toast.html#attributes)"}, "wd-toast/icon-class": {"type": "string", "description": "图标类名\n\n[Docs](https://wot-design-uni.cn/component/toast.html#attributes)"}, "wd-toast/class-prefix": {"type": "string", "description": "类名前缀，用于使用自定义图标, default: wd-icon.\n\n[Docs](https://wot-design-uni.cn/component/toast.html#attributes)"}, "wd-toast/opened": {"type": "Function", "description": "完全展示后的回调函数\n\n[Docs](https://wot-design-uni.cn/component/toast.html#attributes)"}, "wd-toast/closed": {"type": "Function", "description": "完全关闭时的回调函数\n\n[Docs](https://wot-design-uni.cn/component/toast.html#attributes)"}, "wd-tooltip/show": {"type": "boolean", "description": "状态是否可见, default: false.\n\n[Docs](https://wot-design-uni.cn/component/tooltip.html#attributes)"}, "wd-tooltip/content": {"type": "string | array", "description": "显示的内容，也可以通过 `slot#content` 传入\n\n[Docs](https://wot-design-uni.cn/component/tooltip.html#attributes)"}, "wd-tooltip/placement": {"type": "string", "options": ["top", "top-start", "top-end", "bottom", "bottom-start", "bottom-end", "left", "left-start", "left-end", "right", "right-start", "right-end"], "description": "Tooltip 的出现位置, default: bottom.\n\n[Docs](https://wot-design-uni.cn/component/tooltip.html#attributes)"}, "wd-tooltip/disabled": {"type": "boolean", "description": "Tooltip 是否可用, default: false.\n\n[Docs](https://wot-design-uni.cn/component/tooltip.html#attributes)"}, "wd-tooltip/visible-arrow": {"type": "boolean", "description": "是否显示 Tooltip 箭头, default: true.\n\n[Docs](https://wot-design-uni.cn/component/tooltip.html#attributes)"}, "wd-tooltip/offset": {"type": "number | number[]", "description": "出现位置的偏移量, default: {x:0, y:0}.\n\n[Docs](https://wot-design-uni.cn/component/tooltip.html#attributes)"}, "wd-tooltip/show-close": {"type": "boolean", "description": "是否显示 Tooltip 内部的关闭按钮, default: false.\n\n[Docs](https://wot-design-uni.cn/component/tooltip.html#attributes)"}, "wd-tooltip/open": {"type": "event", "description": "显示时触发\n\n[Docs](https://wot-design-uni.cn/component/tooltip.html#events)"}, "wd-tooltip/close": {"type": "event", "description": "隐藏时触发\n\n[Docs](https://wot-design-uni.cn/component/tooltip.html#events)"}, "wd-tooltip/change": {"type": "event", "description": "显隐值变化时触发\n\n[Docs](https://wot-design-uni.cn/component/tooltip.html#events)"}, "wd-transition/show": {"type": "boolean", "description": "是否展示组件\n\n[Docs](https://wot-design-uni.cn/component/transition.html#attributes)"}, "wd-transition/name": {"type": "string", "options": ["TransitionName"], "description": "动画类型\n\n[Docs](https://wot-design-uni.cn/component/transition.html#attributes)"}, "wd-transition/duration": {"type": "number | boolean", "description": "动画执行时间, default: 300(ms).\n\n[Docs](https://wot-design-uni.cn/component/transition.html#attributes)"}, "wd-transition/custom-style": {"type": "string", "description": "自定义样式\n\n[Docs](https://wot-design-uni.cn/component/transition.html#attributes)"}, "wd-transition/disable-touch-move": {"type": "boolean", "description": "是否阻止触摸滚动, default: false.\n\n[Docs](https://wot-design-uni.cn/component/transition.html#attributes)"}, "wd-transition/beforeenter": {"type": "event", "description": "进入前触发\n\n[Docs](https://wot-design-uni.cn/component/transition.html#events)"}, "wd-transition/enter": {"type": "event", "description": "进入时触发\n\n[Docs](https://wot-design-uni.cn/component/transition.html#events)"}, "wd-transition/afterenter": {"type": "event", "description": "进入后触发\n\n[Docs](https://wot-design-uni.cn/component/transition.html#events)"}, "wd-transition/beforeleave": {"type": "event", "description": "离开前触发\n\n[Docs](https://wot-design-uni.cn/component/transition.html#events)"}, "wd-transition/leave": {"type": "event", "description": "离开时触发\n\n[Docs](https://wot-design-uni.cn/component/transition.html#events)"}, "wd-transition/afterleave": {"type": "event", "description": "离开后触发\n\n[Docs](https://wot-design-uni.cn/component/transition.html#events)"}, "wd-upload/v-model:file": {"type": "array", "description": "上传的文件列表, 例如: [{ name: 'food.jpg', url: 'https://xxx.cdn.com/xxx.jpg' }], default: [].\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/action": {"type": "string", "description": "必选参数，上传的地址\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/header": {"type": "object", "description": "HTTP 请求 Header，Header 中不能设置 Referer，uploadFile 接口详细参数，查看[官方手册](https://uniapp.dcloud.net.cn/api/request/network-file#uploadfile)\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/multiple": {"type": "boolean", "description": "是否支持多选文件\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/disabled": {"type": "boolean", "description": "是否禁用, default: false.\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/reupload": {"type": "boolean", "description": "是否开启覆盖上传，开启后会关闭图片预览, default: false.\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/limit": {"type": "number", "description": "最大允许上传个数\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/show-limit-num": {"type": "boolean", "description": "限制上传个数的情况下，是否展示当前上传的个数, default: false.\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/max-size": {"type": "number", "description": "文件大小限制，单位为`byte`\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/source-type": {"type": "array | string", "description": "选择图片的来源，chooseImage 接口详细参数，查看[官方手册](https://uniapp.dcloud.net.cn/api/media/image.html#chooseimage), default: ['album', 'camera'].\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/size-type": {"type": "array | string", "description": "所选的图片的尺寸，chooseImage 接口详细参数，查看[官方手册](https://uniapp.dcloud.net.cn/api/media/image.html#chooseimage), default: ['original', 'compressed'].\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/name": {"type": "string", "description": "文件对应的 key，开发者在服务端可以通过这个 key 获取文件的二进制内容，uploadFile 接口详细参数，查看[官方手册](https://uniapp.dcloud.net.cn/api/request/network-file#uploadfile), default: file.\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/form-data": {"type": "object", "description": "HTTP 请求中其他额外的 form data，uploadFile 接口详细参数，查看[官方手册](https://uniapp.dcloud.net.cn/api/request/network-file#uploadfile)\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/on-preview-fail": {"type": "function({ index, imgList })", "description": "预览失败执行操作\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/before-upload": {"type": "function({ files, fileList, resolve })", "description": "上传文件之前的钩子，参数为上传的文件和文件列表，若返回 false 或者返回 Promise 且被 reject，则停止上传。\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/before-choose": {"type": "function({ fileList, resolve })", "description": "选择图片之前的钩子，参数为文件列表，若返回 false 或者返回 Promise 且被 reject，则停止上传。\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/before-remove": {"type": "function({ file, fileList, resolve })", "description": "删除文件之前的钩子，参数为要删除的文件和文件列表，若返回 false 或者返回 Promise 且被 reject，则停止上传。\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/before-preview": {"type": "function({file, index, imgList, resolve })", "description": "图片预览前的钩子，参数为预览的图片下标和图片列表，若返回 false 或者返回 Promise 且被 reject，则停止上传。\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/build-form-data": {"type": "function({ file, formData, resolve })", "description": "构建上传`formData`的钩子，参数为上传的文件、待处理的`formData`，返回值为处理后的`formData`，若返回 false 或者返回 Promise 且被 reject，则停止上传。\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/loading-type": {"type": "string", "description": "[加载中图标类型](/component/loading), default: circular-ring.\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/loading-color": {"type": "string", "description": "[加载中图标颜色](/component/loading), default: #ffffff.\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/loading-size": {"type": "string", "description": "[加载中图标尺寸](/component/loading), default: 24px.\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/status-key": {"type": "string", "description": "file 数据结构中，status 对应的 key, default: status.\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/image-mode": {"type": "ImageMode", "description": "预览图片的 mode 属性, default: aspectFit.\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/accept": {"type": "UploadFileType", "options": ["image video media file all"], "description": "接受的文件类型, default: image.\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/compressed": {"type": "boolean", "description": "是否压缩视频，当 accept 为 video | media 时生效, default: true.\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/max-duration": {"type": "Number", "description": "拍摄视频最长拍摄时间，当 accept 为 video | media 时生效，单位秒, default: 60.\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/camera": {"type": "UploadCameraType", "options": ["front"], "description": "使用前置或者后置相机，当 accept 为 video | media 时生效, default: back.\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/success-status": {"type": "number", "description": "接口响应的成功状态（statusCode）值, default: 200.\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/auto-upload": {"type": "boolean", "description": "是否选择文件后自动上传。为 false 时应手动调用 submit() 方法开始上传, default: true.\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/upload-method": {"type": "UploadMethod", "description": "自定义上传方法\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/extension": {"type": "string[]", "description": "根据文件拓展名过滤(H5支持全部类型过滤,微信小程序支持all和file时过滤,其余平台不支持)\n\n[Docs](https://wot-design-uni.cn/component/upload.html#attributes)"}, "wd-upload/success": {"type": "event", "description": "上传成功时触发\n\n[Docs](https://wot-design-uni.cn/component/upload.html#events)"}, "wd-upload/fail": {"type": "event", "description": "上传失败时触发\n\n[Docs](https://wot-design-uni.cn/component/upload.html#events)"}, "wd-upload/progress": {"type": "event", "description": "上传中时触发\n\n[Docs](https://wot-design-uni.cn/component/upload.html#events)"}, "wd-upload/chooseerror": {"type": "event", "description": "选择图片失败时触发\n\n[Docs](https://wot-design-uni.cn/component/upload.html#events)"}, "wd-upload/change": {"type": "event", "description": "上传列表修改时触发\n\n[Docs](https://wot-design-uni.cn/component/upload.html#events)"}, "wd-upload/remove": {"type": "event", "description": "移除图片时触发\n\n[Docs](https://wot-design-uni.cn/component/upload.html#events)"}, "wd-upload/oversize": {"type": "event", "description": "文件大小超过限制时触发\n\n[Docs](https://wot-design-uni.cn/component/upload.html#events)"}, "wd-watermark/content": {"type": "string", "description": "显示内容\n\n[Docs](https://wot-design-uni.cn/component/watermark.html#attributes)"}, "wd-watermark/image": {"type": "string", "description": "显示图片的地址，支持网络图片和base64（钉钉小程序支持网络图片）\n\n[Docs](https://wot-design-uni.cn/component/watermark.html#attributes)"}, "wd-watermark/image-height": {"type": "number", "description": "图片高度, default: 100.\n\n[Docs](https://wot-design-uni.cn/component/watermark.html#attributes)"}, "wd-watermark/image-width": {"type": "number", "description": "图片宽度, default: 100.\n\n[Docs](https://wot-design-uni.cn/component/watermark.html#attributes)"}, "wd-watermark/gutter-x": {"type": "number", "description": "X轴间距，单位px, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/watermark.html#attributes)"}, "wd-watermark/gutter-y": {"type": "number", "description": "Y轴间距，单位px, default: 0.\n\n[Docs](https://wot-design-uni.cn/component/watermark.html#attributes)"}, "wd-watermark/width": {"type": "number", "description": "canvas画布宽度，单位px, default: 100.\n\n[Docs](https://wot-design-uni.cn/component/watermark.html#attributes)"}, "wd-watermark/height": {"type": "number", "description": "canvas画布高度，单位px, default: 100.\n\n[Docs](https://wot-design-uni.cn/component/watermark.html#attributes)"}, "wd-watermark/full-screen": {"type": "boolean", "description": "是否为全屏水印, default: true.\n\n[Docs](https://wot-design-uni.cn/component/watermark.html#attributes)"}, "wd-watermark/color": {"type": "string", "description": "水印字体颜色, default: #8c8c8c.\n\n[Docs](https://wot-design-uni.cn/component/watermark.html#attributes)"}, "wd-watermark/size": {"type": "number", "description": "水印字体大小，单位px, default: 14.\n\n[Docs](https://wot-design-uni.cn/component/watermark.html#attributes)"}, "wd-watermark/font-style": {"type": "string", "options": ["normal", "italic", "oblique"], "description": "水印字体样式（仅微信、支付宝和h5支持）, default: normal.\n\n[Docs](https://wot-design-uni.cn/component/watermark.html#attributes)"}, "wd-watermark/font-weight": {"type": "string", "options": ["normal", "bold", "bolder"], "description": "水印字体的粗细（仅微信、支付宝和h5支持）, default: normal.\n\n[Docs](https://wot-design-uni.cn/component/watermark.html#attributes)"}, "wd-watermark/font-family": {"type": "string", "description": "水印字体系列（仅微信、支付宝和h5支持）, default: PingFang SC.\n\n[Docs](https://wot-design-uni.cn/component/watermark.html#attributes)"}, "wd-watermark/rotate": {"type": "number", "description": "水印旋转角度, default: -25.\n\n[Docs](https://wot-design-uni.cn/component/watermark.html#attributes)"}, "wd-watermark/z-index": {"type": "number", "description": "自定义层级, default: 1100.\n\n[Docs](https://wot-design-uni.cn/component/watermark.html#attributes)"}, "wd-watermark/opacity": {"type": "number", "description": "自定义透明度，取值 0~1, default: 0.5.\n\n[Docs](https://wot-design-uni.cn/component/watermark.html#attributes)"}}