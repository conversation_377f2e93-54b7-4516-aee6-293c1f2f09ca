@import '../common/abstracts/variable';
@import '../common/abstracts/mixin';

@mixin tag-type-style ($normalColor, $normalBg) {
  background: $normalBg;

  @include when(plain) {
    background: transparent;
    color: $normalColor;
    border: 1px solid $normalColor;
    padding: 0 4px;
  }
  @include when(round) {
    line-height: 1.2;
    font-size: $-tag-fs;
    padding: 4px 11px;
    background: transparent;
    color: if($normalColor != $-tag-info-color, $normalColor, $-tag-round-color);
    border: 1px solid if($normalColor != $-tag-info-color, $normalColor, $-tag-round-border-color);
    border-radius: $-tag-round-radius;
  }
  @include when(mark) {
    padding: 1px 6px;
    border-radius: $-tag-mark-radius;

    @include when(plain) {
      padding: 0 6px;
    }
  }
  @include when(active) {
    color: $-tag-primary-color;
    border-color: $-tag-primary-color;
  }
}
@include b(tag) {
  font-size: $-tag-small-fs;
  display: inline-block;
  color: $-tag-color;
  padding: 0 3px;
  border-radius: 2px;
  transition: opacity .3s;
  vertical-align: middle;
  line-height: initial;

  @include when(default) {
    @include tag-type-style($-tag-info-color, $-tag-info-bg);
  }
  @include when(primary) {
    @include tag-type-style($-tag-primary-color, $-tag-primary-bg);
  }
  @include when(danger) {
    @include tag-type-style($-tag-danger-color, $-tag-danger-bg);
  }
  @include when(warning) {
    @include tag-type-style($-tag-warning-color, $-tag-warning-bg);
  }
  @include when(success) {
    @include tag-type-style($-tag-success-color, $-tag-success-bg);
  }
  @include when(icon) {
    font-size: $-tag-fs;
    line-height: 1.2;
    padding: 2px 5px;
  }
  @include when(dynamic) {
    box-sizing: border-box;
    width: 88px;
    transition: .3s;

    &:active {
      color: $-tag-primary-color;
      border-color: $-tag-primary-color;
    }
  }
  @include when(dynamic-input) {
    border-color: $-tag-primary-color;
  }
  @include edeep(icon) {
    display: inline-block;
    margin-right: 4px;
    font-size: $-tag-fs;
    line-height: 1.2;
    vertical-align: baseline;
  }
  @include e(text) {
    display: inline-block;
    vertical-align: text-top;
  }
  @include e(add-text) {
    width: 60px;
    height: 14px;
    min-height: 14px;
    display: inline-block;
    font-size: $-tag-fs;
    vertical-align: middle;
    padding: 0;
  }
  @include e(close) {
    display: inline-block;
    margin-left: 24px;
    margin-right: -4px;
    font-size: $-tag-close-size;
    height: 14px;
    line-height: 1.1;
    vertical-align: text-bottom;
    color: $-tag-close-color;

    &:active {
      color: $-tag-close-active-color;
    }
  }
  @include edeep(add) {
    vertical-align: bottom;
  }
}