/**
 * 辅助函数
 */
@import 'config';
$default-theme: #4d80f0 !default; // 正常色

/* 转换成字符串 */
@function selectorToString($selector) {
  $selector: inspect($selector);
  $selector: str-slice($selector, 2, -2);

  @return $selector;
}

/* 判断是否存在 Modifier */
@function containsModifier($selector) {
  $selector: selectorToString($selector);

  @if str-index($selector, $modifierSeparator) {
    @return true;
  }

  @else {
    @return false;
  }
}

/* 判断是否存在伪类 */
@function containsPseudo($selector) {
  $selector: selectorToString($selector);

  @if str-index($selector, ':') {
    @return true;
  }

  @else {
    @return false;
  }
}


/**
 * 主题色切换
 * @params $theme-color 主题色
 * @params $type 变暗’dark‘ 变亮 'light'
 * @params $mix-color 自己设置的混色
 */
@function themeColor($theme-color, $type: "", $mix-color: "") {
  @if $default-theme !=#4d80f0 {
    @if $type=="dark" {
      @return darken($theme-color, 10%);
    }

    @else if $type=="light" {
      @return lighten($theme-color, 10%);
    }

    @else {
      @return $theme-color;
    }
  }

  @else {
    @return $mix-color;
  }
}

/**
 * 颜色结果切换， 如果开启线性渐变色 使用渐变色，如果没有开启，那么使用主题色
 * @params $open-linear 是否开启线性渐变色
 * @params $deg 渐变色角度
 * @params $theme-color 当前配色
 * @params [Array] $set 主题色明暗设置，与 $color-list 数量对应
 * @params [Array] $color-list 渐变色顺序， $color-list 和 $per-list 数量相同
 * @params [Array] $per-list 渐变色比例
 */
@function resultColor($deg, $theme-color, $set, $color-list, $per-list) {
  // 开启渐变

  $len: length($color-list);
  $arg: $deg;

  @for $i from 1 through $len {
    $arg: $arg + ","+ themeColor($theme-color, nth($set, $i), nth($color-list, $i)) + " "+ nth($per-list, $i);
  }

  @return linear-gradient(unquote($arg));

}