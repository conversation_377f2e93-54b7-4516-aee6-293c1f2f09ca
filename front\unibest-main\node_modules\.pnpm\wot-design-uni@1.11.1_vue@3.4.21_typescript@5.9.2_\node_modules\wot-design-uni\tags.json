{"wd-action-sheet": {"attributes": ["v-model", "actions", "panels", "title", "cancel-text", "close-on-click-action", "close-on-click-modal", "duration", "z-index", "lazy-render", "safe-area-inset-bottom", "select", "open", "opened", "close", "closed", "click-modal", "cancel"], "description": "从底部弹出的动作菜单面板。\n\n[Docs](https://wot-design-uni.cn/component/action-sheet.html#actionsheet)"}, "wd-backtop": {"attributes": ["scroll-top", "top", "duration", "z-index", "icon-style", "shape", "bottom", "right"], "description": "用于返回页面顶部的操作按钮。\n\n[Docs](https://wot-design-uni.cn/component/backtop.html#backtop)"}, "wd-badge": {"attributes": ["v-model", "max", "top", "right", "is-dot", "hidden", "type", "bg-color", "show-zero"], "description": "出现在按钮、图标旁的数字或状态标记。\n\n[Docs](https://wot-design-uni.cn/component/badge.html#badge)"}, "wd-button": {"attributes": ["type", "round", "plain", "hairline", "loading", "block", "size", "disabled", "icon", "loading-color", "open-type", "hover-stop-propagation", "lang", "session-from", "session-message-title", "session-message-path", "send-message-img", "app-parameter", "show-message-card", "class-prefix", "button-id", "scope", "click", "getuserinfo", "contact", "getphonenumber", "error", "launchapp", "opensetting"], "description": "按钮用于触发一个操作，如提交表单或打开链接。\n\n[Docs](https://wot-design-uni.cn/component/button.html#button)"}, "wd-calendar-view": {"attributes": ["v-model", "type", "min-date", "max-date", "first-day-of-week", "formatter", "max-range", "range-prompt", "allow-same-day", "show-panel-title", "default-time", "panel-height", "time-filter", "hide-second", "immediate-change", "change"], "description": "提供日历单选、多选、范围选择、周维度、月维度等功能。可以根据实际业务场景基于该组件进行封装高度定制化组件。\n\n[Docs](https://wot-design-uni.cn/component/calendar-view.html#calendarview)"}, "wd-calendar": {"attributes": ["v-model", "type", "min-date", "max-date", "first-day-of-week", "formatter", "max-range", "range-prompt", "allow-same-day", "default-time", "time-filter", "hide-second", "show-confirm", "show-type-switch", "shortcuts", "title", "label", "placeholder", "disabled", "readonly", "display-format", "inner-display-format", "size", "label-width", "error", "required", "center", "ellipsis", "align-right", "before-confirm", "suse-default-slots", "suse-label-slots", "close-on-click-modal", "z-index", "safe-area-inset-bottom", "prop", "rules", "immediate-change", "with-cell", "clearable", "root-portal", "confirm", "change", "cancel", "open", "clear"], "description": "提供日历单选、多选、范围选择、周维度、月维度等功能。\n\n[Docs](https://wot-design-uni.cn/component/calendar.html#calendar)"}, "wd-card": {"attributes": ["title", "type"], "description": "用于展示商品的图片、价格等信息。\n\n[Docs](https://wot-design-uni.cn/component/card.html#card)"}, "wd-cell-group": {"attributes": ["title", "value", "border", "use-slot"], "description": "单元格为列表中的单个展示项。\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cellgroup)"}, "wd-cell": {"attributes": ["title", "value", "icon", "label", "is-link", "to", "clickable", "replace", "size", "title-width", "center", "required", "vertical", "ellipsis", "use-title-slot", "prop", "rules", "border", "click"], "description": "单元格为列表中的单个展示项。\n\n[Docs](https://wot-design-uni.cn/component/cell.html#cell)"}, "wd-checkbox": {"attributes": ["v-model", "shape", "checked-color", "disabled", "max-width", "true-value", "false-value", "size", "change"], "description": "复选框用于在一组备选项中进行多选。\n\n[Docs](https://wot-design-uni.cn/component/checkbox.html#checkbox)"}, "wd-checkbox-group": {"attributes": ["v-model", "shape", "cell", "checked-color", "disabled", "min", "max", "inline", "size", "change"], "description": "复选框用于在一组备选项中进行多选。\n\n[Docs](https://wot-design-uni.cn/component/checkbox.html#checkboxgroup)"}, "wd-circle": {"attributes": ["v-model", "custom-class", "custom-style", "size", "color", "layer-color", "fill", "speed", "text", "stroke-width", "stroke-linecap", "clockwise"], "description": "圆环形的进度条组件，支持进度渐变动画。\n\n[Docs](https://wot-design-uni.cn/component/circle.html#circle)"}, "wd-col-picker": {"attributes": ["v-model", "columns", "value-key", "label-key", "tip-key", "title", "label", "placeholder", "disabled", "readonly", "display-format", "column-change", "size", "label-width", "error", "required", "align-right", "before-confirm", "loading-color", "use-default-slot", "use-label-slot", "close-on-click-modal", "auto-complete", "z-index", "safe-area-inset-bottom", "ellipsis", "prop", "rules", "line-width", "line-height", "root-portal", "confirm", "close"], "description": "使用多列选择器来做级联，交互效果较好，多列选择器支持无限级选择。\n\n[Docs](https://wot-design-uni.cn/component/col-picker.html#colpicker)"}, "wd-collapse-item": {"attributes": ["name", "title", "disabled", "before-expend"], "description": "将一组内容放置在多个折叠面板中，点击面板的标题可以展开或收缩其内容。\n\n[Docs](https://wot-design-uni.cn/component/collapse.html#collapseitem)"}, "wd-collapse": {"attributes": ["value", "accordion", "viewmore", "use-more-slot", "line-num", "change"], "description": "将一组内容放置在多个折叠面板中，点击面板的标题可以展开或收缩其内容。\n\n[Docs](https://wot-design-uni.cn/component/collapse.html#collapse)"}, "wd-config-provider": {"attributes": ["theme", "theme-vars"], "description": "用于全局配置 `Wot` 组件，提供深色模式、主题定制等能力。\n\n[Docs](https://wot-design-uni.cn/component/config-provider.html#configprovider)"}, "wd-count-down": {"attributes": ["time", "millisecond", "auto-start", "format", "finish", "change"], "description": "用于实时展示倒计时数值，支持毫秒精度。\n\n[Docs](https://wot-design-uni.cn/component/count-down.html#countdown)"}, "wd-count-to": {"attributes": ["font-size", "color", "type", "start-val", "end-val", "duration", "autoplay", "decimals", "decimal", "separator", "prefix", "suffix", "use-easing", "finish", "mounted"], "description": "数字滚动组件。\n\n[Docs](https://wot-design-uni.cn/component/count-to.html#countto)"}, "wd-curtain": {"attributes": ["value", "model-value", "src", "width", "to", "close-position", "close-on-click-modal", "hide-when-close", "z-index", "root-portal", "click", "close", "click-modal", "beforeenter", "enter", "afterenter", "beforeleave", "leave", "afterleave", "load", "error"], "description": "一般用于公告类的图片弹窗。\n\n[Docs](https://wot-design-uni.cn/component/curtain.html#curtain)"}, "wd-datetime-picker-view": {"attributes": ["v-model", "type", "loading", "loading-color", "columns-height", "formatter", "filter", "min-date", "max-date", "min-hour", "max-hour", "min-minute", "max-minute", "immediate-change", "use-second", "change", "pickstart", "pickend"], "description": "为 Picker 组件的封装，在其内部构建好日期时间选项。\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker-view.html#datetimepickerview)"}, "wd-datetime-picker": {"attributes": ["v-model", "default-value", "type", "loading", "loading-color", "columns-height", "title", "cancel-button-text", "confirm-button-text", "label", "placeholder", "disabled", "readonly", "display-format", "formatter", "filter", "display-format-tab-label", "min-date", "max-date", "min-hour", "max-hour", "min-minute", "max-minute", "required", "size", "label-width", "error", "align-right", "suse-label-slots", "suse-default-slots", "before-confirm", "close-on-click-modal", "z-index", "safe-area-inset-bottom", "ellipsis", "prop", "rules", "immediate-change", "use-second", "clearable", "root-portal", "confirm", "cancel", "toggle", "clear"], "description": "为 DatetimePickerView 组件的封装，在其内部构建好日期时间选项。\n\n[Docs](https://wot-design-uni.cn/component/datetime-picker.html#datetimepicker)"}, "wd-divider": {"attributes": ["color", "hairline", "dashed", "content-position", "vertical"], "description": "用于将内容分隔为多个区域。\n\n[Docs](https://wot-design-uni.cn/component/divider.html#divider)"}, "wd-drop-menu": {"attributes": ["direction", "modal", "close-on-click-modal", "duration"], "description": "向下或向上弹出的菜单列表。\n\n[Docs](https://wot-design-uni.cn/component/drop-menu.html#dropmenu)"}, "wd-drop-menu-item": {"attributes": ["v-model", "disabled", "options", "icon-name", "title", "icon", "icon-size", "before-toggle", "value-key", "label-key", "tip-key", "root-portal"], "description": "向下或向上弹出的菜单列表。\n\n[Docs](https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem)"}, "wd-dropdown-item": {"description": "向下或向上弹出的菜单列表。\n\n[Docs](https://wot-design-uni.cn/component/drop-menu.html#dropdownitem)"}, "wd-fab": {"attributes": ["v-model:active", "type", "position", "draggable", "direction", "disabled", "inactive-icon", "active-icon", "z-index", "gap", "custom-style", "expandable", "click"], "description": "悬浮动作按钮组件，按下可显示一组动作按钮。\n\n[Docs](https://wot-design-uni.cn/component/fab.html#fab)"}, "wd-floating-panel": {"attributes": ["v-model:height", "anchors", "duration", "content-draggable", "safe-area-inset-bottom", "show-scrollbar"], "description": "浮动在页面底部的面板，用户可以通过上下拖动秒板来浏览内容，从而在不离开当前视图的情况下访问更多信息，常用于地图导航。\n\n[Docs](https://wot-design-uni.cn/component/floating-panel.html#floatingpanel)"}, "wd-form": {"attributes": ["model", "rules", "reset-on-change", "error-type", "validate", "reset"], "description": "用于数据录入、校验，支持输入框、单选框、复选框、文件上传等类型，常见的 form 表单为`单元格`形式的展示，即左侧为表单的标题描述，右侧为表单的输入。\n\n[Docs](https://wot-design-uni.cn/component/form.html#form)"}, "wd-gap": {"attributes": ["height", "bg-color", "safe-area-bottom"], "description": "一般用于页面布局时代替margin或者padding;或者充当（底部）占位元素。\n\n[Docs](https://wot-design-uni.cn/component/gap.html#gap)"}, "wd-grid": {"attributes": ["column", "border", "gutter", "square", "clickable", "bg-color", "hover-class"], "description": "宫格可以在水平方向上把页面分隔成等宽度的区块，用于展示内容或进行页面导航。\n\n[Docs](https://wot-design-uni.cn/component/grid.html#grid)"}, "wd-grid-item": {"attributes": ["text", "icon", "is-dot", "type", "value", "max", "url", "link-type", "use-slot", "use-icon-slot", "use-text-slot", "icon-size", "badge-props"], "description": "宫格可以在水平方向上把页面分隔成等宽度的区块，用于展示内容或进行页面导航。\n\n[Docs](https://wot-design-uni.cn/component/grid.html#griditem)"}, "wd-icon": {"attributes": ["name", "color", "size", "class-prefix", "custom-style"], "description": "基于字体的图标集。\n\n[Docs](https://wot-design-uni.cn/component/icon.html#icon)"}, "wd-img-cropper": {"attributes": ["v-model", "img-src", "img-width", "img-height", "disabled-rotate", "export-scale", "max-scale", "cancel-button-text", "confirm-button-text", "quality", "file-type", "aspect-ratio", "confirm", "cancel", "imgloaderror", "imgloaded"], "description": "图片剪裁组件，用于图片裁剪，支持拖拽、缩放、旋转等操作。\n\n[Docs](https://wot-design-uni.cn/component/img-cropper.html#imgcropper)"}, "wd-img": {"attributes": ["src", "width", "height", "mode", "round", "radius", "enable-preview", "show-menu-by-longpress", "preview-src", "click", "load", "error"], "description": "增强版的 img 标签，提供多种图片填充模式，支持图片懒加载、加载完成、加载失败。\n\n[Docs](https://wot-design-uni.cn/component/img.html#img)"}, "wd-index-anchor": {"attributes": ["index"], "description": "用于列表的索引分类显示和快速定位。\n\n[Docs](https://wot-design-uni.cn/component/index-bar.html#indexanchor)"}, "wd-index-bar": {"attributes": ["sticky"], "description": "用于列表的索引分类显示和快速定位。\n\n[Docs](https://wot-design-uni.cn/component/index-bar.html#indexbar)"}, "wd-input-number": {"attributes": ["v-model", "min", "max", "step", "step-strictly", "precision", "disabled", "without-input", "input-width", "allow-null", "placeholder", "disable-input", "disable-plus", "disable-minus", "adjust-position", "before-change", "long-press", "immediate-change", "update-on-init", "input-type", "change", "focus", "blur"], "description": "由增加按钮、减少按钮和输入框组成，用于在一定范围内输入、调整数字。\n\n[Docs](https://wot-design-uni.cn/component/input-number.html#inputnumber)"}, "wd-input": {"attributes": ["type", "v-model", "placeholder", "clearable", "maxlength", "show-password", "disabled", "readonly", "prefix-icon", "suffix-icon", "show-word-limit", "confirm-type", "confirm-hold", "always-embed", "placeholder-style", "placeholder-class", "focus", "cursor-spacing", "cursor", "selection-start", "selection-end", "adjust-position", "label", "size", "error", "center", "label-width", "required", "no-border", "prop", "rules", "clear-trigger", "focus-when-clear", "ignore-composition-event", "inputmode", "input", "focus", "blur", "clear", "confirm", "keyboardheightchange", "clickprefixicon", "clicksuffixicon"], "description": "用户可以在文本框里输入内容。\n\n[Docs](https://wot-design-uni.cn/component/input.html#input)"}, "wd-keyboard": {"attributes": ["v-model:visible", "v-model", "title", "mode", "z-index", "maxlength", "show-delete-key", "random-key-order", "close-text", "delete-text", "close-button-loading", "modal", "hide-on-click-outside", "lock-scroll", "safe-area-inset-bottom", "extra-key", "root-portal", "input", "delete", "close"], "description": "虚拟数字键盘，用于输入数字、密码、身份证或车牌号等场景。\n\n[Docs](https://wot-design-uni.cn/component/keyboard.html#keyboard)"}, "wd-row": {"attributes": ["gutter"], "description": "用于快速进行布局。\n\n[Docs](https://wot-design-uni.cn/component/layout.html#row)"}, "wd-col": {"attributes": ["span", "offset"], "description": "用于快速进行布局。\n\n[Docs](https://wot-design-uni.cn/component/layout.html#col)"}, "wd-loading": {"attributes": ["type", "color", "size"], "description": "加载动画，用于表示加载中的过渡状态。\n\n[Docs](https://wot-design-uni.cn/component/loading.html#loading)"}, "wd-loadmore": {"attributes": ["state", "loading-text", "finished-text", "error-text", "loading-props", "reload"], "description": "用于在列表底部展示加载状态。\n\n[Docs](https://wot-design-uni.cn/component/loadmore.html#loadmore)"}, "wd-message-box": {"attributes": ["selector", "root-portal"], "description": "弹出对话框，常用于消息提示、消息确认等，支持函数调用。\n\n[Docs](https://wot-design-uni.cn/component/message-box.html#messagebox)"}, "wd-navbar": {"attributes": ["title", "left-text", "right-text", "left-arrow", "bordered", "fixed", "placeholder", "z-index", "safe-area-inset-top", "left-disabled", "right-disabled", "click-left", "click-right"], "description": "为页面提供导航功能，常用于页面顶部。\n\n[Docs](https://wot-design-uni.cn/component/navbar.html#navbar)"}, "wd-navbar-capsule": {"attributes": ["back", "back-home"], "description": "为页面提供导航功能，常用于页面顶部。\n\n[Docs](https://wot-design-uni.cn/component/navbar.html#navbarcapsule)"}, "wd-notice-bar": {"attributes": ["text", "type", "prefix", "scrollable", "delay", "speed", "closable", "wrapable", "color", "background-color", "direction", "close", "next", "click"], "description": "通知栏组件，用于在页面顶部展示通知提醒。\n\n[Docs](https://wot-design-uni.cn/component/notice-bar.html#noticebar)"}, "wd-notify": {"attributes": ["type", "message", "duration", "z-index", "position", "color", "background", "safe-height", "selector", "root-portal"], "description": "通知类组件，用于在页面顶部展示通知信息。\n\n[Docs](https://wot-design-uni.cn/component/notify.html#notify)"}, "wd-number-keyboard": {"attributes": ["v-model:visible", "v-model", "title", "mode", "z-index", "maxlength", "show-delete-key", "random-key-order", "close-text", "delete-text", "close-button-loading", "modal", "hide-on-click-outside", "lock-scroll", "safe-area-inset-bottom", "extra-key", "root-portal", "input", "delete", "close"], "description": "虚拟数字键盘，用于输入数字、密码或身份证等场景。\n\n[Docs](https://wot-design-uni.cn/component/number-keyboard.html#numberkeyboard)"}, "wd-overlay": {"attributes": ["show", "duration", "lock-scroll", "z-index", "custom-style"], "description": "创建一个遮罩层，用于强调特定的页面元素，并阻止用户进行其他操作。\n\n[Docs](https://wot-design-uni.cn/component/overlay.html#overlay)"}, "wd-pagination": {"attributes": ["v-model", "prev-text", "next-text", "total-page", "page-size", "total", "show-icon", "show-message", "hide-if-one-page", "change"], "description": "当数据量过多时，使用分页分解数据。\n\n[Docs](https://wot-design-uni.cn/component/pagination.html#pagination)"}, "wd-password-input": {"attributes": ["v-model", "info", "error-info", "length", "gutter", "mask", "focused"], "description": "带网格的输入框组件，可以用于输入密码、短信验证码等场景，通常与[数字键盘]\n\n[Docs](https://wot-design-uni.cn/component/password-input.html#passwordinput)"}, "wd-picker-view": {"attributes": ["v-model", "columns", "loading", "loading-color", "columns-height", "value-key", "label-key", "column-change", "immediate-change", "change", "pickstart", "pickend"], "description": "选择器视图，用于从一组数据中选择单个或多个值。\n\n[Docs](https://wot-design-uni.cn/component/picker-view.html#pickerview)"}, "wd-picker": {"attributes": ["v-model", "columns", "loading", "loading-color", "columns-height", "value-key", "label-key", "title", "cancel-button-text", "confirm-button-text", "label", "placeholder", "disabled", "readonly", "display-format", "column-change", "size", "label-width", "error", "required", "align-right", "use-label-slot", "use-default-slot", "before-confirm", "close-on-click-modal", "z-index", "safe-area-inset-bottom", "ellipsis", "prop", "rules", "immediate-change", "clearable", "root-portal", "confirm", "cancel", "open", "clear"], "description": "Picker 组件为 popup 和 pickerView 的组合。\n\n[Docs](https://wot-design-uni.cn/component/picker.html#picker)"}, "wd-popover": {"attributes": ["open", "close", "change", "menuclick"], "description": "常用于展示提示信息。\n\n[Docs](https://wot-design-uni.cn/component/popover.html#popover)"}, "wd-popup": {"attributes": ["v-model", "position", "closable", "close-on-click-modal", "duration", "z-index", "custom-style", "modal", "modal-style", "hide-when-close", "lazy-render", "safe-area-inset-bottom", "transition", "lock-scroll", "root-portal", "close", "click-modal", "before-enter", "enter", "after-enter", "before-leave", "leave", "after-leave"], "description": "弹出层组件，用于展示弹窗、信息提示等内容。\n\n[Docs](https://wot-design-uni.cn/component/popup.html#popup)"}, "wd-progress": {"attributes": ["percentage", "hide-text", "color", "status", "duration"], "description": "用于展示操作的当前进度。\n\n[Docs](https://wot-design-uni.cn/component/progress.html#progress)"}, "wd-radio-group": {"attributes": ["v-model", "shape", "size", "checked-color", "disabled", "max-width", "inline", "cell", "icon-placement", "change"], "description": "单选框，用于在一组备选项中进行单选。\n\n[Docs](https://wot-design-uni.cn/component/radio.html#radiogroup)"}, "wd-radio": {"attributes": ["value", "shape", "checked-color", "disabled"], "description": "单选框，用于在一组备选项中进行单选。\n\n[Docs](https://wot-design-uni.cn/component/radio.html#radio)"}, "wd-rate": {"attributes": ["v-model", "num", "readonly", "size", "space", "color", "active-color", "icon", "active-icon", "disabled", "disabled-color", "allow-half", "change"], "description": "用于快速的评价操作，或对评价进行展示。\n\n[Docs](https://wot-design-uni.cn/component/rate.html#rate)"}, "wd-resize": {"attributes": ["resize"], "description": "当组件包裹的文档流尺寸发生变化时，触发 `size` 事件。一般用于监听 dom 内容更新时导致的 dom 尺寸位置的变化，重新获取 dom 尺寸和位置，进行内容展示的计算操作。\n\n[Docs](https://wot-design-uni.cn/component/resize.html#resize)"}, "wd-root-portal": {"description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题，主要用于制作弹窗、弹出层等。\n\n[Docs](https://wot-design-uni.cn/component/root-portal.html#root-portal)"}, "wd-search": {"attributes": ["placeholder", "placeholder-left", "cancel-txt", "light", "hide-cancel", "disabled", "maxlength", "v-model", "use-suffix-slot", "focus", "focus-when-clear", "placeholder-style", "placeholder-class", "focus", "blur", "search", "clear", "cancel", "change"], "description": "搜索框组件，支持输入框聚焦、失焦、输入、搜索、取消、清空事件。\n\n[Docs](https://wot-design-uni.cn/component/search.html#search)"}, "wd-segmented": {"attributes": ["v-model:value", "disabled", "size", "options", "vibrate-short", "change", "click"], "description": "分段器用于展示多个选项并允许用户选择其中单个选项。\n\n[Docs](https://wot-design-uni.cn/component/segmented.html#segmented)"}, "wd-select-picker": {"attributes": ["v-model", "columns", "type", "value-key", "label-key", "title", "label", "placeholder", "disabled", "loading", "loading-color", "readonly", "display-format", "confirm-button-text", "size", "label-width", "error", "required", "align-right", "before-confirm", "select-size", "min", "max", "checked-color", "use-default-slot", "use-label-slot", "close-on-click-modal", "z-index", "safe-area-inset-bottom", "filterable", "filter-placeholder", "ellipsis", "scroll-into-view", "show-confirm", "prop", "rules", "clearable", "root-portal", "confirm", "change", "cancel", "close", "open", "clear"], "description": "用于从一组选项中进行单选或多选。\n\n[Docs](https://wot-design-uni.cn/component/select-picker.html#selectpicker)"}, "wd-sidebar-item": {"attributes": ["label", "value", "icon", "badge", "is-dot", "max", "disabled", "badge-props"], "description": "垂直展示的导航栏，用于在不同的内容区域之间进行切换。\n\n[Docs](https://wot-design-uni.cn/component/sidebar.html#sidebaritem)"}, "wd-sidebar": {"attributes": ["v-model", "before-change", "change"], "description": "垂直展示的导航栏，用于在不同的内容区域之间进行切换。\n\n[Docs](https://wot-design-uni.cn/component/sidebar.html#sidebar)"}, "wd-signature": {"attributes": ["pen-color", "line-width", "height", "width", "clear-text", "confirm-text", "file-type", "quality", "export-scale", "disabled", "background-color", "disable-scroll", "enable-history", "step", "pressure", "min-width", "max-width", "min-speed", "start", "end", "signing", "confirm", "clear"], "description": "用于签名场景，基于 Canvas 实现的签名组件。提供了基础签名、历史记录、笔锋效果等功能。\n\n[Docs](https://wot-design-uni.cn/component/signature.html#signature)"}, "wd-skeleton": {"attributes": ["theme", "row-col", "loading", "animation"], "description": "用于等待加载内容所展示的占位图形组合，有动态效果加载效果，减少用户等待焦虑。\n\n[Docs](https://wot-design-uni.cn/component/skeleton.html#skeleton)"}, "wd-slider": {"attributes": ["v-model", "hide-min-max", "hide-label", "disabled", "max", "min", "step", "active-color", "inactive-color", "dragstart", "dragmove", "dragend"], "description": "支持单向滑块和双向滑块。\n\n[Docs](https://wot-design-uni.cn/component/slider.html#slider)"}, "wd-sort-button": {"attributes": ["v-model", "title", "allow-reset", "desc-first", "line", "change"], "description": "用于展示排序按钮，支持升序、降序、重置三种状态。\n\n[Docs](https://wot-design-uni.cn/component/sort-button.html#sortbutton)"}, "wd-status-tip": {"attributes": ["image", "image-size", "tip", "image-mode", "url-prefix"], "description": "一般用于兜底占位展示。\n\n[Docs](https://wot-design-uni.cn/component/status-tip.html#statustip)"}, "wd-steps": {"attributes": ["active", "vertical", "dot", "space", "align-center"], "description": "用于引导用户按照流程完成任务或向用户展示当前状态。\n\n[Docs](https://wot-design-uni.cn/component/steps.html#steps)"}, "wd-step": {"attributes": ["title", "stitle-slots", "description", "sdescription-slots", "icon", "sicon-slots", "status"], "description": "用于引导用户按照流程完成任务或向用户展示当前状态。\n\n[Docs](https://wot-design-uni.cn/component/steps.html#step)"}, "wd-sticky": {"attributes": ["z-index", "offset-top"], "description": "粘性布局组件，用于在页面滚动时将元素固定在指定位置。\n\n[Docs](https://wot-design-uni.cn/component/sticky.html#sticky)"}, "wd-swipe-action": {"attributes": ["v-model", "disabled", "before-close", "click"], "description": "常用于单元格左右滑删除等手势操作。\n\n[Docs](https://wot-design-uni.cn/component/swipe-action.html#swipeaction)"}, "wd-swiper": {"attributes": ["autoplay", "v-model:current", "direction", "display-multiple-items", "duration", "easing-function", "height", "interval", "list", "loop", "next-margin", "indicator-position", "previous-margin", "snap-to-edge", "indicator", "image-mode", "autoplay-video", "stop-previous-video", "stop-autoplay-when-video-play", "custom-style", "value-key", "text-key", "adjust-height", "adjust-vertical-height", " muted", "video-loop", "click", "change"], "description": "用于创建轮播，它支持水平和垂直方向的滑动，可以自定义样式和指示器位置，支持视频和图片资源的轮播，支持设置轮播标题和自定义标题样式。\n\n[Docs](https://wot-design-uni.cn/component/swiper.html#swiper)"}, "wd-switch": {"attributes": ["v-model", "disabled", "active-value", "inactive-value", "active-color", "inactive-color", "size", "before-change", "change"], "description": "用来打开或关闭选项。\n\n[Docs](https://wot-design-uni.cn/component/switch.html#switch)"}, "wd-tabbar-item": {"attributes": ["title", "name", "icon", "value", "is-dot", "max", "badge-props"], "description": "底部导航栏，用于在不同页面之间进行切换。\n\n[Docs](https://wot-design-uni.cn/component/tabbar.html#tabbaritem)"}, "wd-tabbar": {"attributes": ["v-model", "fixed", "safe-area-inset-bottom", "bordered", "shape", "active-color", "inactive-color", "placeholder", "z-index", "change"], "description": "底部导航栏，用于在不同页面之间进行切换。\n\n[Docs](https://wot-design-uni.cn/component/tabbar.html#tabbar)"}, "wd-table-column": {"attributes": ["prop", "label", "width", "sortable", "fixed", "align"], "description": "用于展示多条结构类似的数据， 可对数据进行排序等操作。\n\n[Docs](https://wot-design-uni.cn/component/table.html#tablecolumn)"}, "wd-table": {"attributes": ["data", "border", "stripe", "height", "row-height", "show-header", "ellipsis", "index", "fixed-header", "sort-method", "row-click"], "description": "用于展示多条结构类似的数据， 可对数据进行排序等操作。\n\n[Docs](https://wot-design-uni.cn/component/table.html#table)"}, "wd-tabs": {"attributes": ["v-model", "slidable-num", "map-num", "map-title", "sticky", "offset-top", "swipeable", "auto-line-width", "line-width", "line-height", "color", "inactive-color", "animated", "duration", "slidable", "badge-props", "change", "click", "disabled"], "description": "标签页组件，用于在不同的内容区域之间进行切换。\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tabs)"}, "wd-tab": {"attributes": ["name", "title", "disabled", "lazy"], "description": "标签页组件，用于在不同的内容区域之间进行切换。\n\n[Docs](https://wot-design-uni.cn/component/tabs.html#tab)"}, "wd-tag": {"attributes": ["type", "plain", "mark", "round", "icon", "color", "bg-color", "closable", "use-icon-slot", "dynamic", "click", "close", "confirm"], "description": "用于标记状态或者概括主要内容。\n\n[Docs](https://wot-design-uni.cn/component/tag.html#tag)"}, "wd-text": {"attributes": ["type", "text", "size", "mode", "bold", "format", "color", "lines", "line-height", "decoration", "prefix", "suffix", "click"], "description": "文本组件，用于展示文本信息。\n\n[Docs](https://wot-design-uni.cn/component/text.html#text)"}, "wd-textarea": {"attributes": ["v-model", "placeholder", "placeholder-style", "placeholder-class", "disabled", "maxlength", "auto-focus", "focus", "auto-height", "fixed", "cursor-spacing", "cursor", "confirm-type", "confirm-hold", "show-confirm-bar", "selection-start", "selection-end", "adjust-position", "disable-default-padding", "hold-keyboard", "show-password", "clearable", "readonly", "prefix-icon", "show-word-limit", "label", "label-width", "size", "error", "center", "no-border", "required", "prop", "rules", "clear-trigger", "focus-when-clear", "ignore-composition-event", "inputmode", "input", "focus", "blur", "clear", "linechange", "confirm", "keyboardheightchange", "clickprefixicon", "clicksuffixicon"], "description": "用于输入多行文本信息。\n\n[Docs](https://wot-design-uni.cn/component/textarea.html#textarea)"}, "wd-toast": {"attributes": ["selector", "msg", "direction", "icon-name", "icon-size", "loading-type", "loading-color", "loading-size", "icon-color", "position", "z-index", "cover", "icon-class", "class-prefix", "opened", "closed"], "description": "轻提示组件，用于消息通知、加载提示、操作结果提示等场景，支持函数式调用。\n\n[Docs](https://wot-design-uni.cn/component/toast.html#toast)"}, "wd-tooltip": {"attributes": ["show", "content", "placement", "disabled", "visible-arrow", "offset", "show-close", "open", "close", "change"], "description": "常用于展示提示信息。\n\n[Docs](https://wot-design-uni.cn/component/tooltip.html#tooltip)"}, "wd-transition": {"attributes": ["show", "name", "duration", "custom-style", "disable-touch-move", "beforeenter", "enter", "afterenter", "beforeleave", "leave", "afterleave"], "description": "用于在元素进入或离开时应用过渡效果。\n\n[Docs](https://wot-design-uni.cn/component/transition.html#transition)"}, "wd-upload": {"attributes": ["v-model:file", "action", "header", "multiple", "disabled", "reupload", "limit", "show-limit-num", "max-size", "source-type", "size-type", "name", "form-data", "header", "on-preview-fail", "before-upload", "before-choose", "before-remove", "before-preview", "build-form-data", "loading-type", "loading-color", "loading-size", "status-key", "image-mode", "accept", "compressed", "max-duration", "camera", "success-status", "auto-upload", "upload-method", "extension", "success", "fail", "progress", "chooseerror", "change", "remove", "oversize"], "description": "图片、视频和文件上传组件\n\n[Docs](https://wot-design-uni.cn/component/upload.html#upload)"}, "wd-watermark": {"attributes": ["content", "image", "image-height", "image-width", "gutter-x", "gutter-y", "width", "height", "full-screen", "color", "size", "font-style", "font-weight", "font-family", "rotate", "z-index", "opacity"], "description": "在页面或组件上添加指定的图片或文字，可用于版权保护、品牌宣传等场景。\n\n[Docs](https://wot-design-uni.cn/component/watermark.html#watermark)"}}