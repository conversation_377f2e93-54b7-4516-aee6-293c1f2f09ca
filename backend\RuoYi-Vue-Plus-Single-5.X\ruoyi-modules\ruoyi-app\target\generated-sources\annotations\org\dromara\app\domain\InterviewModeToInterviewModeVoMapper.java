package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__129;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.vo.InterviewModeVo;
import org.dromara.app.domain.vo.InterviewModeVoToInterviewModeMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__129.class,
    uses = {InterviewModeVoToInterviewModeMapper.class},
    imports = {}
)
public interface InterviewModeToInterviewModeVoMapper extends BaseMapper<InterviewMode, InterviewModeVo> {
}
