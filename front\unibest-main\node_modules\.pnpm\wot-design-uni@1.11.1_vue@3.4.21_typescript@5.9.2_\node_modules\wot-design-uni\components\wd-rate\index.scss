@import './../common/abstracts/_mixin.scss';
@import './../common/abstracts/variable.scss';

@include b(rate) {
  display: inline-block;
  vertical-align: middle;
  line-height: 1;

  @include e(item) {
    position: relative;
    display: inline-block;
    touch-action: none; // 禁用默认触摸行为
  }
  @include edeep(item-star) {
    -webkit-background-clip: text !important;
    color: transparent;
  }
  @include e(item-half) {
    position: absolute;
    left: 0;
    top: 0;
    overflow: hidden;
    width: 50%;
  }
}