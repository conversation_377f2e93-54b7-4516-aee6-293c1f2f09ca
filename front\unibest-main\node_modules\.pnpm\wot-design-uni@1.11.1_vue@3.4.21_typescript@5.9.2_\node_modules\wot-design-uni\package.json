{"id": "wot-design-uni", "name": "wot-design-uni", "displayName": "wot-design-uni 基于vue3+Typescript的高颜值组件库", "version": "1.11.1", "license": "MIT", "description": "一个基于Vue3+TS开发的uni-app组件库，提供70+高质量组件，支持暗黑模式、国际化和自定义主题。", "keywords": ["wot-design-uni", "国际化", "组件库", "vue3", "暗黑模式"], "main": "index.ts", "repository": {"type": "git", "url": "https://github.com/Moonofweisheng/wot-design-uni.git"}, "engines": {"HBuilderX": "^3.8.7"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": "https://www.npmjs.com/package/wot-design-uni"}, "vetur": {"tags": "tags.json", "attributes": "attributes.json"}, "web-types": "web-types.json", "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "n"}, "client": {"Vue": {"vue2": "n", "vue3": "y"}, "App": {"app-vue": "y", "app-nvue": "n", "app-uvue": "n"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "u", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "u", "字节跳动": "u", "QQ": "y", "钉钉": "y", "快手": "u", "飞书": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}, "peerDependencies": {"vue": ">=3.2.47"}}