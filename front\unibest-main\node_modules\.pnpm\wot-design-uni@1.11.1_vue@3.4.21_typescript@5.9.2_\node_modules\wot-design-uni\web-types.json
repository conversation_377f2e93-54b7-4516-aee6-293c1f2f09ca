{"$schema": "http://json.schemastore.org/web-types", "framework": "vue", "name": "wot-design-uni", "version": "1.11.1", "js-types-syntax": "typescript", "description-markup": "markdown", "contributions": {"html": {"vue-components": [{"name": "wd-action-sheet", "source": {"symbol": "WdActionSheet"}, "description": "从底部弹出的动作菜单面板。", "doc-url": "https://wot-design-uni.cn/component/action-sheet.html#actionsheet", "props": [{"name": "v-model", "description": "设置菜单显示隐藏", "doc-url": "https://wot-design-uni.cn/component/action-sheet.html#attributes", "type": ["boolean"]}, {"name": "actions", "description": "菜单选项", "doc-url": "https://wot-design-uni.cn/component/action-sheet.html#attributes", "type": ["array"], "default": "[]"}, {"name": "panels", "description": "自定义面板项,可以为字符串数组，也可以为对象数组，如果为二维数组，则为多行展示", "doc-url": "https://wot-design-uni.cn/component/action-sheet.html#attributes", "type": ["array"], "default": "[]"}, {"name": "title", "description": "标题", "doc-url": "https://wot-design-uni.cn/component/action-sheet.html#attributes", "type": ["string"]}, {"name": "cancel-text", "description": "取消按钮文案", "doc-url": "https://wot-design-uni.cn/component/action-sheet.html#attributes", "type": ["string"]}, {"name": "close-on-click-action", "description": "点击选项后是否关闭菜单", "doc-url": "https://wot-design-uni.cn/component/action-sheet.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "close-on-click-modal", "description": "点击遮罩是否关闭", "doc-url": "https://wot-design-uni.cn/component/action-sheet.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "duration", "description": "动画持续时间", "doc-url": "https://wot-design-uni.cn/component/action-sheet.html#attributes", "type": ["number"], "default": "200(ms)"}, {"name": "z-index", "description": "菜单层级", "doc-url": "https://wot-design-uni.cn/component/action-sheet.html#attributes", "type": ["number"], "default": "10"}, {"name": "lazy-render", "description": "弹层内容懒渲染，触发展示时才渲染内容", "doc-url": "https://wot-design-uni.cn/component/action-sheet.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "safe-area-inset-bottom", "description": "弹出面板是否设置底部安全距离（iphone X 类型的机型）", "doc-url": "https://wot-design-uni.cn/component/action-sheet.html#attributes", "type": ["boolean"], "default": "true"}], "js": {"events": [{"name": "select", "description": "点击选项时触发", "doc-url": "https://wot-design-uni.cn/component/action-sheet.html#events"}, {"name": "open", "description": "弹出层打开时触发", "doc-url": "https://wot-design-uni.cn/component/action-sheet.html#events"}, {"name": "opened", "description": "弹出层打开动画结束时触发", "doc-url": "https://wot-design-uni.cn/component/action-sheet.html#events"}, {"name": "close", "description": "弹出层关闭时触发", "doc-url": "https://wot-design-uni.cn/component/action-sheet.html#events"}, {"name": "closed", "description": "弹出层关闭动画结束时触发", "doc-url": "https://wot-design-uni.cn/component/action-sheet.html#events"}, {"name": "click-modal", "description": "点击遮罩时触发", "doc-url": "https://wot-design-uni.cn/component/action-sheet.html#events"}, {"name": "cancel", "description": "点击取消按钮时触发", "doc-url": "https://wot-design-uni.cn/component/action-sheet.html#events"}]}}, {"name": "wd-backtop", "source": {"symbol": "WdBacktop"}, "description": "用于返回页面顶部的操作按钮。", "doc-url": "https://wot-design-uni.cn/component/backtop.html#backtop", "props": [{"name": "scroll-top", "description": "页面滚动距离", "doc-url": "https://wot-design-uni.cn/component/backtop.html#attributes", "type": ["number"]}, {"name": "top", "description": "距离顶部多少距离时显示，单位`px`", "doc-url": "https://wot-design-uni.cn/component/backtop.html#attributes", "type": ["number"], "default": "300"}, {"name": "duration", "description": "返回顶部滚动时间，单位`ms`", "doc-url": "https://wot-design-uni.cn/component/backtop.html#attributes", "type": ["number"], "default": "100"}, {"name": "z-index", "description": "组件z-index属性", "doc-url": "https://wot-design-uni.cn/component/backtop.html#attributes", "type": ["number"], "default": "10"}, {"name": "icon-style", "description": "自定义`icon`样式", "doc-url": "https://wot-design-uni.cn/component/backtop.html#attributes", "type": ["string"]}, {"name": "shape", "description": "按钮形状", "doc-url": "https://wot-design-uni.cn/component/backtop.html#attributes", "type": ["string"], "default": "circle", "attribute-value": {"type": "enum"}, "values": [{"name": "square"}]}, {"name": "bottom", "description": "距离屏幕底部的距离，单位`px`", "doc-url": "https://wot-design-uni.cn/component/backtop.html#attributes", "type": ["number"], "default": "100"}, {"name": "right", "description": "距离屏幕右边距离，单位`px`", "doc-url": "https://wot-design-uni.cn/component/backtop.html#attributes", "type": ["number"], "default": "20"}]}, {"name": "wd-badge", "source": {"symbol": "WdBadge"}, "description": "出现在按钮、图标旁的数字或状态标记。", "doc-url": "https://wot-design-uni.cn/component/badge.html#badge", "props": [{"name": "v-model", "description": "显示值", "doc-url": "https://wot-design-uni.cn/component/badge.html#attributes", "type": ["string", "number"]}, {"name": "max", "description": "最大值，超过最大值会显示 '{max}+'，要求 value 是 Number 类型", "doc-url": "https://wot-design-uni.cn/component/badge.html#attributes", "type": ["number"]}, {"name": "top", "description": "为正时，角标向下偏移对应的像素", "doc-url": "https://wot-design-uni.cn/component/badge.html#attributes", "type": ["number"]}, {"name": "right", "description": "为正时，角标向左偏移对应的像素", "doc-url": "https://wot-design-uni.cn/component/badge.html#attributes", "type": ["number"]}, {"name": "is-dot", "description": "红色点状标注", "doc-url": "https://wot-design-uni.cn/component/badge.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "hidden", "description": "隐藏 badge", "doc-url": "https://wot-design-uni.cn/component/badge.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "type", "description": "类型", "doc-url": "https://wot-design-uni.cn/component/badge.html#attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "primary"}, {"name": "success"}, {"name": "warning"}, {"name": "danger"}, {"name": "info"}]}, {"name": "bg-color", "description": "背景色", "doc-url": "https://wot-design-uni.cn/component/badge.html#attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "各种颜色的css写法"}]}, {"name": "show-zero", "description": "是否显示0", "doc-url": "https://wot-design-uni.cn/component/badge.html#attributes", "type": ["boolean"], "default": "false"}]}, {"name": "wd-button", "source": {"symbol": "WdButton"}, "description": "按钮用于触发一个操作，如提交表单或打开链接。", "doc-url": "https://wot-design-uni.cn/component/button.html#button", "props": [{"name": "type", "description": "按钮类型", "doc-url": "https://wot-design-uni.cn/component/button.html#attributes", "type": ["string"], "default": "primary", "attribute-value": {"type": "enum"}, "values": [{"name": "primary"}, {"name": "success"}, {"name": "info"}, {"name": "warning"}, {"name": "error"}, {"name": "text"}, {"name": "icon"}]}, {"name": "round", "description": "圆角按钮", "doc-url": "https://wot-design-uni.cn/component/button.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "plain", "description": "幽灵按钮", "doc-url": "https://wot-design-uni.cn/component/button.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "hairline", "description": "是否细边框", "doc-url": "https://wot-design-uni.cn/component/button.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "loading", "description": "加载中按钮", "doc-url": "https://wot-design-uni.cn/component/button.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "block", "description": "块状按钮", "doc-url": "https://wot-design-uni.cn/component/button.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "size", "description": "按钮尺寸", "doc-url": "https://wot-design-uni.cn/component/button.html#attributes", "type": ["string"], "default": "medium", "attribute-value": {"type": "enum"}, "values": [{"name": "small"}, {"name": "medium"}, {"name": "large"}]}, {"name": "disabled", "description": "禁用按钮", "doc-url": "https://wot-design-uni.cn/component/button.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "icon", "description": "图标类名", "doc-url": "https://wot-design-uni.cn/component/button.html#attributes", "type": ["string"]}, {"name": "loading-color", "description": "加载图标颜色", "doc-url": "https://wot-design-uni.cn/component/button.html#attributes", "type": ["string"]}, {"name": "open-type", "description": "微信开放能力", "doc-url": "https://wot-design-uni.cn/component/button.html#attributes", "type": ["string"]}, {"name": "hover-stop-propagation", "description": "指定是否阻止本节点的祖先节点出现点击态", "doc-url": "https://wot-design-uni.cn/component/button.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "lang", "description": "指定返回用户信息的语言，zh_CN 简体中文，zh_TW 繁体中文，en 英文", "doc-url": "https://wot-design-uni.cn/component/button.html#attributes", "type": ["string"], "default": "en", "attribute-value": {"type": "enum"}, "values": [{"name": "zh_CN"}, {"name": "zh_TW"}]}, {"name": "session-from", "description": "会话来源，open-type=\"contact\"时有效", "doc-url": "https://wot-design-uni.cn/component/button.html#attributes", "type": ["string"]}, {"name": "session-message-title", "description": "会话内消息卡片标题，open-type=\"contact\"时有效", "doc-url": "https://wot-design-uni.cn/component/button.html#attributes", "type": ["string"], "default": "当前标题"}, {"name": "session-message-path", "description": "会话内消息卡片点击跳转小程序路径，open-type=\"contact\"时有效", "doc-url": "https://wot-design-uni.cn/component/button.html#attributes", "type": ["string"], "default": "当前分享路径"}, {"name": "send-message-img", "description": "会话内消息卡片图片，open-type=\"contact\"时有效", "doc-url": "https://wot-design-uni.cn/component/button.html#attributes", "type": ["string"], "default": "截图"}, {"name": "app-parameter", "description": "打开 APP 时，向 APP 传递的参数，open-type=launchApp 时有效", "doc-url": "https://wot-design-uni.cn/component/button.html#attributes", "type": ["string"]}, {"name": "show-message-card", "description": "是否显示会话内消息卡片，设置此参数为 true，用户进入客服会话会在右下角显示\"可能要发送的小程序\"提示，用户点击后可以快速发送小程序消息，open-type=\"contact\"时有效", "doc-url": "https://wot-design-uni.cn/component/button.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "class-prefix", "description": "类名前缀，用于使用自定义图标，参见[icon](/component/icon#自定义图标)", "doc-url": "https://wot-design-uni.cn/component/button.html#attributes", "type": ["string"], "default": "wd-icon"}, {"name": "button-id", "description": "按钮的唯一标识，可用于设置隐私同意授权按钮的 id", "doc-url": "https://wot-design-uni.cn/component/button.html#attributes", "type": ["string"]}, {"name": "scope", "description": "支付宝小程序使用，当 open-type 为 getAuthorize 时有效。", "doc-url": "https://wot-design-uni.cn/component/button.html#attributes", "type": [{"name": "ButtonScope", "source": {"symbol": "ButtonScope"}}], "attribute-value": {"type": "of-match"}, "values": [{"name": "phoneNumber"}, {"name": "userInfo"}]}], "js": {"events": [{"name": "click", "description": "点击事件", "doc-url": "https://wot-design-uni.cn/component/button.html#events"}, {"name": "getuserinfo", "description": "获取用户信息", "doc-url": "https://wot-design-uni.cn/component/button.html#events"}, {"name": "contact", "description": "客服消息回调，open-type=\"contact\"时有效", "doc-url": "https://wot-design-uni.cn/component/button.html#events"}, {"name": "getphonenumber", "description": "获取用户手机号回调，open-type=getPhoneNumber 时有效", "doc-url": "https://wot-design-uni.cn/component/button.html#events"}, {"name": "error", "description": "当使用开放能力时，发生错误的回调，open-type=launchApp 时有效", "doc-url": "https://wot-design-uni.cn/component/button.html#events"}, {"name": "launchapp", "description": "打开 APP 成功的回调，open-type=launchApp 时有效", "doc-url": "https://wot-design-uni.cn/component/button.html#events"}, {"name": "opensetting", "description": "在打开授权设置页后回调，open-type=openSetting 时有效", "doc-url": "https://wot-design-uni.cn/component/button.html#events"}]}}, {"name": "wd-calendar-view", "source": {"symbol": "WdCalendarView"}, "description": "提供日历单选、多选、范围选择、周维度、月维度等功能。可以根据实际业务场景基于该组件进行封装高度定制化组件。", "doc-url": "https://wot-design-uni.cn/component/calendar-view.html#calendarview", "props": [{"name": "v-model", "description": "选中值，为 13 位时间戳或时间戳数组", "doc-url": "https://wot-design-uni.cn/component/calendar-view.html#attributes", "type": ["null", "number", "array"]}, {"name": "type", "description": "日期类型", "doc-url": "https://wot-design-uni.cn/component/calendar-view.html#attributes", "type": ["string"], "default": "date", "attribute-value": {"type": "enum"}, "values": [{"name": "date"}, {"name": "dates"}, {"name": "datetime"}, {"name": "week"}, {"name": "month"}, {"name": "daterange"}, {"name": "datetimerange"}, {"name": "weekrange"}, {"name": "monthrange"}]}, {"name": "min-date", "description": "最小日期，为 13 位时间戳", "doc-url": "https://wot-design-uni.cn/component/calendar-view.html#attributes", "type": ["number"], "default": "当前日期往前推 6 个月"}, {"name": "max-date", "description": "最大日期，为 13 位时间戳", "doc-url": "https://wot-design-uni.cn/component/calendar-view.html#attributes", "type": ["number"], "default": "当前日期往后推 6 个月"}, {"name": "first-day-of-week", "description": "周起始天", "doc-url": "https://wot-design-uni.cn/component/calendar-view.html#attributes", "type": ["number"], "default": "0"}, {"name": "formatter", "description": "日期格式化函数", "doc-url": "https://wot-design-uni.cn/component/calendar-view.html#attributes", "type": ["function"]}, {"name": "max-range", "description": "type 为范围选择时有效，最大日期范围", "doc-url": "https://wot-design-uni.cn/component/calendar-view.html#attributes", "type": ["number"]}, {"name": "range-prompt", "description": "type 为范围选择时有效，选择超出最大日期范围时的错误提示文案", "doc-url": "https://wot-design-uni.cn/component/calendar-view.html#attributes", "type": ["string"], "default": "选择天数不能超过 x 天"}, {"name": "allow-same-day", "description": "type 为范围选择时有效，是否允许选择同一天", "doc-url": "https://wot-design-uni.cn/component/calendar-view.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "show-panel-title", "description": "是否展示面板标题，自动计算当前滚动的日期月份", "doc-url": "https://wot-design-uni.cn/component/calendar-view.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "default-time", "description": "选中日期所使用的当日内具体时刻", "doc-url": "https://wot-design-uni.cn/component/calendar-view.html#attributes", "type": ["string", "array"], "default": "00:00:00"}, {"name": "panel-height", "description": "可滚动面板的高度", "doc-url": "https://wot-design-uni.cn/component/calendar-view.html#attributes", "type": ["number"], "default": "378"}, {"name": "time-filter", "description": "type 为 'datetime' 或 'datetimerange' 时有效，用于过滤时间选择器的数据", "doc-url": "https://wot-design-uni.cn/component/calendar-view.html#attributes", "type": ["function"]}, {"name": "hide-second", "description": "type 为 'datetime' 或 'datetimerange' 时有效，是否不展示秒修改", "doc-url": "https://wot-design-uni.cn/component/calendar-view.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "immediate-change", "description": "type 为 'datetime' 或 'datetimerange' 时有，是否在手指松开时立即触发 picker-view 的 change 事件。若不开启则会在滚动动画结束后触发 change 事件，1.2.25 版本起提供，仅微信小程序和支付宝小程序支持。", "doc-url": "https://wot-design-uni.cn/component/calendar-view.html#attributes", "type": ["boolean"], "default": "false"}], "js": {"events": [{"name": "change", "description": "绑定值变化时触发", "doc-url": "https://wot-design-uni.cn/component/calendar-view.html#events"}]}}, {"name": "wd-calendar", "source": {"symbol": "WdCalendar"}, "description": "提供日历单选、多选、范围选择、周维度、月维度等功能。", "doc-url": "https://wot-design-uni.cn/component/calendar.html#calendar", "props": [{"name": "v-model", "description": "选中值，为 13 位时间戳或时间戳数组", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["null", "number", "array"]}, {"name": "type", "description": "日期类型", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["string"], "default": "date", "attribute-value": {"type": "enum"}, "values": [{"name": "date"}, {"name": "dates"}, {"name": "datetime"}, {"name": "week"}, {"name": "month"}, {"name": "daterange"}, {"name": "datetimerange"}, {"name": "weekrange"}, {"name": "monthrange"}]}, {"name": "min-date", "description": "最小日期，为 13 位时间戳", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["number"], "default": "当前日期往前推 6 个月"}, {"name": "max-date", "description": "最大日期，为 13 位时间戳", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["number"], "default": "当前日期往后推 6 个月"}, {"name": "first-day-of-week", "description": "周起始天", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["number"], "default": "0"}, {"name": "formatter", "description": "日期格式化函数", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["function"]}, {"name": "max-range", "description": "type 为范围选择时有效，最大日期范围", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["number"]}, {"name": "range-prompt", "description": "type 为范围选择时有效，选择超出最大日期范围时的错误提示文案", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["string"], "default": "选择天数不能超过 x 天"}, {"name": "allow-same-day", "description": "type 为范围选择时有效，是否允许选择同一天", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "default-time", "description": "选中日期所使用的当日内具体时刻", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["string", "array"], "default": "00:00:00"}, {"name": "time-filter", "description": "type 为 'datetime' 或 'datetimerange' 时有效，用于过滤时间选择器的数据", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["function"]}, {"name": "hide-second", "description": "type 为 'datetime' 或 'datetimerange' 时有效，是否不展示秒修改", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "show-confirm", "description": "是否显示确定按钮", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "show-type-switch", "description": "是否显示类型切换功能", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "shortcuts", "description": "快捷选项，为对象数组，其中对象的 `text` 必传", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["array"]}, {"name": "title", "description": "弹出层标题", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["string"], "default": "选择日期"}, {"name": "label", "description": "选择器左侧文案", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["string"]}, {"name": "placeholder", "description": "选择器占位符", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["string"], "default": "请选择"}, {"name": "disabled", "description": "禁用", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "readonly", "description": "只读", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "display-format", "description": "自定义展示文案的格式化函数，返回一个字符串", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["function"]}, {"name": "inner-display-format", "description": "自定义范围选择类型的面板内部回显，返回一个字符串", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["function"]}, {"name": "size", "description": "设置选择器大小", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "large"}]}, {"name": "label-width", "description": "设置左侧标题宽度", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["string"], "default": "33%"}, {"name": "error", "description": "是否为错误状态，错误状态时右侧内容为红色", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "required", "description": "必填样式", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "center", "description": "是否垂直居中", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "ellipsis", "description": "是否超出隐藏", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "align-right", "description": "选择器的值靠右展示", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "before-confirm", "description": "确定前校验函数，接收 { value, resolve } 参数，通过 resolve 继续执行，resolve 接收 1 个 boolean 参数", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["function"]}, {"name": "suse-default-slots", "description": "<s>使用默认插槽时设置该选项</s>，已废弃直接使用默认插槽即可。", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "suse-label-slots", "description": "<s>使用 label 插槽时设置该选项</s>，已废弃直接使用 label 插槽即可。", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "close-on-click-modal", "description": "点击遮罩是否关闭", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "z-index", "description": "弹窗层级", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["number"], "default": "15"}, {"name": "safe-area-inset-bottom", "description": "弹出面板是否设置底部安全距离（iphone X 类型的机型）", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "prop", "description": "表单域 `model` 字段名，在使用表单校验功能的情况下，该属性是必填的", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["string"]}, {"name": "rules", "description": "表单验证规则，结合`wd-form`组件使用", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": [{"name": "FormItemRule []", "source": {"symbol": "FormItemRule "}}], "default": "[]"}, {"name": "immediate-change", "description": "type 为 'datetime' 或 'datetimerange' 时有，是否在手指松开时立即触发 picker-view 的 change 事件。若不开启则会在滚动动画结束后触发 change 事件，1.2.25 版本起提供，仅微信小程序和支付宝小程序支持。", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "with-cell", "description": "是否使用内置 cell 选择器", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "clearable", "description": "显示清空按钮", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "root-portal", "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题", "doc-url": "https://wot-design-uni.cn/component/calendar.html#attributes", "type": ["boolean"], "default": "false"}], "js": {"events": [{"name": "confirm", "description": "绑定值变化时触发", "doc-url": "https://wot-design-uni.cn/component/calendar.html#events"}, {"name": "change", "description": "点击面板日期时触发", "doc-url": "https://wot-design-uni.cn/component/calendar.html#events"}, {"name": "cancel", "description": "点击关闭按钮或者蒙层时触发", "doc-url": "https://wot-design-uni.cn/component/calendar.html#events"}, {"name": "open", "description": "日历打开时触发", "doc-url": "https://wot-design-uni.cn/component/calendar.html#events"}, {"name": "clear", "description": "点击清空按钮时触发", "doc-url": "https://wot-design-uni.cn/component/calendar.html#events"}]}}, {"name": "wd-card", "source": {"symbol": "WdCard"}, "description": "用于展示商品的图片、价格等信息。", "doc-url": "https://wot-design-uni.cn/component/card.html#card", "props": [{"name": "title", "description": "卡片标题", "doc-url": "https://wot-design-uni.cn/component/card.html#attributes", "type": ["string"]}, {"name": "type", "description": "卡片类型", "doc-url": "https://wot-design-uni.cn/component/card.html#attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "rectangle"}]}]}, {"name": "wd-cell-group", "source": {"symbol": "WdCellGroup"}, "description": "单元格为列表中的单个展示项。", "doc-url": "https://wot-design-uni.cn/component/cell.html#cellgroup", "props": [{"name": "title", "description": "分组标题", "doc-url": "https://wot-design-uni.cn/component/cell.html#cellgroup-attributes", "type": ["string"]}, {"name": "value", "description": "分组右侧内容", "doc-url": "https://wot-design-uni.cn/component/cell.html#cellgroup-attributes", "type": ["string"]}, {"name": "border", "description": "是否展示边框线", "doc-url": "https://wot-design-uni.cn/component/cell.html#cellgroup-attributes", "type": ["boolean"]}, {"name": "use-slot", "description": "分组启用插槽", "doc-url": "https://wot-design-uni.cn/component/cell.html#cellgroup-attributes", "type": ["boolean"], "default": "false"}]}, {"name": "wd-cell", "source": {"symbol": "WdCell"}, "description": "单元格为列表中的单个展示项。", "doc-url": "https://wot-design-uni.cn/component/cell.html#cell", "props": [{"name": "title", "description": "标题", "doc-url": "https://wot-design-uni.cn/component/cell.html#cell-attributes", "type": ["string"]}, {"name": "value", "description": "右侧内容", "doc-url": "https://wot-design-uni.cn/component/cell.html#cell-attributes", "type": ["string"]}, {"name": "icon", "description": "图标类名", "doc-url": "https://wot-design-uni.cn/component/cell.html#cell-attributes", "type": ["string"]}, {"name": "label", "description": "描述信息", "doc-url": "https://wot-design-uni.cn/component/cell.html#cell-attributes", "type": ["string"]}, {"name": "is-link", "description": "是否为跳转链接", "doc-url": "https://wot-design-uni.cn/component/cell.html#cell-attributes", "type": ["boolean"], "default": "false"}, {"name": "to", "description": "跳转地址", "doc-url": "https://wot-design-uni.cn/component/cell.html#cell-attributes", "type": ["string"]}, {"name": "clickable", "description": "点击反馈，开启 is-link 时，默认开启此选项", "doc-url": "https://wot-design-uni.cn/component/cell.html#cell-attributes", "type": ["boolean"], "default": "false"}, {"name": "replace", "description": "跳转时是否替换栈顶页面", "doc-url": "https://wot-design-uni.cn/component/cell.html#cell-attributes", "type": ["boolean"], "default": "false"}, {"name": "size", "description": "设置单元格大小", "doc-url": "https://wot-design-uni.cn/component/cell.html#cell-attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "large"}]}, {"name": "title-width", "description": "设置左侧标题宽度", "doc-url": "https://wot-design-uni.cn/component/cell.html#cell-attributes", "type": ["string"]}, {"name": "center", "description": "是否垂直居中，默认顶部居中", "doc-url": "https://wot-design-uni.cn/component/cell.html#cell-attributes", "type": ["boolean"], "default": "false"}, {"name": "required", "description": "表单属性，必填", "doc-url": "https://wot-design-uni.cn/component/cell.html#cell-attributes", "type": ["boolean"], "default": "false"}, {"name": "vertical", "description": "表单属性，上下结构", "doc-url": "https://wot-design-uni.cn/component/cell.html#cell-attributes", "type": ["boolean"], "default": "false"}, {"name": "ellipsis", "description": "内容省略，右侧内容超出时会以省略号显示", "doc-url": "https://wot-design-uni.cn/component/cell.html#cell-attributes", "type": ["boolean"], "default": "false"}, {"name": "use-title-slot", "description": "是否启用title插槽，默认启用，用来解决插槽传递时v-slot和v-if冲突问题", "doc-url": "https://wot-design-uni.cn/component/cell.html#cell-attributes", "type": ["boolean"], "default": "true"}, {"name": "prop", "description": "表单域 `model` 字段名，在使用表单校验功能的情况下，该属性是必填的", "doc-url": "https://wot-design-uni.cn/component/cell.html#cell-attributes", "type": ["string"]}, {"name": "rules", "description": "表单验证规则，结合`wd-form`组件使用", "doc-url": "https://wot-design-uni.cn/component/cell.html#cell-attributes", "type": [{"name": "FormItemRule []", "source": {"symbol": "FormItemRule "}}], "default": "[]"}, {"name": "border", "description": "是否展示边框线，优先级高于`cell-group`的`border`", "doc-url": "https://wot-design-uni.cn/component/cell.html#cell-attributes", "type": ["boolean"]}], "js": {"events": [{"name": "click", "description": "当 clickable 或 is-link 为 true 时点击单元格触发", "doc-url": "https://wot-design-uni.cn/component/cell.html#cell-events"}]}}, {"name": "wd-checkbox", "source": {"symbol": "WdCheckbox"}, "description": "复选框用于在一组备选项中进行多选。", "doc-url": "https://wot-design-uni.cn/component/checkbox.html#checkbox", "props": [{"name": "v-model", "description": "单选框选中时的值", "doc-url": "https://wot-design-uni.cn/component/checkbox.html#checkbox-attributes", "type": ["string", "number", "boolean"]}, {"name": "shape", "description": "单选框形状", "doc-url": "https://wot-design-uni.cn/component/checkbox.html#checkbox-attributes", "type": ["string"], "default": "circle", "attribute-value": {"type": "enum"}, "values": [{"name": "circle"}, {"name": "square"}, {"name": "button"}]}, {"name": "checked-color", "description": "选中的颜色", "doc-url": "https://wot-design-uni.cn/component/checkbox.html#checkbox-attributes", "type": ["string"], "default": "#4D80F0"}, {"name": "disabled", "description": "禁用", "doc-url": "https://wot-design-uni.cn/component/checkbox.html#checkbox-attributes", "type": ["boolean"], "default": "false"}, {"name": "max-width", "description": "文字位置最大宽度", "doc-url": "https://wot-design-uni.cn/component/checkbox.html#checkbox-attributes", "type": ["string"]}, {"name": "true-value", "description": "选中值，在 checkbox-group 中使用无效，需同 false-value 一块使用", "doc-url": "https://wot-design-uni.cn/component/checkbox.html#checkbox-attributes", "type": ["string", "number"], "default": "true"}, {"name": "false-value", "description": "非选中时的值，在 checkbox-group 中使用无效，需同 true-value 一块使用", "doc-url": "https://wot-design-uni.cn/component/checkbox.html#checkbox-attributes", "type": ["string", "number"], "default": "false"}, {"name": "size", "description": "设置大小", "doc-url": "https://wot-design-uni.cn/component/checkbox.html#checkbox-attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "large"}]}], "js": {"events": [{"name": "change", "description": "绑定值变化时触发，当为复选框组时参数为 boolean，表示该复选框是否选中", "doc-url": "https://wot-design-uni.cn/component/checkbox.html#checkbox-events"}]}}, {"name": "wd-checkbox-group", "source": {"symbol": "WdCheckboxGroup"}, "description": "复选框用于在一组备选项中进行多选。", "doc-url": "https://wot-design-uni.cn/component/checkbox.html#checkboxgroup", "props": [{"name": "v-model", "description": "绑定值", "doc-url": "https://wot-design-uni.cn/component/checkbox.html#checkboxgroup-attributes", "type": ["Array"]}, {"name": "shape", "description": "单选框形状", "doc-url": "https://wot-design-uni.cn/component/checkbox.html#checkboxgroup-attributes", "type": ["string"], "default": "circle", "attribute-value": {"type": "enum"}, "values": [{"name": "circle"}, {"name": "square"}, {"name": "button"}]}, {"name": "cell", "description": "表单模式", "doc-url": "https://wot-design-uni.cn/component/checkbox.html#checkboxgroup-attributes", "type": ["boolean"], "default": "false"}, {"name": "checked-color", "description": "选中的颜色", "doc-url": "https://wot-design-uni.cn/component/checkbox.html#checkboxgroup-attributes", "type": ["string"], "default": "#4D80F0"}, {"name": "disabled", "description": "禁用", "doc-url": "https://wot-design-uni.cn/component/checkbox.html#checkboxgroup-attributes", "type": ["boolean"], "default": "false"}, {"name": "min", "description": "最小选中的数量", "doc-url": "https://wot-design-uni.cn/component/checkbox.html#checkboxgroup-attributes", "type": ["number"], "default": "0"}, {"name": "max", "description": "最大选中的数量，0 为无限数量，默认为 0", "doc-url": "https://wot-design-uni.cn/component/checkbox.html#checkboxgroup-attributes", "type": ["number"], "default": "0"}, {"name": "inline", "description": "同行展示", "doc-url": "https://wot-design-uni.cn/component/checkbox.html#checkboxgroup-attributes", "type": ["boolean"], "default": "false"}, {"name": "size", "description": "设置大小", "doc-url": "https://wot-design-uni.cn/component/checkbox.html#checkboxgroup-attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "large"}]}], "js": {"events": [{"name": "change", "description": "绑定值变化时触发", "doc-url": "https://wot-design-uni.cn/component/checkbox.html#checkboxgroup-events"}]}}, {"name": "wd-circle", "source": {"symbol": "WdCircle"}, "description": "圆环形的进度条组件，支持进度渐变动画。", "doc-url": "https://wot-design-uni.cn/component/circle.html#circle", "props": [{"name": "v-model", "description": "当前进度", "doc-url": "https://wot-design-uni.cn/component/circle.html#attributes", "type": ["number"], "default": "0"}, {"name": "custom-class", "description": "自定义class", "doc-url": "https://wot-design-uni.cn/component/circle.html#attributes", "type": ["string"]}, {"name": "custom-style", "description": "自定义style", "doc-url": "https://wot-design-uni.cn/component/circle.html#attributes", "type": ["string"]}, {"name": "size", "description": "圆环直径，默认单位为 px", "doc-url": "https://wot-design-uni.cn/component/circle.html#attributes", "type": ["number"], "default": "100"}, {"name": "color", "description": "进度条颜色", "doc-url": "https://wot-design-uni.cn/component/circle.html#attributes", "type": ["string", "Record<string, string>"], "default": "#4d80f0"}, {"name": "layer-color", "description": "轨道颜色", "doc-url": "https://wot-design-uni.cn/component/circle.html#attributes", "type": ["string"], "default": "#EBEEF5"}, {"name": "fill", "description": "填充颜色", "doc-url": "https://wot-design-uni.cn/component/circle.html#attributes", "type": ["string"], "default": "#ffffff"}, {"name": "speed", "description": "动画速度（单位为 rate/s）", "doc-url": "https://wot-design-uni.cn/component/circle.html#attributes", "type": ["number"], "default": "50"}, {"name": "text", "description": "文字", "doc-url": "https://wot-design-uni.cn/component/circle.html#attributes", "type": ["string"]}, {"name": "stroke-width", "description": "进度条宽度，单位px", "doc-url": "https://wot-design-uni.cn/component/circle.html#attributes", "type": ["number"], "default": "10"}, {"name": "stroke-linecap", "description": "进度条端点的形状", "doc-url": "https://wot-design-uni.cn/component/circle.html#attributes", "type": ["string"], "default": "round", "attribute-value": {"type": "enum"}, "values": [{"name": "butt"}, {"name": "round"}, {"name": "square"}]}, {"name": "clockwise", "description": "是否顺时针增加", "doc-url": "https://wot-design-uni.cn/component/circle.html#attributes", "type": ["boolean"], "default": "true"}]}, {"name": "wd-col-picker", "source": {"symbol": "WdColPicker"}, "description": "使用多列选择器来做级联，交互效果较好，多列选择器支持无限级选择。", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#colpicker", "props": [{"name": "v-model", "description": "选中项", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["array"]}, {"name": "columns", "description": "选择器数据，二维数组", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["array"]}, {"name": "value-key", "description": "选项对象中，value 对应的 key", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["string"], "default": "value"}, {"name": "label-key", "description": "选项对象中，展示的文本对应的 key", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["string"], "default": "label"}, {"name": "tip-key", "description": "选项对象中，提示文案对应的 key", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["string"], "default": "tip"}, {"name": "title", "description": "弹出层标题", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["string"]}, {"name": "label", "description": "选择器左侧文案", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["string"]}, {"name": "placeholder", "description": "选择器占位符", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["string"], "default": "请选择"}, {"name": "disabled", "description": "禁用", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "readonly", "description": "只读", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "display-format", "description": "自定义展示文案的格式化函数，返回一个字符串", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["function"]}, {"name": "column-change", "description": "接收当前列的选中项 item、当前列下标、当前列选中项下标下一列数据处理函数 resolve、结束选择 finish", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["function"]}, {"name": "size", "description": "设置选择器大小", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "large"}]}, {"name": "label-width", "description": "设置左侧标题宽度", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["string"], "default": "33%"}, {"name": "error", "description": "是否为错误状态，错误状态时右侧内容为红色", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "required", "description": "必填样式", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "align-right", "description": "选择器的值靠右展示", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "before-confirm", "description": "确定前校验函数，接收 (value, resolve) 参数，通过 resolve 继续执行 picker，resolve 接收 1 个 boolean 参数", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["function"]}, {"name": "loading-color", "description": "loading 图标的颜色", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["string"], "default": "#4D80F0"}, {"name": "use-default-slot", "description": "使用默认插槽时设置该选项", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "use-label-slot", "description": "使用 label 插槽时设置该选项", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "close-on-click-modal", "description": "点击遮罩是否关闭", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "auto-complete", "description": "自动触发 column-change 事件来补全数据，当 columns 为空数组或者 columns 数组长度小于 value 数组长度时，会自动触发 column-change", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": [{"name": "-", "source": {"symbol": "-"}}], "attribute-value": {"type": "of-match"}, "values": [{"name": "false"}]}, {"name": "z-index", "description": "弹窗层级", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["number"], "default": "15"}, {"name": "safe-area-inset-bottom", "description": "弹出面板是否设置底部安全距离（iphone X 类型的机型）", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "ellipsis", "description": "是否超出隐藏", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "prop", "description": "表单域 `model` 字段名，在使用表单校验功能的情况下，该属性是必填的", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["string"]}, {"name": "rules", "description": "表单验证规则，结合`wd-form`组件使用", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": [{"name": "FormItemRule []", "source": {"symbol": "FormItemRule "}}], "default": "[]"}, {"name": "line-width", "description": "底部条宽度，单位像素", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["number"]}, {"name": "line-height", "description": "底部条高度，单位像素", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["number"]}, {"name": "root-portal", "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#attributes", "type": ["boolean"], "default": "false"}], "js": {"events": [{"name": "confirm", "description": "最后一列选项选中时触发", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#events"}, {"name": "close", "description": "点击关闭按钮或者蒙层时触发", "doc-url": "https://wot-design-uni.cn/component/col-picker.html#events"}]}}, {"name": "wd-collapse-item", "source": {"symbol": "WdCollapseItem"}, "description": "将一组内容放置在多个折叠面板中，点击面板的标题可以展开或收缩其内容。", "doc-url": "https://wot-design-uni.cn/component/collapse.html#collapseitem", "props": [{"name": "name", "description": "折叠栏的标识符", "doc-url": "https://wot-design-uni.cn/component/collapse.html#collapseitem-attributes", "type": ["string"]}, {"name": "title", "description": "折叠栏的标题, 支持同名 slot 自定义内容", "doc-url": "https://wot-design-uni.cn/component/collapse.html#collapseitem-attributes", "type": ["string"]}, {"name": "disabled", "description": "禁用折叠栏", "doc-url": "https://wot-design-uni.cn/component/collapse.html#collapseitem-attributes", "type": ["boolean"], "default": "false"}, {"name": "before-expend", "description": "打开前的回调函数，返回 false 可以阻止打开，支持返回 Promise", "doc-url": "https://wot-design-uni.cn/component/collapse.html#collapseitem-attributes", "type": ["Function"]}]}, {"name": "wd-collapse", "source": {"symbol": "WdCollapse"}, "description": "将一组内容放置在多个折叠面板中，点击面板的标题可以展开或收缩其内容。", "doc-url": "https://wot-design-uni.cn/component/collapse.html#collapse", "props": [{"name": "value", "description": "绑定值", "doc-url": "https://wot-design-uni.cn/component/collapse.html#collapse-attributes", "type": ["string", "array", "boolean"]}, {"name": "accordion", "description": "手风琴", "doc-url": "https://wot-design-uni.cn/component/collapse.html#collapse-attributes", "type": ["boolean"], "default": "false"}, {"name": "viewmore", "description": "查看更多的折叠面板", "doc-url": "https://wot-design-uni.cn/component/collapse.html#collapse-attributes", "type": ["boolean"], "default": "false"}, {"name": "use-more-slot", "description": "查看更多的自定义插槽使用标志", "doc-url": "https://wot-design-uni.cn/component/collapse.html#collapse-attributes", "type": ["boolean"], "default": "false"}, {"name": "line-num", "description": "查看更多的折叠面板，收起时的显示行数", "doc-url": "https://wot-design-uni.cn/component/collapse.html#collapse-attributes", "type": ["number"], "default": "2"}], "js": {"events": [{"name": "change", "description": "绑定值变化时触发", "doc-url": "https://wot-design-uni.cn/component/collapse.html#collapse-events"}]}}, {"name": "wd-config-provider", "source": {"symbol": "WdConfigProvider"}, "description": "用于全局配置 `Wot` 组件，提供深色模式、主题定制等能力。", "doc-url": "https://wot-design-uni.cn/component/config-provider.html#configprovider", "props": [{"name": "theme", "description": "主题风格，设置为 `dark` 来开启深色模式，全局生效", "doc-url": "https://wot-design-uni.cn/component/config-provider.html#attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "dark"}, {"name": "light"}]}, {"name": "theme-vars", "description": "自定义主题变量", "doc-url": "https://wot-design-uni.cn/component/config-provider.html#attributes", "type": [{"name": "ConfigProviderThemeVars", "source": {"symbol": "ConfigProviderThemeVars"}}]}]}, {"name": "wd-count-down", "source": {"symbol": "WdCountDown"}, "description": "用于实时展示倒计时数值，支持毫秒精度。", "doc-url": "https://wot-design-uni.cn/component/count-down.html#countdown", "props": [{"name": "time", "description": "倒计时时长，单位毫秒", "doc-url": "https://wot-design-uni.cn/component/count-down.html#attributes", "type": ["Number"], "default": "0"}, {"name": "millisecond", "description": "是否开启毫秒级渲染", "doc-url": "https://wot-design-uni.cn/component/count-down.html#attributes", "type": ["Boolean"], "default": "false"}, {"name": "auto-start", "description": "是否自动开始倒计时", "doc-url": "https://wot-design-uni.cn/component/count-down.html#attributes", "type": ["Boolean"], "default": "true"}, {"name": "format", "description": "倒计时格式化字符串", "doc-url": "https://wot-design-uni.cn/component/count-down.html#attributes", "type": ["String"], "default": "HH:mm:ss"}], "slots": [{"name": "—", "description": "默认插槽", "doc-url": "https://wot-design-uni.cn/component/count-down.html#slots"}], "js": {"events": [{"name": "finish", "description": "倒计时结束时触发", "doc-url": "https://wot-design-uni.cn/component/count-down.html#events"}, {"name": "change", "description": "倒计时变化时触发", "doc-url": "https://wot-design-uni.cn/component/count-down.html#events"}]}}, {"name": "wd-count-to", "source": {"symbol": "WdCountTo"}, "description": "数字滚动组件。", "doc-url": "https://wot-design-uni.cn/component/count-to.html#countto", "props": [{"name": "font-size", "description": "字体大小", "doc-url": "https://wot-design-uni.cn/component/count-to.html#attributes", "type": ["number"], "default": "default", "attribute-value": {"type": "of-match"}, "values": [{"name": "16"}]}, {"name": "color", "description": "文本颜色", "doc-url": "https://wot-design-uni.cn/component/count-to.html#attributes", "type": ["string"]}, {"name": "type", "description": "主题类型", "doc-url": "https://wot-design-uni.cn/component/count-to.html#attributes", "type": ["string"], "default": "default", "attribute-value": {"type": "enum"}, "values": [{"name": "primary'"}, {"name": "'error'"}, {"name": "'warning'"}, {"name": "'success"}]}, {"name": "start-val", "description": "起始值", "doc-url": "https://wot-design-uni.cn/component/count-to.html#attributes", "type": ["number"], "default": "0"}, {"name": "end-val", "description": "最终值", "doc-url": "https://wot-design-uni.cn/component/count-to.html#attributes", "type": ["number"], "default": "2024"}, {"name": "duration", "description": "从起始值到结束值数字变动的时间", "doc-url": "https://wot-design-uni.cn/component/count-to.html#attributes", "type": ["number"], "default": "3000"}, {"name": "autoplay", "description": "是否自动播放", "doc-url": "https://wot-design-uni.cn/component/count-to.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "decimals", "description": "保留的小数位数", "doc-url": "https://wot-design-uni.cn/component/count-to.html#attributes", "type": ["number"], "default": "0", "attribute-value": {"type": "of-match"}, "values": [{"name": "(需大于等于 0)"}]}, {"name": "decimal", "description": "小数点符号", "doc-url": "https://wot-design-uni.cn/component/count-to.html#attributes", "type": ["string"], "default": "."}, {"name": "separator", "description": "三位三位的隔开效果", "doc-url": "https://wot-design-uni.cn/component/count-to.html#attributes", "type": ["string"], "default": ","}, {"name": "prefix", "description": "前缀", "doc-url": "https://wot-design-uni.cn/component/count-to.html#attributes", "type": ["string"]}, {"name": "suffix", "description": "后缀", "doc-url": "https://wot-design-uni.cn/component/count-to.html#attributes", "type": ["string"]}, {"name": "use-easing", "description": "是否具有连贯性", "doc-url": "https://wot-design-uni.cn/component/count-to.html#attributes", "type": ["boolean"], "default": "true"}], "slots": [{"name": "default", "description": "默认插槽", "doc-url": "https://wot-design-uni.cn/component/count-to.html#slots"}, {"name": "prefix", "description": "前缀插槽", "doc-url": "https://wot-design-uni.cn/component/count-to.html#slots"}, {"name": "suffix", "description": "后缀插槽", "doc-url": "https://wot-design-uni.cn/component/count-to.html#slots"}], "js": {"events": [{"name": "finish", "description": "动画完成时触发", "doc-url": "https://wot-design-uni.cn/component/count-to.html#events"}, {"name": "mounted", "description": "组件加载完成时时触发", "doc-url": "https://wot-design-uni.cn/component/count-to.html#events"}]}}, {"name": "wd-curtain", "source": {"symbol": "Wd<PERSON>urtain"}, "description": "一般用于公告类的图片弹窗。", "doc-url": "https://wot-design-uni.cn/component/curtain.html#curtain", "props": [{"name": "value", "description": "绑定值，展示/关闭幕帘（已废弃，请使用 modelValue）", "doc-url": "https://wot-design-uni.cn/component/curtain.html#attributes", "type": ["boolean"]}, {"name": "model-value", "description": "绑定值，展示/关闭幕帘", "doc-url": "https://wot-design-uni.cn/component/curtain.html#attributes", "type": ["boolean"]}, {"name": "src", "description": "幕帘图片地址，必须使用网络地址", "doc-url": "https://wot-design-uni.cn/component/curtain.html#attributes", "type": ["string"]}, {"name": "width", "description": "幕帘图片宽度，默认单位 px", "doc-url": "https://wot-design-uni.cn/component/curtain.html#attributes", "type": ["number"]}, {"name": "to", "description": "幕帘图片点击链接", "doc-url": "https://wot-design-uni.cn/component/curtain.html#attributes", "type": ["string"]}, {"name": "close-position", "description": "关闭按钮位置", "doc-url": "https://wot-design-uni.cn/component/curtain.html#attributes", "type": ["string"], "default": "inset", "attribute-value": {"type": "enum"}, "values": [{"name": "inset"}, {"name": "top"}, {"name": "bottom"}, {"name": "top-left"}, {"name": "top-right"}, {"name": "bottom-left"}, {"name": "bottom-right"}]}, {"name": "close-on-click-modal", "description": "点击遮罩是否关闭", "doc-url": "https://wot-design-uni.cn/component/curtain.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "hide-when-close", "description": "是否当关闭时将弹出层隐藏（display: none）", "doc-url": "https://wot-design-uni.cn/component/curtain.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "z-index", "description": "设置层级", "doc-url": "https://wot-design-uni.cn/component/curtain.html#attributes", "type": ["number"], "default": "10"}, {"name": "root-portal", "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题", "doc-url": "https://wot-design-uni.cn/component/curtain.html#attributes", "type": ["boolean"], "default": "false"}], "js": {"events": [{"name": "click", "description": "点击幕帘时触发", "doc-url": "https://wot-design-uni.cn/component/curtain.html#events"}, {"name": "close", "description": "弹出层关闭时触发", "doc-url": "https://wot-design-uni.cn/component/curtain.html#events"}, {"name": "click-modal", "description": "点击遮罩时触发", "doc-url": "https://wot-design-uni.cn/component/curtain.html#events"}, {"name": "beforeenter", "description": "进入前触发", "doc-url": "https://wot-design-uni.cn/component/curtain.html#events"}, {"name": "enter", "description": "进入时触发", "doc-url": "https://wot-design-uni.cn/component/curtain.html#events"}, {"name": "afterenter", "description": "进入后触发", "doc-url": "https://wot-design-uni.cn/component/curtain.html#events"}, {"name": "beforeleave", "description": "离开前触发", "doc-url": "https://wot-design-uni.cn/component/curtain.html#events"}, {"name": "leave", "description": "离开时触发", "doc-url": "https://wot-design-uni.cn/component/curtain.html#events"}, {"name": "afterleave", "description": "离开后触发", "doc-url": "https://wot-design-uni.cn/component/curtain.html#events"}, {"name": "load", "description": "图片加载完成事件", "doc-url": "https://wot-design-uni.cn/component/curtain.html#events"}, {"name": "error", "description": "图片加载失败事件，若图片加载失败，则不会展示幕帘组件，即使设置 `value` 为 true", "doc-url": "https://wot-design-uni.cn/component/curtain.html#events"}]}}, {"name": "wd-datetime-picker-view", "source": {"symbol": "WdDatetimePickerView"}, "description": "为 Picker 组件的封装，在其内部构建好日期时间选项。", "doc-url": "https://wot-design-uni.cn/component/datetime-picker-view.html#datetimepickerview", "props": [{"name": "v-model", "description": "选中项，当 type 为 time 时，类型为字符串，否则为 `timestamp`", "doc-url": "https://wot-design-uni.cn/component/datetime-picker-view.html#attributes", "type": ["string", {"name": "timestamp", "source": {"symbol": "timestamp"}}]}, {"name": "type", "description": "选择器类型", "doc-url": "https://wot-design-uni.cn/component/datetime-picker-view.html#attributes", "type": ["string"], "default": "datetime", "attribute-value": {"type": "enum"}, "values": [{"name": "date"}, {"name": "year-month"}, {"name": "time"}, {"name": "year"}]}, {"name": "loading", "description": "加载中", "doc-url": "https://wot-design-uni.cn/component/datetime-picker-view.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "loading-color", "description": "加载的颜色，只能使用十六进制的色值写法，且不能使用缩写", "doc-url": "https://wot-design-uni.cn/component/datetime-picker-view.html#attributes", "type": ["string"], "default": "#4D80F0"}, {"name": "columns-height", "description": "picker内部滚筒高", "doc-url": "https://wot-design-uni.cn/component/datetime-picker-view.html#attributes", "type": ["number"], "default": "231"}, {"name": "formatter", "description": "自定义弹出层选项文案的格式化函数，返回一个字符串", "doc-url": "https://wot-design-uni.cn/component/datetime-picker-view.html#attributes", "type": ["function"]}, {"name": "filter", "description": "自定义过滤选项的函数，返回列的选项数组", "doc-url": "https://wot-design-uni.cn/component/datetime-picker-view.html#attributes", "type": ["function"]}, {"name": "min-date", "description": "最小日期，13 位的时间戳", "doc-url": "https://wot-design-uni.cn/component/datetime-picker-view.html#attributes", "type": [{"name": "timestamp", "source": {"symbol": "timestamp"}}], "default": "当前日期 - 10年"}, {"name": "max-date", "description": "最大日期，13 位的时间戳", "doc-url": "https://wot-design-uni.cn/component/datetime-picker-view.html#attributes", "type": [{"name": "timestamp", "source": {"symbol": "timestamp"}}], "default": "当前日期 + 10年"}, {"name": "min-hour", "description": "最小小时，time类型时生效", "doc-url": "https://wot-design-uni.cn/component/datetime-picker-view.html#attributes", "type": ["number"], "default": "0"}, {"name": "max-hour", "description": "最大小时，time类型时生效", "doc-url": "https://wot-design-uni.cn/component/datetime-picker-view.html#attributes", "type": ["number"], "default": "23"}, {"name": "min-minute", "description": "最小分钟，time类型时生效", "doc-url": "https://wot-design-uni.cn/component/datetime-picker-view.html#attributes", "type": ["number"], "default": "0"}, {"name": "max-minute", "description": "最大分钟，time类型时生效", "doc-url": "https://wot-design-uni.cn/component/datetime-picker-view.html#attributes", "type": ["number"], "default": "59"}, {"name": "immediate-change", "description": "是否在手指松开时立即触发picker-view的 change 事件。若不开启则会在滚动动画结束后触发 change 事件，1.2.25版本起提供，仅微信小程序和支付宝小程序支持。", "doc-url": "https://wot-design-uni.cn/component/datetime-picker-view.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "use-second", "description": "是否显示秒选择，仅在 time 和 datetime 类型下生效", "doc-url": "https://wot-design-uni.cn/component/datetime-picker-view.html#attributes", "type": ["boolean"], "default": "false"}], "js": {"events": [{"name": "change", "description": "切换选项时触发", "doc-url": "https://wot-design-uni.cn/component/datetime-picker-view.html#events"}, {"name": "pickstart", "description": "当滚动选择开始时候触发事件", "doc-url": "https://wot-design-uni.cn/component/datetime-picker-view.html#events"}, {"name": "pickend", "description": "当滚动选择结束时候触发事件", "doc-url": "https://wot-design-uni.cn/component/datetime-picker-view.html#events"}]}}, {"name": "wd-datetime-picker", "source": {"symbol": "WdDatetimePicker"}, "description": "为 DatetimePickerView 组件的封装，在其内部构建好日期时间选项。", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#datetimepicker", "props": [{"name": "v-model", "description": "选中项，当 type 为 time 时，类型为字符串；当 type 为 Array 时，类型为范围选择；否则为 `timestamp`", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["string", {"name": "timestamp", "source": {"symbol": "timestamp"}}, "array"]}, {"name": "default-value", "description": "默认日期，类型保持与 value 一致，打开面板时面板自动选到默认日期", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["string", {"name": "timestamp", "source": {"symbol": "timestamp"}}, "array"]}, {"name": "type", "description": "选择器类型", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["string"], "default": "datetime", "attribute-value": {"type": "enum"}, "values": [{"name": "date"}, {"name": "year-month"}, {"name": "time"}, {"name": "year"}]}, {"name": "loading", "description": "加载中", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "loading-color", "description": "加载的颜色，只能使用十六进制的色值写法，且不能使用缩写", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["string"], "default": "#4D80F0"}, {"name": "columns-height", "description": "picker内部滚筒高", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["number"], "default": "231"}, {"name": "title", "description": "弹出层标题", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["string"]}, {"name": "cancel-button-text", "description": "取消按钮文案", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["string"], "default": "取消"}, {"name": "confirm-button-text", "description": "确认按钮文案", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["string"], "default": "完成"}, {"name": "label", "description": "选择器左侧文案，label可以不传", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["string"]}, {"name": "placeholder", "description": "选择器占位符", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["string"], "default": "请选择"}, {"name": "disabled", "description": "禁用", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "readonly", "description": "只读", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "display-format", "description": "自定义展示文案的格式化函数，返回一个字符串", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["function"]}, {"name": "formatter", "description": "自定义弹出层选项文案的格式化函数，返回一个字符串", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["function"]}, {"name": "filter", "description": "自定义过滤选项的函数，返回列的选项数组", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["function"]}, {"name": "display-format-tab-label", "description": "在区域选择模式下，自定义展示tab标签文案的格式化函数，返回一个字符串", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["function"]}, {"name": "min-date", "description": "最小日期，13 位的时间戳", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": [{"name": "timestamp", "source": {"symbol": "timestamp"}}], "default": "当前日期 - 10年"}, {"name": "max-date", "description": "最大日期，13 位的时间戳", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": [{"name": "timestamp", "source": {"symbol": "timestamp"}}], "default": "当前日期 + 10年"}, {"name": "min-hour", "description": "最小小时，time类型时生效", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["number"], "default": "0"}, {"name": "max-hour", "description": "最大小时，time类型时生效", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["number"], "default": "23"}, {"name": "min-minute", "description": "最小分钟，time类型时生效", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["number"], "default": "0"}, {"name": "max-minute", "description": "最大分钟，time类型时生效", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["number"], "default": "59"}, {"name": "required", "description": "表单属性，必填", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "size", "description": "设置选择器大小", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "large"}]}, {"name": "label-width", "description": "设置左侧标题宽度", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["string"], "default": "33%"}, {"name": "error", "description": "是否为错误状态，错误状态时右侧内容为红色", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "align-right", "description": "选择器的值靠右展示", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "suse-label-slots", "description": "<s>label 使用插槽</s>，已废弃，直接使用label插槽即可", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "suse-default-slots", "description": "<s>使用默认插槽</s>，已废弃，直接使用默认插槽即可", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "before-confirm", "description": "确定前校验函数，接收 (value, resolve, picker) 参数，通过 resolve 继续执行 picker，resolve 接收1个boolean参数", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["function"]}, {"name": "close-on-click-modal", "description": "点击遮罩是否关闭", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "z-index", "description": "弹窗层级", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["number"], "default": "15"}, {"name": "safe-area-inset-bottom", "description": "弹出面板是否设置底部安全距离（iphone X 类型的机型）", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "ellipsis", "description": "是否超出隐藏", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "prop", "description": "表单域 `model` 字段名，在使用表单校验功能的情况下，该属性是必填的", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["string"]}, {"name": "rules", "description": "表单验证规则，结合`wd-form`组件使用", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": [{"name": "FormItemRule []", "source": {"symbol": "FormItemRule "}}], "default": "[]"}, {"name": "immediate-change", "description": "是否在手指松开时立即触发picker-view的 change 事件。若不开启则会在滚动动画结束后触发 change 事件，1.2.25版本起提供，仅微信小程序和支付宝小程序支持。", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "use-second", "description": "是否显示秒选择，仅在 time 和 datetime 类型下生效", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "clearable", "description": "显示清空按钮", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "root-portal", "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#attributes", "type": ["boolean"], "default": "false"}], "js": {"events": [{"name": "confirm", "description": "点击右侧按钮触发", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#events"}, {"name": "cancel", "description": "点击左侧按钮触发", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#events"}, {"name": "toggle", "description": "在区域选择模式下，tab标签切换时触发", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#events"}, {"name": "clear", "description": "点击清空按钮触发", "doc-url": "https://wot-design-uni.cn/component/datetime-picker.html#events"}]}}, {"name": "wd-divider", "source": {"symbol": "WdDivider"}, "description": "用于将内容分隔为多个区域。", "doc-url": "https://wot-design-uni.cn/component/divider.html#divider", "props": [{"name": "color", "description": "自定义颜色，支持所有颜色的写法", "doc-url": "https://wot-design-uni.cn/component/divider.html#attributes", "type": ["string"]}, {"name": "hairline", "description": "是否显示边框", "doc-url": "https://wot-design-uni.cn/component/divider.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "dashed", "description": "是否显示为虚线", "doc-url": "https://wot-design-uni.cn/component/divider.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "content-position", "description": "内容位置", "doc-url": "https://wot-design-uni.cn/component/divider.html#attributes", "type": ["string"], "default": "center", "attribute-value": {"type": "enum"}, "values": [{"name": "left"}, {"name": "center"}, {"name": "right"}]}, {"name": "vertical", "description": "是否显示为垂直分割线", "doc-url": "https://wot-design-uni.cn/component/divider.html#attributes", "type": ["boolean"], "default": "false"}]}, {"name": "wd-drop-menu", "source": {"symbol": "WdDropMenu"}, "description": "向下或向上弹出的菜单列表。", "doc-url": "https://wot-design-uni.cn/component/drop-menu.html#dropmenu", "props": [{"name": "direction", "description": "菜单展开方向，可选值为`up` 或 `down`", "doc-url": "https://wot-design-uni.cn/component/drop-menu.html#dropmenu-attributes", "type": ["string"], "default": "down", "attribute-value": {"type": "enum"}, "values": [{"name": "up"}, {"name": "down"}]}, {"name": "modal", "description": "是否展示蒙层", "doc-url": "https://wot-design-uni.cn/component/drop-menu.html#dropmenu-attributes", "type": ["boolean"], "default": "true"}, {"name": "close-on-click-modal", "description": "是否点击蒙层时关闭", "doc-url": "https://wot-design-uni.cn/component/drop-menu.html#dropmenu-attributes", "type": ["boolean"], "default": "true"}, {"name": "duration", "description": "菜单展开收起动画时间，单位 ms", "doc-url": "https://wot-design-uni.cn/component/drop-menu.html#dropmenu-attributes", "type": ["number"], "default": "200"}]}, {"name": "wd-drop-menu-item", "source": {"symbol": "WdDropMenuItem"}, "description": "向下或向上弹出的菜单列表。", "doc-url": "https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem", "props": [{"name": "v-model", "description": "当前选中项对应选中的 value", "doc-url": "https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes", "type": ["string", "number"]}, {"name": "disabled", "description": "禁用菜单", "doc-url": "https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes", "type": ["boolean"], "default": "false"}, {"name": "options", "description": "列表数据，对应数据结构 `[{text: '标题', value: '0', tip: '提示文字'}]`", "doc-url": "https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes", "type": ["array"]}, {"name": "icon-name", "description": "选中的图标名称(可选名称在 wd-icon 组件中)", "doc-url": "https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes", "type": ["string"], "default": "check"}, {"name": "title", "description": "菜单标题", "doc-url": "https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes", "type": ["string"]}, {"name": "icon", "description": "菜单图标", "doc-url": "https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes", "type": ["string"], "default": "arrow-down"}, {"name": "icon-size", "description": "菜单图标尺寸", "doc-url": "https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes", "type": ["string"], "default": "14px"}, {"name": "before-toggle", "description": "下拉菜单打开或者关闭前触发，`reslove(true)`时继续执行打开或关闭操作", "doc-url": "https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes", "type": ["function({ status, resolve })"]}, {"name": "value-key", "description": "选项对象中，value 对应的 key", "doc-url": "https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes", "type": ["string"], "default": "value"}, {"name": "label-key", "description": "选项对象中，展示的文本对应的 key", "doc-url": "https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes", "type": ["string"], "default": "label"}, {"name": "tip-key", "description": "选项对象中，选项说明对应的 key", "doc-url": "https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes", "type": ["string"], "default": "tip"}, {"name": "root-portal", "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题", "doc-url": "https://wot-design-uni.cn/component/drop-menu.html#dropmenuitem-attributes", "type": ["boolean"], "default": "false"}]}, {"name": "wd-dropdown-item", "source": {"symbol": "WdDropdownItem"}, "description": "向下或向上弹出的菜单列表。", "doc-url": "https://wot-design-uni.cn/component/drop-menu.html#dropdownitem"}, {"name": "wd-fab", "source": {"symbol": "WdFab"}, "description": "悬浮动作按钮组件，按下可显示一组动作按钮。", "doc-url": "https://wot-design-uni.cn/component/fab.html#fab", "props": [{"name": "v-model:active", "description": "是否激活", "doc-url": "https://wot-design-uni.cn/component/fab.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "type", "description": "类型", "doc-url": "https://wot-design-uni.cn/component/fab.html#attributes", "type": [{"name": "FabType", "source": {"symbol": "FabType"}}], "default": "primary", "attribute-value": {"type": "of-match"}, "values": [{"name": "primary' &#124; 'success' &#124; 'info' &#124; 'warning' &#124; 'error' &#124; 'default"}]}, {"name": "position", "description": "悬浮按钮位置", "doc-url": "https://wot-design-uni.cn/component/fab.html#attributes", "type": [{"name": "FabPosition", "source": {"symbol": "FabPosition"}}], "default": "right-bottom", "attribute-value": {"type": "of-match"}, "values": [{"name": "left-top' &#124; 'right-top' &#124; 'left-bottom' &#124; 'right-bottom' &#124; left-center &#124; right-center &#124; top-center &#124; bottom-center"}]}, {"name": "draggable", "description": "按钮能否拖动", "doc-url": "https://wot-design-uni.cn/component/fab.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "direction", "description": "悬浮按钮菜单弹出方向", "doc-url": "https://wot-design-uni.cn/component/fab.html#attributes", "type": [{"name": "FabDirection", "source": {"symbol": "FabDirection"}}], "default": "top", "attribute-value": {"type": "of-match"}, "values": [{"name": "top' &#124; 'right' &#124; 'bottom' &#124; 'left"}]}, {"name": "disabled", "description": "是否禁用", "doc-url": "https://wot-design-uni.cn/component/fab.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "inactive-icon", "description": "悬浮按钮未展开时的图标", "doc-url": "https://wot-design-uni.cn/component/fab.html#attributes", "type": ["string"], "default": "add"}, {"name": "active-icon", "description": "悬浮按钮展开时的图标", "doc-url": "https://wot-design-uni.cn/component/fab.html#attributes", "type": ["string"], "default": "close"}, {"name": "z-index", "description": "自定义悬浮按钮层级", "doc-url": "https://wot-design-uni.cn/component/fab.html#attributes", "type": ["number"], "default": "99"}, {"name": "gap", "description": "自定义悬浮按钮与可视区域边缘的间距", "doc-url": "https://wot-design-uni.cn/component/fab.html#attributes", "type": [{"name": "FabGap", "source": {"symbol": "FabGap"}}], "default": "\\{ top: 16, left: 16, right: 16, bottom: 16 \\}"}, {"name": "custom-style", "description": "自定义样式", "doc-url": "https://wot-design-uni.cn/component/fab.html#attributes", "type": ["string"]}, {"name": "expandable", "description": "用于控制点击时是否展开菜单，设置为 false 时触发 click", "doc-url": "https://wot-design-uni.cn/component/fab.html#attributes", "type": ["boolean"], "default": "true"}], "js": {"events": [{"name": "click", "description": "expandable 设置为 false 时，点击悬浮按钮触发", "doc-url": "https://wot-design-uni.cn/component/fab.html#events"}]}}, {"name": "wd-floating-panel", "source": {"symbol": "WdFloatingPanel"}, "description": "浮动在页面底部的面板，用户可以通过上下拖动秒板来浏览内容，从而在不离开当前视图的情况下访问更多信息，常用于地图导航。", "doc-url": "https://wot-design-uni.cn/component/floating-panel.html#floatingpanel", "props": [{"name": "v-model:height", "description": "当前面板的显示高度", "doc-url": "https://wot-design-uni.cn/component/floating-panel.html#attributes", "type": ["number"], "default": "0"}, {"name": "anchors", "description": "设置自定义锚点, 单位 `px`", "doc-url": "https://wot-design-uni.cn/component/floating-panel.html#attributes", "type": ["number[]"], "default": "[100, windowHeight  0.6]"}, {"name": "duration", "description": "动画时长，单位`ms`，设置为 `0` 可以禁用动画", "doc-url": "https://wot-design-uni.cn/component/floating-panel.html#attributes", "type": ["number"], "default": "300"}, {"name": "content-draggable", "description": "允许拖拽内容容器", "doc-url": "https://wot-design-uni.cn/component/floating-panel.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "safe-area-inset-bottom", "description": "是否开启底部安全区适配", "doc-url": "https://wot-design-uni.cn/component/floating-panel.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "show-scrollbar", "description": "是否开启滚动条", "doc-url": "https://wot-design-uni.cn/component/floating-panel.html#attributes", "type": ["boolean"], "default": "true"}], "slots": [{"name": "—", "description": "默认插槽", "doc-url": "https://wot-design-uni.cn/component/floating-panel.html#slots"}]}, {"name": "wd-form", "source": {"symbol": "WdForm"}, "description": "用于数据录入、校验，支持输入框、单选框、复选框、文件上传等类型，常见的 form 表单为`单元格`形式的展示，即左侧为表单的标题描述，右侧为表单的输入。", "doc-url": "https://wot-design-uni.cn/component/form.html#form", "props": [{"name": "model", "description": "表单数据对象", "doc-url": "https://wot-design-uni.cn/component/form.html#attributes", "type": ["Record<string, any>"]}, {"name": "rules", "description": "表单验证规则", "doc-url": "https://wot-design-uni.cn/component/form.html#attributes", "type": [{"name": "FormRules", "source": {"symbol": "FormRules"}}]}, {"name": "reset-on-change", "description": "表单数据变化时是否重置表单提示信息（设置为 false 时需要开发者单独对变更项进行校验）", "doc-url": "https://wot-design-uni.cn/component/form.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "error-type", "description": "校验错误提示方式", "doc-url": "https://wot-design-uni.cn/component/form.html#attributes", "type": [{"name": "toast", "source": {"symbol": "toast"}}, {"name": "message", "source": {"symbol": "message"}}, {"name": "none", "source": {"symbol": "none"}}], "default": "message"}], "js": {"events": [{"name": "validate", "description": "验证表单，支持传入一个 prop 来验证单个表单项，不传入 prop 时，会验证所有表单项，1.6.0 版本起支持传入数组", "doc-url": "https://wot-design-uni.cn/component/form.html#events"}, {"name": "reset", "description": "重置校验结果", "doc-url": "https://wot-design-uni.cn/component/form.html#events"}]}}, {"name": "wd-gap", "source": {"symbol": "WdGap"}, "description": "一般用于页面布局时代替margin或者padding;或者充当（底部）占位元素。", "doc-url": "https://wot-design-uni.cn/component/gap.html#gap", "props": [{"name": "height", "description": "高度", "doc-url": "https://wot-design-uni.cn/component/gap.html#attributes", "type": ["string", "number"], "default": "15"}, {"name": "bg-color", "description": "背景颜色", "doc-url": "https://wot-design-uni.cn/component/gap.html#attributes", "type": ["string"], "default": "transparent"}, {"name": "safe-area-bottom", "description": "开启底部安全区", "doc-url": "https://wot-design-uni.cn/component/gap.html#attributes", "type": ["boolean"], "default": "false", "attribute-value": {"type": "of-match"}, "values": [{"name": "true"}, {"name": "false"}]}]}, {"name": "wd-grid", "source": {"symbol": "WdGrid"}, "description": "宫格可以在水平方向上把页面分隔成等宽度的区块，用于展示内容或进行页面导航。", "doc-url": "https://wot-design-uni.cn/component/grid.html#grid", "props": [{"name": "column", "description": "列数", "doc-url": "https://wot-design-uni.cn/component/grid.html#grid-attributes", "type": ["number"]}, {"name": "border", "description": "是否显示边框", "doc-url": "https://wot-design-uni.cn/component/grid.html#grid-attributes", "type": ["boolean"], "default": "false"}, {"name": "gutter", "description": "格子之间的间距，默认单位为`px`", "doc-url": "https://wot-design-uni.cn/component/grid.html#grid-attributes", "type": ["number"]}, {"name": "square", "description": "是否将格子固定为正方形", "doc-url": "https://wot-design-uni.cn/component/grid.html#grid-attributes", "type": ["boolean"], "default": "false"}, {"name": "clickable", "description": "是否开启格子点击反馈", "doc-url": "https://wot-design-uni.cn/component/grid.html#grid-attributes", "type": ["boolean"], "default": "false"}, {"name": "bg-color", "description": "背景颜色设置", "doc-url": "https://wot-design-uni.cn/component/grid.html#grid-attributes", "type": ["string"], "default": "#ffffff"}, {"name": "hover-class", "description": "指定grid-item按下去的样式类", "doc-url": "https://wot-design-uni.cn/component/grid.html#grid-attributes", "type": ["string"], "default": "wd-grid-item__content--hover"}]}, {"name": "wd-grid-item", "source": {"symbol": "WdGridItem"}, "description": "宫格可以在水平方向上把页面分隔成等宽度的区块，用于展示内容或进行页面导航。", "doc-url": "https://wot-design-uni.cn/component/grid.html#griditem", "props": [{"name": "text", "description": "文字 value", "doc-url": "https://wot-design-uni.cn/component/grid.html#griditem-attributes", "type": ["string"]}, {"name": "icon", "description": "图标名称，可选值见 `wd-icon` 组件", "doc-url": "https://wot-design-uni.cn/component/grid.html#griditem-attributes", "type": ["string"]}, {"name": "is-dot", "description": "是否显示图标右上角小红点", "doc-url": "https://wot-design-uni.cn/component/grid.html#griditem-attributes", "type": ["boolean"], "default": "false"}, {"name": "type", "description": "图标右上角显示的 `badge` 类型", "doc-url": "https://wot-design-uni.cn/component/grid.html#griditem-attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "primary"}, {"name": "success"}, {"name": "warning"}, {"name": "danger"}, {"name": "info"}]}, {"name": "value", "description": "图标右上角 `badge` 显示值", "doc-url": "https://wot-design-uni.cn/component/grid.html#griditem-attributes", "type": [{"name": "string, number", "source": {"symbol": "string, number"}}]}, {"name": "max", "description": "图标右上角 `badge` 最大值，超过最大值会显示 '{max}+'，要求 value 是 Number 类型", "doc-url": "https://wot-design-uni.cn/component/grid.html#griditem-attributes", "type": ["number"]}, {"name": "url", "description": "点击后跳转的链接地址", "doc-url": "https://wot-design-uni.cn/component/grid.html#griditem-attributes", "type": ["string"]}, {"name": "link-type", "description": "页面跳转方式, 参考[微信小程序路由文档](https://developers.weixin.qq.com/miniprogram/dev/framework/app-service/route.html)", "doc-url": "https://wot-design-uni.cn/component/grid.html#griditem-attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "navigateTo"}, {"name": "switchTab"}, {"name": "reLaunch"}]}, {"name": "use-slot", "description": "是否开启 `GridItem` 内容插槽", "doc-url": "https://wot-design-uni.cn/component/grid.html#griditem-attributes", "type": ["boolean"], "default": "false"}, {"name": "use-icon-slot", "description": "是否开启 `GridItem` icon 插槽", "doc-url": "https://wot-design-uni.cn/component/grid.html#griditem-attributes", "type": ["boolean"], "default": "false"}, {"name": "use-text-slot", "description": "是否开启 `GridItem` text 内容插槽", "doc-url": "https://wot-design-uni.cn/component/grid.html#griditem-attributes", "type": ["boolean"], "default": "false"}, {"name": "icon-size", "description": "图标大小", "doc-url": "https://wot-design-uni.cn/component/grid.html#griditem-attributes", "type": ["string"], "default": "26px"}, {"name": "badge-props", "description": "自定义徽标的属性，传入的对象会被透传给 [Badge 组件的 props](/component/badge#attributes)", "doc-url": "https://wot-design-uni.cn/component/grid.html#griditem-attributes", "type": [{"name": "BadgeProps", "source": {"symbol": "BadgeProps"}}]}]}, {"name": "wd-icon", "source": {"symbol": "WdIcon"}, "description": "基于字体的图标集。", "doc-url": "https://wot-design-uni.cn/component/icon.html#icon", "props": [{"name": "name", "description": "使用的图标名字，可以使用链接图片", "doc-url": "https://wot-design-uni.cn/component/icon.html#attributes", "type": ["string"]}, {"name": "color", "description": "图标的颜色", "doc-url": "https://wot-design-uni.cn/component/icon.html#attributes", "type": ["string"], "default": "inherit"}, {"name": "size", "description": "图标的字体大小", "doc-url": "https://wot-design-uni.cn/component/icon.html#attributes", "type": ["string"], "default": "inherit"}, {"name": "class-prefix", "description": "类名前缀，用于使用自定义图标", "doc-url": "https://wot-design-uni.cn/component/icon.html#attributes", "type": ["string"], "default": "wd-icon"}, {"name": "custom-style", "description": "根节点样式", "doc-url": "https://wot-design-uni.cn/component/icon.html#attributes", "type": ["string"]}]}, {"name": "wd-img-cropper", "source": {"symbol": "WdImgCropper"}, "description": "图片剪裁组件，用于图片裁剪，支持拖拽、缩放、旋转等操作。", "doc-url": "https://wot-design-uni.cn/component/img-cropper.html#imgcropper", "props": [{"name": "v-model", "description": "打开图片裁剪组件", "doc-url": "https://wot-design-uni.cn/component/img-cropper.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "img-src", "description": "图片资源链接", "doc-url": "https://wot-design-uni.cn/component/img-cropper.html#attributes", "type": ["string"]}, {"name": "img-width", "description": "截屏预览图片的初始宽度; `1、设置宽度不设置高度，按照宽度等比缩放；2、如果都不设置，预览时图片大小会根据裁剪框大小进行等比缩放，进行锁边处理；`; string 类型只支持 % 单位，number 类型时单位为 px", "doc-url": "https://wot-design-uni.cn/component/img-cropper.html#attributes", "type": ["number", "string"]}, {"name": "img-height", "description": "截屏预览图片的初始高度; `1、设置高度不设置宽度，按照高度等比缩放；2、如果都不设置，预览时图片大小会根据裁剪框大小进行等比缩放，进行锁边处理；`; string 类型只支持 % 单位，number 类型时单位为 px", "doc-url": "https://wot-design-uni.cn/component/img-cropper.html#attributes", "type": ["number", "string"]}, {"name": "disabled-rotate", "description": "禁止图片旋转", "doc-url": "https://wot-design-uni.cn/component/img-cropper.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "export-scale", "description": "设置导出图片尺寸", "doc-url": "https://wot-design-uni.cn/component/img-cropper.html#attributes", "type": ["number"], "default": "2"}, {"name": "max-scale", "description": "最大缩放倍数", "doc-url": "https://wot-design-uni.cn/component/img-cropper.html#attributes", "type": ["number"], "default": "3"}, {"name": "cancel-button-text", "description": "取消按钮文案", "doc-url": "https://wot-design-uni.cn/component/img-cropper.html#attributes", "type": ["string"], "default": "取消"}, {"name": "confirm-button-text", "description": "确认按钮文案", "doc-url": "https://wot-design-uni.cn/component/img-cropper.html#attributes", "type": ["string"], "default": "完成"}, {"name": "quality", "description": "生成的图片质量 [wx.canvasToTempFilePath属性介绍](https://developers.weixin.qq.com/miniprogram/dev/api/canvas/wx.canvasToTempFilePath.html#%E5%8F%82%E6%95%B0)", "doc-url": "https://wot-design-uni.cn/component/img-cropper.html#attributes", "type": ["number"], "default": "1", "attribute-value": {"type": "of-match"}, "values": [{"name": "0"}, {"name": "1"}]}, {"name": "file-type", "description": "目标文件的类型，[wx.canvasToTempFilePath属性介绍](https://developers.weixin.qq.com/miniprogram/dev/api/canvas/wx.canvasToTempFilePath.html#%E5%8F%82%E6%95%B0)", "doc-url": "https://wot-design-uni.cn/component/img-cropper.html#attributes", "type": ["string"], "default": "png"}, {"name": "aspect-ratio", "description": "裁剪框宽高比，格式为 width:height", "doc-url": "https://wot-design-uni.cn/component/img-cropper.html#attributes", "type": ["string"], "default": "1:1"}], "js": {"events": [{"name": "confirm", "description": "完成截图时触发", "doc-url": "https://wot-design-uni.cn/component/img-cropper.html#events"}, {"name": "cancel", "description": "当取消截图时触发", "doc-url": "https://wot-design-uni.cn/component/img-cropper.html#events"}, {"name": "imgloaderror", "description": "当图片加载错误时触发", "doc-url": "https://wot-design-uni.cn/component/img-cropper.html#events"}, {"name": "imgloaded", "description": "当图片加载完成时触发", "doc-url": "https://wot-design-uni.cn/component/img-cropper.html#events"}]}}, {"name": "wd-img", "source": {"symbol": "WdImg"}, "description": "增强版的 img 标签，提供多种图片填充模式，支持图片懒加载、加载完成、加载失败。", "doc-url": "https://wot-design-uni.cn/component/img.html#img", "props": [{"name": "src", "description": "图片链接", "doc-url": "https://wot-design-uni.cn/component/img.html#attributes", "type": ["string"]}, {"name": "width", "description": "宽度，默认单位为 px", "doc-url": "https://wot-design-uni.cn/component/img.html#attributes", "type": ["number", "string"]}, {"name": "height", "description": "高度，默认单位为 px", "doc-url": "https://wot-design-uni.cn/component/img.html#attributes", "type": ["number", "string"]}, {"name": "mode", "description": "填充模式", "doc-url": "https://wot-design-uni.cn/component/img.html#attributes", "type": [{"name": "ImageMode", "source": {"symbol": "ImageMode"}}], "default": "scaleToFill", "attribute-value": {"type": "of-match"}, "values": [{"name": "top left'"}, {"name": "'top right'"}, {"name": "'bottom left'"}, {"name": "'bottom right'"}, {"name": "'right'"}, {"name": "'left'"}, {"name": "'center'"}, {"name": "'bottom'"}, {"name": "'top'"}, {"name": "'heightFix'"}, {"name": "'widthFix'"}, {"name": "'aspectFill'"}, {"name": "'aspectFit'"}, {"name": "'scaleToFill"}]}, {"name": "round", "description": "是否显示为圆形", "doc-url": "https://wot-design-uni.cn/component/img.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "radius", "description": "圆角大小，默认单位为 px", "doc-url": "https://wot-design-uni.cn/component/img.html#attributes", "type": ["number", "string"]}, {"name": "enable-preview", "description": "是否支持点击预览", "doc-url": "https://wot-design-uni.cn/component/img.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "show-menu-by-longpress", "description": "开启长按图片显示识别小程序码菜单，仅微信小程序支持", "doc-url": "https://wot-design-uni.cn/component/img.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "preview-src", "description": "预览图片链接", "doc-url": "https://wot-design-uni.cn/component/img.html#attributes", "type": ["string"]}], "slots": [{"name": "loading", "description": "图片加载时展示", "doc-url": "https://wot-design-uni.cn/component/img.html#slots"}, {"name": "error", "description": "图片加载失败后展示", "doc-url": "https://wot-design-uni.cn/component/img.html#slots"}], "js": {"events": [{"name": "click", "description": "点击事件", "doc-url": "https://wot-design-uni.cn/component/img.html#events"}, {"name": "load", "description": "当图片载入完毕时触发", "doc-url": "https://wot-design-uni.cn/component/img.html#events"}, {"name": "error", "description": "当错误发生时触发", "doc-url": "https://wot-design-uni.cn/component/img.html#events"}]}}, {"name": "wd-index-anchor", "source": {"symbol": "WdIndexAnchor"}, "description": "用于列表的索引分类显示和快速定位。", "doc-url": "https://wot-design-uni.cn/component/index-bar.html#indexanchor", "props": [{"name": "index", "description": "索引字符", "doc-url": "https://wot-design-uni.cn/component/index-bar.html#indexanchor-attributes", "type": ["string", "number"]}]}, {"name": "wd-index-bar", "source": {"symbol": "WdIndexBar"}, "description": "用于列表的索引分类显示和快速定位。", "doc-url": "https://wot-design-uni.cn/component/index-bar.html#indexbar", "props": [{"name": "sticky", "description": "索引是否吸顶", "doc-url": "https://wot-design-uni.cn/component/index-bar.html#attributes", "type": ["boolean"], "default": "false"}]}, {"name": "wd-input-number", "source": {"symbol": "WdInputNumber"}, "description": "由增加按钮、减少按钮和输入框组成，用于在一定范围内输入、调整数字。", "doc-url": "https://wot-design-uni.cn/component/input-number.html#inputnumber", "props": [{"name": "v-model", "description": "绑定值", "doc-url": "https://wot-design-uni.cn/component/input-number.html#attributes", "type": ["number", "string"]}, {"name": "min", "description": "最小值", "doc-url": "https://wot-design-uni.cn/component/input-number.html#attributes", "type": ["number"], "default": "1"}, {"name": "max", "description": "最大值", "doc-url": "https://wot-design-uni.cn/component/input-number.html#attributes", "type": ["number"], "default": "Infinity"}, {"name": "step", "description": "步数", "doc-url": "https://wot-design-uni.cn/component/input-number.html#attributes", "type": ["number"], "default": "1"}, {"name": "step-strictly", "description": "严格值为步数的倍数", "doc-url": "https://wot-design-uni.cn/component/input-number.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "precision", "description": "小数精度", "doc-url": "https://wot-design-uni.cn/component/input-number.html#attributes", "type": ["number"], "default": "0"}, {"name": "disabled", "description": "禁用", "doc-url": "https://wot-design-uni.cn/component/input-number.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "without-input", "description": "不显示输入框", "doc-url": "https://wot-design-uni.cn/component/input-number.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "input-width", "description": "输入框宽度", "doc-url": "https://wot-design-uni.cn/component/input-number.html#attributes", "type": ["string"], "default": "36px"}, {"name": "allow-null", "description": "是否允许输入的值为空，设置为 `true` 后允许传入空字符串", "doc-url": "https://wot-design-uni.cn/component/input-number.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "placeholder", "description": "占位文本", "doc-url": "https://wot-design-uni.cn/component/input-number.html#attributes", "type": ["string"]}, {"name": "disable-input", "description": "禁用输入框", "doc-url": "https://wot-design-uni.cn/component/input-number.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "disable-plus", "description": "禁用增加按钮", "doc-url": "https://wot-design-uni.cn/component/input-number.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "disable-minus", "description": "禁用减少按钮", "doc-url": "https://wot-design-uni.cn/component/input-number.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "adjust-position", "description": "原生属性，键盘弹起时，是否自动上推页面", "doc-url": "https://wot-design-uni.cn/component/input-number.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "before-change", "description": "输入框值改变前触发，返回 false 会阻止输入框值改变，支持返回 `Promise`", "doc-url": "https://wot-design-uni.cn/component/input-number.html#attributes", "type": ["(value: number | string) => boolean | Promise<boolean>"]}, {"name": "long-press", "description": "是否允许长按进行加减", "doc-url": "https://wot-design-uni.cn/component/input-number.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "immediate-change", "description": "是否立即响应输入变化，false 时仅在失焦和按钮点击时更新", "doc-url": "https://wot-design-uni.cn/component/input-number.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "update-on-init", "description": "是否在初始化时更新 v-model 为修正后的值", "doc-url": "https://wot-design-uni.cn/component/input-number.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "input-type", "description": "输入框类型", "doc-url": "https://wot-design-uni.cn/component/input-number.html#attributes", "type": ["string"], "default": "digit", "attribute-value": {"type": "enum"}, "values": [{"name": "number"}, {"name": "digit"}]}], "js": {"events": [{"name": "change", "description": "值修改事件", "doc-url": "https://wot-design-uni.cn/component/input-number.html#events"}, {"name": "focus", "description": "输入框获取焦点事件", "doc-url": "https://wot-design-uni.cn/component/input-number.html#events"}, {"name": "blur", "description": "输入框失去焦点事件", "doc-url": "https://wot-design-uni.cn/component/input-number.html#events"}]}}, {"name": "wd-input", "source": {"symbol": "WdInput"}, "description": "用户可以在文本框里输入内容。", "doc-url": "https://wot-design-uni.cn/component/input.html#input", "props": [{"name": "type", "description": "类型", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["string"], "default": "text", "attribute-value": {"type": "enum"}, "values": [{"name": "text"}, {"name": "number"}, {"name": "digit"}, {"name": "idcard"}, {"name": "safe-password"}, {"name": "nickname"}, {"name": "tel"}]}, {"name": "v-model", "description": "绑定值", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["string", "number"]}, {"name": "placeholder", "description": "占位文本", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["string"], "default": "请输入..."}, {"name": "clearable", "description": "显示清空按钮", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "maxlength", "description": "原生属性，最大长度", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["number"], "default": "支付宝小程序无默认值，其余平台默认为-1"}, {"name": "show-password", "description": "显示为密码框", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "disabled", "description": "原生属性，禁用", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "readonly", "description": "只读", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "prefix-icon", "description": "前置图标，icon组件中的图标类名", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["string"]}, {"name": "suffix-icon", "description": "后置图标，icon组件中的图标类名", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["string"]}, {"name": "show-word-limit", "description": "显示字数限制，需要同时设置 maxlength", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "confirm-type", "description": "设置键盘右下角按钮的文字，仅在type='text'时生效", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["string"], "default": "done", "attribute-value": {"type": "enum"}, "values": [{"name": "done"}, {"name": "go"}, {"name": "next"}, {"name": "search"}, {"name": "send"}]}, {"name": "confirm-hold", "description": "点击键盘右下角按钮时是否保持键盘不收起", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["Boolean"], "default": "false"}, {"name": "always-embed", "description": "微信小程序原生属性，强制 input 处于同层状态，默认 focus 时 input 会切到非同层状态 (仅在 iOS 下生效)", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["Boolean"], "default": "false"}, {"name": "placeholder-style", "description": "原生属性，指定 placeholder 的样式，目前仅支持color,font-size和font-weight", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["string"]}, {"name": "placeholder-class", "description": "原生属性，指定 placeholder 的样式类", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["string"]}, {"name": "focus", "description": "原生属性，获取焦点", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "cursor-spacing", "description": "原生属性，指定光标与键盘的距离。取 input 距离底部的距离和cursor-spacing指定的距离的最小值作为光标与键盘的距离", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["number"], "default": "0"}, {"name": "cursor", "description": "原生属性，指定focus时的光标位置", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["number"], "default": "-1"}, {"name": "selection-start", "description": "原生属性，光标起始位置，自动聚集时有效，需与selection-end搭配使用", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["number"], "default": "-1"}, {"name": "selection-end", "description": "原生属性，光标结束位置，自动聚集时有效，需与selection-start搭配使用", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["number"], "default": "-1"}, {"name": "adjust-position", "description": "原生属性，键盘弹起时，是否自动上推页面", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "label", "description": "设置左侧标题", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["string"]}, {"name": "size", "description": "设置输入框大小", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["string"]}, {"name": "error", "description": "设置输入框错误状态，错误状态时为红色", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "center", "description": "当有label属性时，设置标题和输入框垂直居中，默认为顶部居中", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "label-width", "description": "设置左侧标题宽度", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["string"], "default": "33%"}, {"name": "required", "description": "cell 类型下必填样式", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "no-border", "description": "非 cell 类型下是否隐藏下划线", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "prop", "description": "表单域 `model` 字段名，在使用表单校验功能的情况下，该属性是必填的", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["string"]}, {"name": "rules", "description": "表单验证规则，结合`wd-form`组件使用", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": [{"name": "FormItemRule []", "source": {"symbol": "FormItemRule "}}], "default": "[]"}, {"name": "clear-trigger", "description": "显示清除图标的时机，always 表示输入框不为空时展示，focus 表示输入框聚焦且不为空时展示", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": [{"name": "InputClearTrigger", "source": {"symbol": "InputClearTrigger"}}], "default": "always", "attribute-value": {"type": "of-match"}, "values": [{"name": "focus"}, {"name": "always"}]}, {"name": "focus-when-clear", "description": "是否在点击清除按钮时聚焦输入框", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "ignore-composition-event", "description": "是否忽略组件内对文本合成系统事件的处理。为 false 时将触发 compositionstart、compositionend、compositionupdate 事件，且在文本合成期间会触发 input 事件。", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "inputmode", "description": "提供用户在编辑元素或其内容时可能输入的数据类型的提示。", "doc-url": "https://wot-design-uni.cn/component/input.html#attributes", "type": [{"name": "InputMode", "source": {"symbol": "InputMode"}}], "default": "text"}], "js": {"events": [{"name": "input", "description": "监听输入框input事件", "doc-url": "https://wot-design-uni.cn/component/input.html#events"}, {"name": "focus", "description": "监听输入框focus事件", "doc-url": "https://wot-design-uni.cn/component/input.html#events"}, {"name": "blur", "description": "监听输入框blur事件", "doc-url": "https://wot-design-uni.cn/component/input.html#events"}, {"name": "clear", "description": "监听输入框清空按钮事件", "doc-url": "https://wot-design-uni.cn/component/input.html#events"}, {"name": "confirm", "description": "点击完成时， 触发 confirm 事件", "doc-url": "https://wot-design-uni.cn/component/input.html#events"}, {"name": "keyboardheightchange", "description": "键盘高度发生变化的时候触发此事件", "doc-url": "https://wot-design-uni.cn/component/input.html#events"}, {"name": "clickprefixicon", "description": "点击前置图标时触发", "doc-url": "https://wot-design-uni.cn/component/input.html#events"}, {"name": "clicksuffixicon", "description": "点击后置图标时触发", "doc-url": "https://wot-design-uni.cn/component/input.html#events"}]}}, {"name": "wd-keyboard", "source": {"symbol": "WdKeyboard"}, "description": "虚拟数字键盘，用于输入数字、密码、身份证或车牌号等场景。", "doc-url": "https://wot-design-uni.cn/component/keyboard.html#keyboard", "props": [{"name": "v-model:visible", "description": "是否展开", "doc-url": "https://wot-design-uni.cn/component/keyboard.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "v-model", "description": "绑定的值", "doc-url": "https://wot-design-uni.cn/component/keyboard.html#attributes", "type": ["string"]}, {"name": "title", "description": "标题", "doc-url": "https://wot-design-uni.cn/component/keyboard.html#attributes", "type": ["string"]}, {"name": "mode", "description": "键盘模式", "doc-url": "https://wot-design-uni.cn/component/keyboard.html#attributes", "type": ["string"], "default": "default", "attribute-value": {"type": "of-match"}, "values": [{"name": "default, car, custom"}]}, {"name": "z-index", "description": "层级", "doc-url": "https://wot-design-uni.cn/component/keyboard.html#attributes", "type": ["number"], "default": "100"}, {"name": "maxlength", "description": "最大长度", "doc-url": "https://wot-design-uni.cn/component/keyboard.html#attributes", "type": ["number"], "default": "Infinity"}, {"name": "show-delete-key", "description": "是否显示删除键", "doc-url": "https://wot-design-uni.cn/component/keyboard.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "random-key-order", "description": "是否随机键盘按键顺序", "doc-url": "https://wot-design-uni.cn/component/keyboard.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "close-text", "description": "确认按钮文本", "doc-url": "https://wot-design-uni.cn/component/keyboard.html#attributes", "type": ["string"]}, {"name": "delete-text", "description": "删除按钮文本", "doc-url": "https://wot-design-uni.cn/component/keyboard.html#attributes", "type": ["string"]}, {"name": "close-button-loading", "description": "关闭按钮是否显示加载状态", "doc-url": "https://wot-design-uni.cn/component/keyboard.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "modal", "description": "是否显示蒙层遮罩", "doc-url": "https://wot-design-uni.cn/component/keyboard.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "hide-on-click-outside", "description": "是否在点击外部时收起键盘", "doc-url": "https://wot-design-uni.cn/component/keyboard.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "lock-scroll", "description": "是否锁定背景滚动，锁定时蒙层里的内容也将无法滚动", "doc-url": "https://wot-design-uni.cn/component/keyboard.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "safe-area-inset-bottom", "description": "是否在底部安全区域内", "doc-url": "https://wot-design-uni.cn/component/keyboard.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "extra-key", "description": "额外按键", "doc-url": "https://wot-design-uni.cn/component/keyboard.html#attributes", "type": ["string", "string[]"]}, {"name": "root-portal", "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题", "doc-url": "https://wot-design-uni.cn/component/keyboard.html#attributes", "type": ["boolean"], "default": "false"}], "js": {"events": [{"name": "input", "description": "点击按键时触发", "doc-url": "https://wot-design-uni.cn/component/keyboard.html#events"}, {"name": "delete", "description": "点击删除键时触发", "doc-url": "https://wot-design-uni.cn/component/keyboard.html#events"}, {"name": "close", "description": "点击关闭按钮或非键盘区域时触发", "doc-url": "https://wot-design-uni.cn/component/keyboard.html#events"}]}}, {"name": "wd-row", "source": {"symbol": "WdRow"}, "description": "用于快速进行布局。", "doc-url": "https://wot-design-uni.cn/component/layout.html#row", "props": [{"name": "gutter", "description": "列元素之间的间距（单位为px）", "doc-url": "https://wot-design-uni.cn/component/layout.html#row-attributes", "type": ["number"], "default": "0"}]}, {"name": "wd-col", "source": {"symbol": "WdCol"}, "description": "用于快速进行布局。", "doc-url": "https://wot-design-uni.cn/component/layout.html#col", "props": [{"name": "span", "description": "列元素宽度", "doc-url": "https://wot-design-uni.cn/component/layout.html#col-attributes", "type": ["number"], "default": "24"}, {"name": "offset", "description": "列元素偏移距离", "doc-url": "https://wot-design-uni.cn/component/layout.html#col-attributes", "type": ["number"], "default": "0"}]}, {"name": "wd-loading", "source": {"symbol": "WdLoading"}, "description": "加载动画，用于表示加载中的过渡状态。", "doc-url": "https://wot-design-uni.cn/component/loading.html#loading", "props": [{"name": "type", "description": "加载指示器类型", "doc-url": "https://wot-design-uni.cn/component/loading.html#attributes", "type": ["string"], "default": "ring", "attribute-value": {"type": "enum"}, "values": [{"name": "outline"}]}, {"name": "color", "description": "设置加载指示器颜色", "doc-url": "https://wot-design-uni.cn/component/loading.html#attributes", "type": ["string"], "default": "#4D80F0"}, {"name": "size", "description": "设置加载指示器大小", "doc-url": "https://wot-design-uni.cn/component/loading.html#attributes", "type": ["number", "string"], "default": "32px"}]}, {"name": "wd-loadmore", "source": {"symbol": "WdLoadmore"}, "description": "用于在列表底部展示加载状态。", "doc-url": "https://wot-design-uni.cn/component/loadmore.html#loadmore", "props": [{"name": "state", "description": "加载状态", "doc-url": "https://wot-design-uni.cn/component/loadmore.html#attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "loading"}, {"name": "finished"}, {"name": "error"}]}, {"name": "loading-text", "description": "加载提示文案", "doc-url": "https://wot-design-uni.cn/component/loadmore.html#attributes", "type": ["string"], "default": "加载中..."}, {"name": "finished-text", "description": "全部加载完的提示文案", "doc-url": "https://wot-design-uni.cn/component/loadmore.html#attributes", "type": ["string"], "default": "没有更多了"}, {"name": "error-text", "description": "加载失败的提示文案", "doc-url": "https://wot-design-uni.cn/component/loadmore.html#attributes", "type": ["string"], "default": "加载失败，点击重试"}, {"name": "loading-props", "description": "loading加载组件属性", "doc-url": "https://wot-design-uni.cn/component/loadmore.html#attributes", "type": ["Partial<LoadingProps>"]}], "js": {"events": [{"name": "reload", "description": "state 为 error 加载错误时，点击文案触发 reload 事件", "doc-url": "https://wot-design-uni.cn/component/loadmore.html#events"}]}}, {"name": "wd-message-box", "source": {"symbol": "WdMessageBox"}, "description": "弹出对话框，常用于消息提示、消息确认等，支持函数调用。", "doc-url": "https://wot-design-uni.cn/component/message-box.html#messagebox", "props": [{"name": "selector", "description": "指定唯一标识", "doc-url": "https://wot-design-uni.cn/component/message-box.html#attributes", "type": ["string"]}, {"name": "root-portal", "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题", "doc-url": "https://wot-design-uni.cn/component/message-box.html#attributes", "type": ["boolean"], "default": "false"}]}, {"name": "wd-navbar", "source": {"symbol": "WdNavbar"}, "description": "为页面提供导航功能，常用于页面顶部。", "doc-url": "https://wot-design-uni.cn/component/navbar.html#navbar", "props": [{"name": "title", "description": "卡片标题", "doc-url": "https://wot-design-uni.cn/component/navbar.html#navbar-attributes", "type": ["string"]}, {"name": "left-text", "description": "左侧文案", "doc-url": "https://wot-design-uni.cn/component/navbar.html#navbar-attributes", "type": ["string"]}, {"name": "right-text", "description": "右侧文案", "doc-url": "https://wot-design-uni.cn/component/navbar.html#navbar-attributes", "type": ["string"]}, {"name": "left-arrow", "description": "显示左侧箭头", "doc-url": "https://wot-design-uni.cn/component/navbar.html#navbar-attributes", "type": ["boolean"], "default": "false", "attribute-value": {"type": "of-match"}, "values": [{"name": "true, false"}]}, {"name": "bordered", "description": "显示下边框", "doc-url": "https://wot-design-uni.cn/component/navbar.html#navbar-attributes", "type": ["boolean"], "default": "true", "attribute-value": {"type": "of-match"}, "values": [{"name": "true, false"}]}, {"name": "fixed", "description": "固定到顶部", "doc-url": "https://wot-design-uni.cn/component/navbar.html#navbar-attributes", "type": ["boolean"], "default": "false", "attribute-value": {"type": "of-match"}, "values": [{"name": "true, false"}]}, {"name": "placeholder", "description": "固定在顶部时，在标签位置生成一个等高的占位元素", "doc-url": "https://wot-design-uni.cn/component/navbar.html#navbar-attributes", "type": ["boolean"], "default": "false", "attribute-value": {"type": "of-match"}, "values": [{"name": "true, false"}]}, {"name": "z-index", "description": "导航栏 z-index", "doc-url": "https://wot-design-uni.cn/component/navbar.html#navbar-attributes", "type": ["number"], "default": "1"}, {"name": "safe-area-inset-top", "description": "开启顶部安全区适配", "doc-url": "https://wot-design-uni.cn/component/navbar.html#navbar-attributes", "type": ["boolean"], "default": "false", "attribute-value": {"type": "of-match"}, "values": [{"name": "true, false"}]}, {"name": "left-disabled", "description": "禁用左侧按钮，禁用时透明度降低，且无法点击", "doc-url": "https://wot-design-uni.cn/component/navbar.html#navbar-attributes", "type": ["boolean"], "default": "false", "attribute-value": {"type": "of-match"}, "values": [{"name": "true, false"}]}, {"name": "right-disabled", "description": "禁用右侧按钮，禁用时透明度降低，且无法点击", "doc-url": "https://wot-design-uni.cn/component/navbar.html#navbar-attributes", "type": ["boolean"], "default": "false", "attribute-value": {"type": "of-match"}, "values": [{"name": "true, false"}]}], "js": {"events": [{"name": "click-left", "description": "点击左侧按钮时触发", "doc-url": "https://wot-design-uni.cn/component/navbar.html#navbar-events"}, {"name": "click-right", "description": "点击右侧按钮时触发", "doc-url": "https://wot-design-uni.cn/component/navbar.html#navbar-events"}]}}, {"name": "wd-navbar-capsule", "source": {"symbol": "WdNavbarCapsule"}, "description": "为页面提供导航功能，常用于页面顶部。", "doc-url": "https://wot-design-uni.cn/component/navbar.html#navbarcapsule", "js": {"events": [{"name": "back", "description": "点击返回按钮时触发", "doc-url": "https://wot-design-uni.cn/component/navbar.html#navbarcapsule-events"}, {"name": "back-home", "description": "点击返回首页按钮时触发", "doc-url": "https://wot-design-uni.cn/component/navbar.html#navbarcapsule-events"}]}}, {"name": "wd-notice-bar", "source": {"symbol": "WdNoticeBar"}, "description": "通知栏组件，用于在页面顶部展示通知提醒。", "doc-url": "https://wot-design-uni.cn/component/notice-bar.html#noticebar", "props": [{"name": "text", "description": "设置通知栏文案", "doc-url": "https://wot-design-uni.cn/component/notice-bar.html#attributes", "type": [{"name": "string string[]", "source": {"symbol": "string string"}}]}, {"name": "type", "description": "设置通知栏类型", "doc-url": "https://wot-design-uni.cn/component/notice-bar.html#attributes", "type": ["string"], "default": "warning", "attribute-value": {"type": "of-match"}, "values": [{"name": "info"}, {"name": "warning"}, {"name": "danger"}]}, {"name": "prefix", "description": "设置左侧图标，使用 icon 章节中的图标名", "doc-url": "https://wot-design-uni.cn/component/notice-bar.html#attributes", "type": ["string"]}, {"name": "scrollable", "description": "是否可以滚动", "doc-url": "https://wot-design-uni.cn/component/notice-bar.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "delay", "description": "滚动动画初始延时，单位 秒(s)", "doc-url": "https://wot-design-uni.cn/component/notice-bar.html#attributes", "type": ["number"], "default": "1"}, {"name": "speed", "description": "滚动速度，单位 px/s", "doc-url": "https://wot-design-uni.cn/component/notice-bar.html#attributes", "type": ["number"], "default": "50"}, {"name": "closable", "description": "是否可以关闭", "doc-url": "https://wot-design-uni.cn/component/notice-bar.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "wrapable", "description": "是否换行展示", "doc-url": "https://wot-design-uni.cn/component/notice-bar.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "color", "description": "文字、图标颜色", "doc-url": "https://wot-design-uni.cn/component/notice-bar.html#attributes", "type": ["string"]}, {"name": "background-color", "description": "背景颜色", "doc-url": "https://wot-design-uni.cn/component/notice-bar.html#attributes", "type": ["string"]}, {"name": "direction", "description": "滚动方向", "doc-url": "https://wot-design-uni.cn/component/notice-bar.html#attributes", "type": [{"name": "NoticeBarScrollDirection", "source": {"symbol": "NoticeBarScrollDirection"}}], "default": "horizontal", "attribute-value": {"type": "of-match"}, "values": [{"name": "horizontal vertical"}]}], "js": {"events": [{"name": "close", "description": "关闭按钮点击时", "doc-url": "https://wot-design-uni.cn/component/notice-bar.html#events"}, {"name": "next", "description": "下一次滚动前触发", "doc-url": "https://wot-design-uni.cn/component/notice-bar.html#events"}, {"name": "click", "description": "点击时触发", "doc-url": "https://wot-design-uni.cn/component/notice-bar.html#events"}]}}, {"name": "wd-notify", "source": {"symbol": "WdNotify"}, "description": "通知类组件，用于在页面顶部展示通知信息。", "doc-url": "https://wot-design-uni.cn/component/notify.html#notify", "props": [{"name": "type", "description": "类型", "doc-url": "https://wot-design-uni.cn/component/notify.html#attributes", "type": [{"name": "NotifyType", "source": {"symbol": "NotifyType"}}], "default": "danger", "attribute-value": {"type": "of-match"}, "values": [{"name": "primary success warning danger"}]}, {"name": "message", "description": "展示文案，支持通过`\\n`换行", "doc-url": "https://wot-design-uni.cn/component/notify.html#attributes", "type": ["string"]}, {"name": "duration", "description": "展示时长(ms)，值为 0 时，notify 不会消失", "doc-url": "https://wot-design-uni.cn/component/notify.html#attributes", "type": ["number"], "default": "3000"}, {"name": "z-index", "description": "层级", "doc-url": "https://wot-design-uni.cn/component/notify.html#attributes", "type": ["number"], "default": "99"}, {"name": "position", "description": "弹出位置", "doc-url": "https://wot-design-uni.cn/component/notify.html#attributes", "type": [{"name": "NotifyPosition", "source": {"symbol": "NotifyPosition"}}], "default": "top", "attribute-value": {"type": "of-match"}, "values": [{"name": "top bottom"}]}, {"name": "color", "description": "字体颜色", "doc-url": "https://wot-design-uni.cn/component/notify.html#attributes", "type": ["string"]}, {"name": "background", "description": "背景颜色", "doc-url": "https://wot-design-uni.cn/component/notify.html#attributes", "type": ["string"]}, {"name": "safe-height", "description": "顶部安全高度", "doc-url": "https://wot-design-uni.cn/component/notify.html#attributes", "type": ["number", "string"]}, {"name": "selector", "description": "指定唯一标识", "doc-url": "https://wot-design-uni.cn/component/notify.html#attributes", "type": ["number"]}, {"name": "root-portal", "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题", "doc-url": "https://wot-design-uni.cn/component/notify.html#attributes", "type": ["boolean"], "default": "false"}]}, {"name": "wd-number-keyboard", "source": {"symbol": "WdNumberKeyboard"}, "description": "虚拟数字键盘，用于输入数字、密码或身份证等场景。", "doc-url": "https://wot-design-uni.cn/component/number-keyboard.html#numberkeyboard", "props": [{"name": "v-model:visible", "description": "是否展开", "doc-url": "https://wot-design-uni.cn/component/number-keyboard.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "v-model", "description": "绑定的值", "doc-url": "https://wot-design-uni.cn/component/number-keyboard.html#attributes", "type": ["string"]}, {"name": "title", "description": "标题", "doc-url": "https://wot-design-uni.cn/component/number-keyboard.html#attributes", "type": ["string"]}, {"name": "mode", "description": "键盘模式", "doc-url": "https://wot-design-uni.cn/component/number-keyboard.html#attributes", "type": ["string"], "default": "default", "attribute-value": {"type": "of-match"}, "values": [{"name": "default, custom"}]}, {"name": "z-index", "description": "层级", "doc-url": "https://wot-design-uni.cn/component/number-keyboard.html#attributes", "type": ["number"], "default": "100"}, {"name": "maxlength", "description": "最大长度", "doc-url": "https://wot-design-uni.cn/component/number-keyboard.html#attributes", "type": ["number"], "default": "Infinity"}, {"name": "show-delete-key", "description": "是否显示删除键", "doc-url": "https://wot-design-uni.cn/component/number-keyboard.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "random-key-order", "description": "是否随机键盘按键顺序", "doc-url": "https://wot-design-uni.cn/component/number-keyboard.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "close-text", "description": "确认按钮文本", "doc-url": "https://wot-design-uni.cn/component/number-keyboard.html#attributes", "type": ["string"]}, {"name": "delete-text", "description": "删除按钮文本", "doc-url": "https://wot-design-uni.cn/component/number-keyboard.html#attributes", "type": ["string"]}, {"name": "close-button-loading", "description": "关闭按钮是否显示加载状态", "doc-url": "https://wot-design-uni.cn/component/number-keyboard.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "modal", "description": "是否显示蒙层遮罩", "doc-url": "https://wot-design-uni.cn/component/number-keyboard.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "hide-on-click-outside", "description": "是否在点击外部时收起键盘", "doc-url": "https://wot-design-uni.cn/component/number-keyboard.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "lock-scroll", "description": "是否锁定背景滚动，锁定时蒙层里的内容也将无法滚动", "doc-url": "https://wot-design-uni.cn/component/number-keyboard.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "safe-area-inset-bottom", "description": "是否在底部安全区域内", "doc-url": "https://wot-design-uni.cn/component/number-keyboard.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "extra-key", "description": "额外按键", "doc-url": "https://wot-design-uni.cn/component/number-keyboard.html#attributes", "type": ["string", "string[]"]}, {"name": "root-portal", "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题", "doc-url": "https://wot-design-uni.cn/component/number-keyboard.html#attributes", "type": ["boolean"], "default": "false"}], "js": {"events": [{"name": "input", "description": "点击按键时触发", "doc-url": "https://wot-design-uni.cn/component/number-keyboard.html#events"}, {"name": "delete", "description": "点击删除键时触发", "doc-url": "https://wot-design-uni.cn/component/number-keyboard.html#events"}, {"name": "close", "description": "点击关闭按钮或非键盘区域时触发", "doc-url": "https://wot-design-uni.cn/component/number-keyboard.html#events"}]}}, {"name": "wd-overlay", "source": {"symbol": "WdOverlay"}, "description": "创建一个遮罩层，用于强调特定的页面元素，并阻止用户进行其他操作。", "doc-url": "https://wot-design-uni.cn/component/overlay.html#overlay", "props": [{"name": "show", "description": "是否展示遮罩层", "doc-url": "https://wot-design-uni.cn/component/overlay.html#attributes", "type": ["boolean"], "default": "false", "attribute-value": {"type": "of-match"}, "values": [{"name": "true"}]}, {"name": "duration", "description": "动画时长，单位毫秒", "doc-url": "https://wot-design-uni.cn/component/overlay.html#attributes", "type": ["string", "number"], "default": "300"}, {"name": "lock-scroll", "description": "是否锁定背景滚动，锁定时蒙层里的内容也将无法滚动", "doc-url": "https://wot-design-uni.cn/component/overlay.html#attributes", "type": ["boolean"], "default": "true", "attribute-value": {"type": "of-match"}, "values": [{"name": "false"}]}, {"name": "z-index", "description": "层级", "doc-url": "https://wot-design-uni.cn/component/overlay.html#attributes", "type": ["number"], "default": "10"}, {"name": "custom-style", "description": "自定义样式", "doc-url": "https://wot-design-uni.cn/component/overlay.html#attributes", "type": ["string"]}]}, {"name": "wd-pagination", "source": {"symbol": "WdPagination"}, "description": "当数据量过多时，使用分页分解数据。", "doc-url": "https://wot-design-uni.cn/component/pagination.html#pagination", "props": [{"name": "v-model", "description": "绑定值", "doc-url": "https://wot-design-uni.cn/component/pagination.html#attributes", "type": ["number"]}, {"name": "prev-text", "description": "上一页按钮文字", "doc-url": "https://wot-design-uni.cn/component/pagination.html#attributes", "type": ["string"], "default": "上一页"}, {"name": "next-text", "description": "下一页按钮文字", "doc-url": "https://wot-design-uni.cn/component/pagination.html#attributes", "type": ["string"], "default": "下一页"}, {"name": "total-page", "description": "总页数，如果有total，则优先使用total计算页数", "doc-url": "https://wot-design-uni.cn/component/pagination.html#attributes", "type": ["number"], "default": "根据页数计算"}, {"name": "page-size", "description": "分页大小", "doc-url": "https://wot-design-uni.cn/component/pagination.html#attributes", "type": ["number"], "default": "10"}, {"name": "total", "description": "总数据个数", "doc-url": "https://wot-design-uni.cn/component/pagination.html#attributes", "type": ["number"]}, {"name": "show-icon", "description": "是否展示分页Icon", "doc-url": "https://wot-design-uni.cn/component/pagination.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "show-message", "description": "是否展示文字提示", "doc-url": "https://wot-design-uni.cn/component/pagination.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "hide-if-one-page", "description": "总页数只有一页时是否隐藏", "doc-url": "https://wot-design-uni.cn/component/pagination.html#attributes", "type": ["boolean"], "default": "true"}], "js": {"events": [{"name": "change", "description": "值修改事件", "doc-url": "https://wot-design-uni.cn/component/pagination.html#events"}]}}, {"name": "wd-password-input", "source": {"symbol": "WdPasswordInput"}, "description": "带网格的输入框组件，可以用于输入密码、短信验证码等场景，通常与[数字键盘]", "doc-url": "https://wot-design-uni.cn/component/password-input.html#passwordinput", "props": [{"name": "v-model", "description": "密码值", "doc-url": "https://wot-design-uni.cn/component/password-input.html#attributes", "type": ["string"]}, {"name": "info", "description": "输入框下方文字提示", "doc-url": "https://wot-design-uni.cn/component/password-input.html#attributes", "type": ["string"]}, {"name": "error-info", "description": "输入框下方错误提示", "doc-url": "https://wot-design-uni.cn/component/password-input.html#attributes", "type": ["string"]}, {"name": "length", "description": "密码最大长度", "doc-url": "https://wot-design-uni.cn/component/password-input.html#attributes", "type": ["number"], "default": "6"}, {"name": "gutter", "description": "输入框格子之间的间距，如 20px 2em，默认单位为 px", "doc-url": "https://wot-design-uni.cn/component/password-input.html#attributes", "type": ["number", "string"], "default": "0"}, {"name": "mask", "description": "是否隐藏密码内容", "doc-url": "https://wot-design-uni.cn/component/password-input.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "focused", "description": "是否已聚焦，聚焦时会显示光标", "doc-url": "https://wot-design-uni.cn/component/password-input.html#attributes", "type": ["boolean"], "default": "false"}]}, {"name": "wd-picker-view", "source": {"symbol": "WdPickerView"}, "description": "选择器视图，用于从一组数据中选择单个或多个值。", "doc-url": "https://wot-design-uni.cn/component/picker-view.html#pickerview", "props": [{"name": "v-model", "description": "选中项，如果为多列选择器，则其类型应为数组", "doc-url": "https://wot-design-uni.cn/component/picker-view.html#attributes", "type": ["string", "number", "boolean", "array"]}, {"name": "columns", "description": "选择器数据，可以为字符串数组，也可以为对象数组，如果为二维数组，则为多列选择器", "doc-url": "https://wot-design-uni.cn/component/picker-view.html#attributes", "type": ["array"]}, {"name": "loading", "description": "加载中", "doc-url": "https://wot-design-uni.cn/component/picker-view.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "loading-color", "description": "加载的颜色，只能使用十六进制的色值写法，且不能使用缩写", "doc-url": "https://wot-design-uni.cn/component/picker-view.html#attributes", "type": ["string"], "default": "#4D80F0"}, {"name": "columns-height", "description": "picker内部滚筒高", "doc-url": "https://wot-design-uni.cn/component/picker-view.html#attributes", "type": ["number"], "default": "231"}, {"name": "value-key", "description": "选项对象中，value对应的 key", "doc-url": "https://wot-design-uni.cn/component/picker-view.html#attributes", "type": ["string"], "default": "value"}, {"name": "label-key", "description": "选项对象中，展示的文本对应的 key", "doc-url": "https://wot-design-uni.cn/component/picker-view.html#attributes", "type": ["string"], "default": "label"}, {"name": "column-change", "description": "接收 pickerView 实例、选中项、当前修改列的下标、resolve 作为入参，根据选中项和列下标进行判断，通过 pickerView 实例暴露出来的 `setColumnData` 方法修改其他列的数据源。", "doc-url": "https://wot-design-uni.cn/component/picker-view.html#attributes", "type": ["function"]}, {"name": "immediate-change", "description": "是否在手指松开时立即触发picker-view的 change 事件。若不开启则会在滚动动画结束后触发 change 事件，1.2.25版本起提供，仅微信小程序和支付宝小程序支持。", "doc-url": "https://wot-design-uni.cn/component/picker-view.html#attributes", "type": ["boolean"], "default": "false"}], "js": {"events": [{"name": "change", "description": "选项值修改时触发", "doc-url": "https://wot-design-uni.cn/component/picker-view.html#events"}, {"name": "pickstart", "description": "当滚动选择开始时候触发事件", "doc-url": "https://wot-design-uni.cn/component/picker-view.html#events"}, {"name": "pickend", "description": "当滚动选择结束时候触发事件", "doc-url": "https://wot-design-uni.cn/component/picker-view.html#events"}]}}, {"name": "wd-picker", "source": {"symbol": "WdPicker"}, "description": "Picker 组件为 popup 和 pickerView 的组合。", "doc-url": "https://wot-design-uni.cn/component/picker.html#picker", "props": [{"name": "v-model", "description": "选中项，如果为多列选择器，则其类型应为数组", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["string", "number", "boolean", "array"]}, {"name": "columns", "description": "选择器数据，可以为字符串数组，也可以为对象数组，如果为二维数组，则为多列选择器", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["array"]}, {"name": "loading", "description": "加载中", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "loading-color", "description": "加载的颜色，只能使用十六进制的色值写法，且不能使用缩写", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["string"], "default": "#4D80F0"}, {"name": "columns-height", "description": "picker内部滚筒高", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["number"], "default": "231"}, {"name": "value-key", "description": "选项对象中，value对应的 key", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["string"], "default": "value"}, {"name": "label-key", "description": "选项对象中，展示的文本对应的 key", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["string"], "default": "label"}, {"name": "title", "description": "弹出层标题", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["string"]}, {"name": "cancel-button-text", "description": "取消按钮文案", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["string"], "default": "取消"}, {"name": "confirm-button-text", "description": "确认按钮文案", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["string"], "default": "完成"}, {"name": "label", "description": "选择器左侧文案", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["string"]}, {"name": "placeholder", "description": "选择器占位符", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["string"], "default": "请选择"}, {"name": "disabled", "description": "禁用", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "readonly", "description": "只读", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "display-format", "description": "自定义展示文案的格式化函数，返回一个字符串", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["function"]}, {"name": "column-change", "description": "接收 pickerView 实例、选中项、当前修改列的下标、resolve 作为入参，根据选中项和列下标进行判断，通过 pickerView 实例暴露出来的 `setColumnData` 方法修改其他列的数据源", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["function"]}, {"name": "size", "description": "设置选择器大小", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "large"}]}, {"name": "label-width", "description": "设置左侧标题宽度", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["string"], "default": "33%"}, {"name": "error", "description": "是否为错误状态，错误状态时右侧内容为红色", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "required", "description": "表单属性，必填", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "align-right", "description": "选择器的值靠右展示", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "use-label-slot", "description": "label 使用插槽", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "use-default-slot", "description": "使用默认插槽", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "before-confirm", "description": "确定前校验函数，接收 (value, resolve, picker) 参数，通过 resolve 继续执行 picker，resolve 接收1个boolean参数", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["function"]}, {"name": "close-on-click-modal", "description": "点击遮罩是否关闭", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "z-index", "description": "弹窗层级", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["number"], "default": "15"}, {"name": "safe-area-inset-bottom", "description": "弹出面板是否设置底部安全距离（iphone X 类型的机型）", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "ellipsis", "description": "是否超出隐藏", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "prop", "description": "表单域 `model` 字段名，在使用表单校验功能的情况下，该属性是必填的", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["string"]}, {"name": "rules", "description": "表单验证规则，结合`wd-form`组件使用", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": [{"name": "FormItemRule []", "source": {"symbol": "FormItemRule "}}], "default": "[]"}, {"name": "immediate-change", "description": "是否在手指松开时立即触发picker-view的 change 事件。若不开启则会在滚动动画结束后触发 change 事件，1.2.25版本起提供，仅微信小程序和支付宝小程序支持", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "clearable", "description": "显示清空按钮", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "root-portal", "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题", "doc-url": "https://wot-design-uni.cn/component/picker.html#attributes", "type": ["boolean"], "default": "false"}], "js": {"events": [{"name": "confirm", "description": "点击右侧按钮触发", "doc-url": "https://wot-design-uni.cn/component/picker.html#events"}, {"name": "cancel", "description": "点击左侧按钮触发", "doc-url": "https://wot-design-uni.cn/component/picker.html#events"}, {"name": "open", "description": "打开选择器弹出层时触发", "doc-url": "https://wot-design-uni.cn/component/picker.html#events"}, {"name": "clear", "description": "点击清空按钮时触发", "doc-url": "https://wot-design-uni.cn/component/picker.html#events"}]}}, {"name": "wd-popover", "source": {"symbol": "WdPopover"}, "description": "常用于展示提示信息。", "doc-url": "https://wot-design-uni.cn/component/popover.html#popover", "props": [{"name": "v-model", "description": "手动状态是否可见", "doc-url": "https://wot-design-uni.cn/component/popover.html#popover-attributes", "type": ["boolean"], "default": "false"}, {"name": "content", "description": "显示的内容，也可以通过 `slot#content` 传入", "doc-url": "https://wot-design-uni.cn/component/popover.html#popover-attributes", "type": ["string", {"name": "array（当模式为菜单模式时，content 属性格式为 Array）", "source": {"symbol": "array（当模式为菜单模式时，content 属性格式为 Array）"}}]}, {"name": "mode", "description": "当前显示的模式，决定内容的展现形式", "doc-url": "https://wot-design-uni.cn/component/popover.html#popover-attributes", "type": ["string"], "default": "normal", "attribute-value": {"type": "enum"}, "values": [{"name": "normal（普通模式）"}, {"name": "menu（菜单模式）"}]}, {"name": "placement", "description": "popover 的出现位置", "doc-url": "https://wot-design-uni.cn/component/popover.html#popover-attributes", "type": ["string"], "default": "bottom", "attribute-value": {"type": "enum"}, "values": [{"name": "top"}, {"name": "top-start"}, {"name": "top-end"}, {"name": "bottom"}, {"name": "bottom-start"}, {"name": "bottom-end"}, {"name": "left"}, {"name": "left-start"}, {"name": "left-end"}, {"name": "right"}, {"name": "right-start"}, {"name": "right-end"}]}, {"name": "visible-arrow", "description": "是否显示 popover 箭头", "doc-url": "https://wot-design-uni.cn/component/popover.html#popover-attributes", "type": ["boolean"], "default": "true"}, {"name": "disabled", "description": "popover 是否可用", "doc-url": "https://wot-design-uni.cn/component/popover.html#popover-attributes", "type": ["boolean"], "default": "false"}, {"name": "offset", "description": "出现位置的偏移量", "doc-url": "https://wot-design-uni.cn/component/popover.html#popover-attributes", "type": ["number"], "default": "0"}]}, {"name": "wd-popover", "source": {"symbol": "WdPopover"}, "description": "常用于展示提示信息。", "doc-url": "https://wot-design-uni.cn/component/popover.html#popover", "js": {"events": [{"name": "open", "description": "显示时触发", "doc-url": "https://wot-design-uni.cn/component/popover.html#events"}, {"name": "close", "description": "隐藏时触发", "doc-url": "https://wot-design-uni.cn/component/popover.html#events"}, {"name": "change", "description": "pop 显隐值变化时触发", "doc-url": "https://wot-design-uni.cn/component/popover.html#events"}, {"name": "menuclick", "description": "menu 模式下点击某一选项触发", "doc-url": "https://wot-design-uni.cn/component/popover.html#events"}]}}, {"name": "wd-popup", "source": {"symbol": "WdPopup"}, "description": "弹出层组件，用于展示弹窗、信息提示等内容。", "doc-url": "https://wot-design-uni.cn/component/popup.html#popup", "props": [{"name": "v-model", "description": "弹出层是否显示", "doc-url": "https://wot-design-uni.cn/component/popup.html#attributes", "type": ["boolean"]}, {"name": "position", "description": "弹出位置", "doc-url": "https://wot-design-uni.cn/component/popup.html#attributes", "type": ["string"], "default": "center", "attribute-value": {"type": "enum"}, "values": [{"name": "center"}, {"name": "top"}, {"name": "right"}, {"name": "bottom"}, {"name": "left"}]}, {"name": "closable", "description": "关闭按钮", "doc-url": "https://wot-design-uni.cn/component/popup.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "close-on-click-modal", "description": "点击遮罩是否关闭", "doc-url": "https://wot-design-uni.cn/component/popup.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "duration", "description": "动画持续时间", "doc-url": "https://wot-design-uni.cn/component/popup.html#attributes", "type": ["number", "boolean"], "default": "300(ms)"}, {"name": "z-index", "description": "设置层级", "doc-url": "https://wot-design-uni.cn/component/popup.html#attributes", "type": ["number"], "default": "10"}, {"name": "custom-style", "description": "自定义弹出层样式", "doc-url": "https://wot-design-uni.cn/component/popup.html#attributes", "type": ["string"]}, {"name": "modal", "description": "是否显示遮罩", "doc-url": "https://wot-design-uni.cn/component/popup.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "modal-style", "description": "自定义modal蒙层样式", "doc-url": "https://wot-design-uni.cn/component/popup.html#attributes", "type": ["string"]}, {"name": "hide-when-close", "description": "是否当关闭时将弹出层隐藏（display: none)", "doc-url": "https://wot-design-uni.cn/component/popup.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "lazy-render", "description": "弹层内容懒渲染，触发展示时才渲染内容", "doc-url": "https://wot-design-uni.cn/component/popup.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "safe-area-inset-bottom", "description": "弹出面板是否设置底部安全距离（iphone X 类型的机型）", "doc-url": "https://wot-design-uni.cn/component/popup.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "transition", "description": "动画类型，参见 wd-transition 组件的name", "doc-url": "https://wot-design-uni.cn/component/popup.html#attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "fade"}, {"name": "fade-up"}, {"name": "fade-down"}, {"name": "fade-left"}, {"name": "fade-right"}, {"name": "slide-up"}, {"name": "slide-down"}, {"name": "slide-left"}, {"name": "slide-right"}, {"name": "zoom-in"}]}, {"name": "lock-scroll", "description": "是否锁定背景滚动，锁定时蒙层里的内容也将无法滚动", "doc-url": "https://wot-design-uni.cn/component/popup.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "root-portal", "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题", "doc-url": "https://wot-design-uni.cn/component/popup.html#attributes", "type": ["boolean"], "default": "false"}], "js": {"events": [{"name": "close", "description": "弹出层关闭时触发", "doc-url": "https://wot-design-uni.cn/component/popup.html#events"}, {"name": "click-modal", "description": "点击遮罩时触发", "doc-url": "https://wot-design-uni.cn/component/popup.html#events"}, {"name": "before-enter", "description": "进入前触发", "doc-url": "https://wot-design-uni.cn/component/popup.html#events"}, {"name": "enter", "description": "进入时触发", "doc-url": "https://wot-design-uni.cn/component/popup.html#events"}, {"name": "after-enter", "description": "进入后触发", "doc-url": "https://wot-design-uni.cn/component/popup.html#events"}, {"name": "before-leave", "description": "离开前触发", "doc-url": "https://wot-design-uni.cn/component/popup.html#events"}, {"name": "leave", "description": "离开时触发", "doc-url": "https://wot-design-uni.cn/component/popup.html#events"}, {"name": "after-leave", "description": "离开后触发", "doc-url": "https://wot-design-uni.cn/component/popup.html#events"}]}}, {"name": "wd-progress", "source": {"symbol": "WdProgress"}, "description": "用于展示操作的当前进度。", "doc-url": "https://wot-design-uni.cn/component/progress.html#progress", "props": [{"name": "percentage", "description": "进度数值，最大值 100", "doc-url": "https://wot-design-uni.cn/component/progress.html#attributes", "type": ["number"], "default": "0"}, {"name": "hide-text", "description": "隐藏进度文字", "doc-url": "https://wot-design-uni.cn/component/progress.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "color", "description": "进度条颜色", "doc-url": "https://wot-design-uni.cn/component/progress.html#attributes", "type": ["string", {"name": "ProgressColor[]", "source": {"symbol": "ProgressColor"}}, "string[]"]}, {"name": "status", "description": "进度条状态", "doc-url": "https://wot-design-uni.cn/component/progress.html#attributes", "type": ["string"], "attribute-value": {"type": "of-match"}, "values": [{"name": "success"}, {"name": "danger"}, {"name": "warning"}]}, {"name": "duration", "description": "进度增加 1%所需毫秒数", "doc-url": "https://wot-design-uni.cn/component/progress.html#attributes", "type": ["number"], "default": "30"}]}, {"name": "wd-radio-group", "source": {"symbol": "WdRadioGroup"}, "description": "单选框，用于在一组备选项中进行单选。", "doc-url": "https://wot-design-uni.cn/component/radio.html#radiogroup", "props": [{"name": "v-model", "description": "会自动选中value对应的单选框", "doc-url": "https://wot-design-uni.cn/component/radio.html#radiogroup-attributes", "type": ["string", "number", "boolean"]}, {"name": "shape", "description": "单选框形状", "doc-url": "https://wot-design-uni.cn/component/radio.html#radiogroup-attributes", "type": ["string"], "default": "check", "attribute-value": {"type": "enum"}, "values": [{"name": "dot"}, {"name": "button"}, {"name": "check"}]}, {"name": "size", "description": "设置大小", "doc-url": "https://wot-design-uni.cn/component/radio.html#radiogroup-attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "large"}]}, {"name": "checked-color", "description": "选中的颜色", "doc-url": "https://wot-design-uni.cn/component/radio.html#radiogroup-attributes", "type": ["string"], "default": "#4D80F0"}, {"name": "disabled", "description": "禁用", "doc-url": "https://wot-design-uni.cn/component/radio.html#radiogroup-attributes", "type": ["boolean"], "default": "false"}, {"name": "max-width", "description": "文字位置最大宽度", "doc-url": "https://wot-design-uni.cn/component/radio.html#radiogroup-attributes", "type": ["string"]}, {"name": "inline", "description": "同行展示", "doc-url": "https://wot-design-uni.cn/component/radio.html#radiogroup-attributes", "type": ["boolean"], "default": "false"}, {"name": "cell", "description": "表单模式", "doc-url": "https://wot-design-uni.cn/component/radio.html#radiogroup-attributes", "type": ["boolean"], "default": "false"}, {"name": "icon-placement", "description": "勾选图标对齐方式", "doc-url": "https://wot-design-uni.cn/component/radio.html#radiogroup-attributes", "type": ["string"], "default": "auto", "attribute-value": {"type": "enum"}, "values": [{"name": "left"}, {"name": "right"}, {"name": "auto"}]}], "js": {"events": [{"name": "change", "description": "绑定值变化时触发", "doc-url": "https://wot-design-uni.cn/component/radio.html#radiogroup-events"}]}}, {"name": "wd-radio", "source": {"symbol": "WdRadio"}, "description": "单选框，用于在一组备选项中进行单选。", "doc-url": "https://wot-design-uni.cn/component/radio.html#radio", "props": [{"name": "value", "description": "单选框选中时的值。会自动匹配radioGroup的value", "doc-url": "https://wot-design-uni.cn/component/radio.html#radio-attributes", "type": ["string", "number", "boolean"]}, {"name": "shape", "description": "单选框形状", "doc-url": "https://wot-design-uni.cn/component/radio.html#radio-attributes", "type": ["string"], "default": "check", "attribute-value": {"type": "enum"}, "values": [{"name": "dot"}, {"name": "button"}, {"name": "check"}]}, {"name": "checked-color", "description": "选中的颜色", "doc-url": "https://wot-design-uni.cn/component/radio.html#radio-attributes", "type": ["string"], "default": "#4D80F0"}, {"name": "disabled", "description": "禁用", "doc-url": "https://wot-design-uni.cn/component/radio.html#radio-attributes", "type": ["boolean"], "default": "false"}]}, {"name": "wd-rate", "source": {"symbol": "WdRate"}, "description": "用于快速的评价操作，或对评价进行展示。", "doc-url": "https://wot-design-uni.cn/component/rate.html#rate", "props": [{"name": "v-model", "description": "当前分数", "doc-url": "https://wot-design-uni.cn/component/rate.html#attributes", "type": ["number"]}, {"name": "num", "description": "评分最大值", "doc-url": "https://wot-design-uni.cn/component/rate.html#attributes", "type": ["number"], "default": "5"}, {"name": "readonly", "description": "是否只读", "doc-url": "https://wot-design-uni.cn/component/rate.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "size", "description": "图标大小", "doc-url": "https://wot-design-uni.cn/component/rate.html#attributes", "type": ["string"], "default": "16px"}, {"name": "space", "description": "图标间距", "doc-url": "https://wot-design-uni.cn/component/rate.html#attributes", "type": ["string"], "default": "4px"}, {"name": "color", "description": "未选中的图标颜色", "doc-url": "https://wot-design-uni.cn/component/rate.html#attributes", "type": ["string"], "default": "#E8E8E8"}, {"name": "active-color", "description": "选中的图标颜色(支持传颜色数组，共有 2 个元素，为 2 个分段所对应的颜色)", "doc-url": "https://wot-design-uni.cn/component/rate.html#attributes", "type": ["string", "array"], "default": "linear-gradient(180deg, rgba(255,238,0,1) 0%,rgba(250,176,21,1) 100%)"}, {"name": "icon", "description": "未选中的图标类名", "doc-url": "https://wot-design-uni.cn/component/rate.html#attributes", "type": ["string"], "default": "wd-icon-star-on"}, {"name": "active-icon", "description": "选中的图标类名", "doc-url": "https://wot-design-uni.cn/component/rate.html#attributes", "type": ["string"], "default": "wd-icon-star-on"}, {"name": "disabled", "description": "是否禁用", "doc-url": "https://wot-design-uni.cn/component/rate.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "disabled-color", "description": "禁用的图标颜色", "doc-url": "https://wot-design-uni.cn/component/rate.html#attributes", "type": ["string"], "default": "linear-gradient(315deg, rgba(177,177,177,1) 0%,rgba(199,199,199,1) 100%)"}, {"name": "allow-half", "description": "是否允许半选", "doc-url": "https://wot-design-uni.cn/component/rate.html#attributes", "type": ["boolean"], "default": "false"}], "js": {"events": [{"name": "change", "description": "点击icon，修改分值事件", "doc-url": "https://wot-design-uni.cn/component/rate.html#events"}]}}, {"name": "wd-resize", "source": {"symbol": "WdResize"}, "description": "当组件包裹的文档流尺寸发生变化时，触发 `size` 事件。一般用于监听 dom 内容更新时导致的 dom 尺寸位置的变化，重新获取 dom 尺寸和位置，进行内容展示的计算操作。", "doc-url": "https://wot-design-uni.cn/component/resize.html#resize", "js": {"events": [{"name": "resize", "description": "尺寸发生变化时触发", "doc-url": "https://wot-design-uni.cn/component/resize.html#events"}]}}, {"name": "wd-root-portal", "source": {"symbol": "WdRootPortal"}, "description": "是否从页面中脱离出来，用于解决各种 fixed 失效问题，主要用于制作弹窗、弹出层等。", "doc-url": "https://wot-design-uni.cn/component/root-portal.html#root-portal", "slots": [{"name": "default", "description": "默认插槽，用于渲染传送内容", "doc-url": "https://wot-design-uni.cn/component/root-portal.html#slots"}]}, {"name": "wd-search", "source": {"symbol": "WdSearch"}, "description": "搜索框组件，支持输入框聚焦、失焦、输入、搜索、取消、清空事件。", "doc-url": "https://wot-design-uni.cn/component/search.html#search", "props": [{"name": "placeholder", "description": "搜索框占位文本", "doc-url": "https://wot-design-uni.cn/component/search.html#attributes", "type": ["string"], "default": "搜索"}, {"name": "placeholder-left", "description": "placeholder 居左边", "doc-url": "https://wot-design-uni.cn/component/search.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "cancel-txt", "description": "搜索框右侧文本", "doc-url": "https://wot-design-uni.cn/component/search.html#attributes", "type": ["string"], "default": "取消"}, {"name": "light", "description": "搜索框亮色（白色）", "doc-url": "https://wot-design-uni.cn/component/search.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "hide-cancel", "description": "是否隐藏右侧文本", "doc-url": "https://wot-design-uni.cn/component/search.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "disabled", "description": "是否禁用搜索框", "doc-url": "https://wot-design-uni.cn/component/search.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "maxlength", "description": "原生属性，设置最大长度。-1 表示无限制", "doc-url": "https://wot-design-uni.cn/component/search.html#attributes", "type": ["string", "number"], "default": "-1"}, {"name": "v-model", "description": "输入框内容，双向绑定", "doc-url": "https://wot-design-uni.cn/component/search.html#attributes", "type": ["string"]}, {"name": "use-suffix-slot", "description": "~~是否使用输入框右侧插槽~~**（已废弃，将在下一个 minor 版本被移除，直接使用插槽即可）**", "doc-url": "https://wot-design-uni.cn/component/search.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "focus", "description": "是否自动聚焦", "doc-url": "https://wot-design-uni.cn/component/search.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "focus-when-clear", "description": "是否在点击清除按钮时聚焦输入框", "doc-url": "https://wot-design-uni.cn/component/search.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "placeholder-style", "description": "原生属性，指定 placeholder 的样式，目前仅支持color,font-size和font-weight", "doc-url": "https://wot-design-uni.cn/component/search.html#attributes", "type": ["string"]}, {"name": "placeholder-class", "description": "原生属性，指定 placeholder 的样式类", "doc-url": "https://wot-design-uni.cn/component/search.html#attributes", "type": ["string"]}], "js": {"events": [{"name": "focus", "description": "输入框聚焦事件", "doc-url": "https://wot-design-uni.cn/component/search.html#events"}, {"name": "blur", "description": "监听输入框失焦事件", "doc-url": "https://wot-design-uni.cn/component/search.html#events"}, {"name": "search", "description": "监听输入框搜索事件", "doc-url": "https://wot-design-uni.cn/component/search.html#events"}, {"name": "clear", "description": "监听输入框清空按钮事件", "doc-url": "https://wot-design-uni.cn/component/search.html#events"}, {"name": "cancel", "description": "监听输入框右侧文本点击事件", "doc-url": "https://wot-design-uni.cn/component/search.html#events"}, {"name": "change", "description": "监听输入框内容变化事件", "doc-url": "https://wot-design-uni.cn/component/search.html#events"}]}}, {"name": "wd-segmented", "source": {"symbol": "WdSegmented"}, "description": "分段器用于展示多个选项并允许用户选择其中单个选项。", "doc-url": "https://wot-design-uni.cn/component/segmented.html#segmented", "props": [{"name": "v-model:value", "description": "当前选中的值", "doc-url": "https://wot-design-uni.cn/component/segmented.html#attributes", "type": ["string", "number"]}, {"name": "disabled", "description": "是否禁用分段器", "doc-url": "https://wot-design-uni.cn/component/segmented.html#attributes", "type": ["boolean"], "default": "false", "attribute-value": {"type": "of-match"}, "values": [{"name": "true"}, {"name": "false"}]}, {"name": "size", "description": "控件尺寸", "doc-url": "https://wot-design-uni.cn/component/segmented.html#attributes", "type": ["string"], "default": "middle", "attribute-value": {"type": "enum"}, "values": [{"name": "large"}, {"name": "middle"}, {"name": "small"}]}, {"name": "options", "description": "数据集合", "doc-url": "https://wot-design-uni.cn/component/segmented.html#attributes", "type": ["string[]", "number[]", {"name": "SegmentedOption[]", "source": {"symbol": "SegmentedOption"}}], "default": "[]"}, {"name": "vibrate-short", "description": "切换选项时是否振动", "doc-url": "https://wot-design-uni.cn/component/segmented.html#attributes", "type": ["boolean"], "default": "false", "attribute-value": {"type": "of-match"}, "values": [{"name": "true"}, {"name": "false"}]}], "js": {"events": [{"name": "change", "description": "选项切换时触发", "doc-url": "https://wot-design-uni.cn/component/segmented.html#events"}, {"name": "click", "description": "选项点击时触发", "doc-url": "https://wot-design-uni.cn/component/segmented.html#events"}]}}, {"name": "wd-select-picker", "source": {"symbol": "WdSelectPicker"}, "description": "用于从一组选项中进行单选或多选。", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#selectpicker", "props": [{"name": "v-model", "description": "选中项，`type`为`checkbox`时类型为array；`type`为`radio`时类型为number/boolean/string", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["array", "number", "boolean", "string"]}, {"name": "columns", "description": "选择器数据，一维数组", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["array"]}, {"name": "type", "description": "单复选选择器类型", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["string"], "default": "checkbox", "attribute-value": {"type": "enum"}, "values": [{"name": "checkbox"}, {"name": "radio"}]}, {"name": "value-key", "description": "选项对象中value对应的key", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["string"], "default": "value"}, {"name": "label-key", "description": "选项对象中展示文本对应的key", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["string"], "default": "label"}, {"name": "title", "description": "弹出层标题", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["string"]}, {"name": "label", "description": "选择器左侧文案", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["string"]}, {"name": "placeholder", "description": "选择器占位符", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["string"], "default": "请选择"}, {"name": "disabled", "description": "禁用", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "loading", "description": "加载中", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "loading-color", "description": "加载颜色（十六进制，不能缩写）", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["String"], "default": "#4D80F0"}, {"name": "readonly", "description": "只读", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "display-format", "description": "自定义展示文案的格式化函数", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["function"]}, {"name": "confirm-button-text", "description": "确认按钮文案", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["string"], "default": "确认"}, {"name": "size", "description": "选择器大小", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "large"}]}, {"name": "label-width", "description": "左侧标题宽度", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["string"], "default": "33%"}, {"name": "error", "description": "错误状态（右侧内容红色）", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "required", "description": "必填样式", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "align-right", "description": "值靠右展示", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "before-confirm", "description": "确定前校验函数，接收(value,resolve)参数", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["function"]}, {"name": "select-size", "description": "picker内部选项组尺寸", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "large"}]}, {"name": "min", "description": "最小选中数量（仅checkbox）", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["number"], "default": "0"}, {"name": "max", "description": "最大选中数量（0为无限，仅checkbox）", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["number"], "default": "0"}, {"name": "checked-color", "description": "选中颜色（单/复选框）", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["string"], "default": "#4D80F0"}, {"name": "use-default-slot", "description": "使用默认插槽", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "use-label-slot", "description": "使用label插槽", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "close-on-click-modal", "description": "点击遮罩关闭", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "z-index", "description": "弹窗层级", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["number"], "default": "15"}, {"name": "safe-area-inset-bottom", "description": "底部安全距离（iPhone X类机型）", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "filterable", "description": "可搜索（仅本地）", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "filter-placeholder", "description": "搜索框占位符", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["string"], "default": "搜索"}, {"name": "ellipsis", "description": "超出隐藏", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "scroll-into-view", "description": "重新打开时滚动到选中项", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "show-confirm", "description": "是否显示确认按钮（仅radio）", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "prop", "description": "表单域model字段名（校验必填）", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["string"]}, {"name": "rules", "description": "表单验证规则（配合wd-form）", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": [{"name": "FormItemRule[]", "source": {"symbol": "FormItemRule"}}], "default": "[]"}, {"name": "clearable", "description": "显示清空按钮", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "root-portal", "description": "脱离页面解决fixed失效问题", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#attributes", "type": ["boolean"], "default": "false"}], "js": {"events": [{"name": "confirm", "description": "点击确认时触发", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#events"}, {"name": "change", "description": "picker 内选项更改时触发", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#events"}, {"name": "cancel", "description": "点击关闭按钮或者蒙层时触发", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#events"}, {"name": "close", "description": "弹窗关闭时触发", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#events"}, {"name": "open", "description": "弹窗打开时触发", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#events"}, {"name": "clear", "description": "点击清空按钮时触发", "doc-url": "https://wot-design-uni.cn/component/select-picker.html#events"}]}}, {"name": "wd-sidebar-item", "source": {"symbol": "WdSidebarItem"}, "description": "垂直展示的导航栏，用于在不同的内容区域之间进行切换。", "doc-url": "https://wot-design-uni.cn/component/sidebar.html#sidebaritem", "props": [{"name": "label", "description": "当前选项标题", "doc-url": "https://wot-design-uni.cn/component/sidebar.html#sidebaritem-attributes", "type": ["string"]}, {"name": "value", "description": "当前选项的值，唯一标识", "doc-url": "https://wot-design-uni.cn/component/sidebar.html#sidebaritem-attributes", "type": ["number", "string"]}, {"name": "icon", "description": "图标", "doc-url": "https://wot-design-uni.cn/component/sidebar.html#sidebaritem-attributes", "type": ["string"]}, {"name": "badge", "description": "徽标属性，徽标显示值", "doc-url": "https://wot-design-uni.cn/component/sidebar.html#sidebaritem-attributes", "type": ["number", "string", "null"]}, {"name": "is-dot", "description": "徽标属性，是否点状徽标", "doc-url": "https://wot-design-uni.cn/component/sidebar.html#sidebaritem-attributes", "type": ["boolean"], "default": "false"}, {"name": "max", "description": "徽标属性，徽标最大值", "doc-url": "https://wot-design-uni.cn/component/sidebar.html#sidebaritem-attributes", "type": ["number"], "default": "99"}, {"name": "disabled", "description": "是否禁用", "doc-url": "https://wot-design-uni.cn/component/sidebar.html#sidebaritem-attributes", "type": ["boolean"], "default": "false"}, {"name": "badge-props", "description": "自定义徽标的属性，传入的对象会被透传给 [Badge 组件的 props](/component/badge#attributes)", "doc-url": "https://wot-design-uni.cn/component/sidebar.html#sidebaritem-attributes", "type": [{"name": "BadgeProps", "source": {"symbol": "BadgeProps"}}]}]}, {"name": "wd-sidebar", "source": {"symbol": "WdSidebar"}, "description": "垂直展示的导航栏，用于在不同的内容区域之间进行切换。", "doc-url": "https://wot-design-uni.cn/component/sidebar.html#sidebar", "props": [{"name": "v-model", "description": "当前导航项的索引", "doc-url": "https://wot-design-uni.cn/component/sidebar.html#attributes", "type": ["string", "number"], "default": "0"}, {"name": "before-change", "description": "切换导航项前钩子，可以在切换标签前执行特定的逻辑，接收 { value, resolve } 参数，通过 resolve 继续执行，resolve 接收 1 个 boolean 参数", "doc-url": "https://wot-design-uni.cn/component/sidebar.html#attributes", "type": ["function"]}], "js": {"events": [{"name": "change", "description": "选项切换时触发", "doc-url": "https://wot-design-uni.cn/component/sidebar.html#events"}]}}, {"name": "wd-signature", "source": {"symbol": "WdSignature"}, "description": "用于签名场景，基于 Canvas 实现的签名组件。提供了基础签名、历史记录、笔锋效果等功能。", "doc-url": "https://wot-design-uni.cn/component/signature.html#signature", "props": [{"name": "pen-color", "description": "签名笔颜色", "doc-url": "https://wot-design-uni.cn/component/signature.html#attributes", "type": ["string"], "default": "#000000"}, {"name": "line-width", "description": "签名笔宽度", "doc-url": "https://wot-design-uni.cn/component/signature.html#attributes", "type": ["number"], "default": "3"}, {"name": "height", "description": "画布的高度", "doc-url": "https://wot-design-uni.cn/component/signature.html#attributes", "type": ["number"], "default": "200"}, {"name": "width", "description": "画布的宽度", "doc-url": "https://wot-design-uni.cn/component/signature.html#attributes", "type": ["number"], "default": "300"}, {"name": "clear-text", "description": "清空按钮的文本", "doc-url": "https://wot-design-uni.cn/component/signature.html#attributes", "type": ["string"]}, {"name": "confirm-text", "description": "确认按钮的文本", "doc-url": "https://wot-design-uni.cn/component/signature.html#attributes", "type": ["string"]}, {"name": "file-type", "description": "导出图片类型", "doc-url": "https://wot-design-uni.cn/component/signature.html#attributes", "type": ["string"], "default": "png"}, {"name": "quality", "description": "导出图片质量(0-1)", "doc-url": "https://wot-design-uni.cn/component/signature.html#attributes", "type": ["number"], "default": "1"}, {"name": "export-scale", "description": "导出图片的缩放比例", "doc-url": "https://wot-design-uni.cn/component/signature.html#attributes", "type": ["number"], "default": "1"}, {"name": "disabled", "description": "是否禁用签名板", "doc-url": "https://wot-design-uni.cn/component/signature.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "background-color", "description": "画板的背景色", "doc-url": "https://wot-design-uni.cn/component/signature.html#attributes", "type": ["string"]}, {"name": "disable-scroll", "description": "是否禁用画布滚动", "doc-url": "https://wot-design-uni.cn/component/signature.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "enable-history", "description": "是否开启历史记录", "doc-url": "https://wot-design-uni.cn/component/signature.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "step", "description": "历史记录步长", "doc-url": "https://wot-design-uni.cn/component/signature.html#attributes", "type": ["number"], "default": "1"}, {"name": "pressure", "description": "是否启用笔锋模式", "doc-url": "https://wot-design-uni.cn/component/signature.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "min-width", "description": "笔锋模式最小宽度", "doc-url": "https://wot-design-uni.cn/component/signature.html#attributes", "type": ["number"], "default": "2"}, {"name": "max-width", "description": "笔锋模式最大宽度", "doc-url": "https://wot-design-uni.cn/component/signature.html#attributes", "type": ["number"], "default": "6"}, {"name": "min-speed", "description": "笔锋模式速度阈值", "doc-url": "https://wot-design-uni.cn/component/signature.html#attributes", "type": ["number"], "default": "1.5"}], "slots": [{"name": "footer", "description": "自定义底部按钮", "doc-url": "https://wot-design-uni.cn/component/signature.html#slots"}], "js": {"events": [{"name": "start", "description": "开始签名时触发", "doc-url": "https://wot-design-uni.cn/component/signature.html#events"}, {"name": "end", "description": "结束签名时触发", "doc-url": "https://wot-design-uni.cn/component/signature.html#events"}, {"name": "signing", "description": "签名过程中触发", "doc-url": "https://wot-design-uni.cn/component/signature.html#events"}, {"name": "confirm", "description": "确认签名时触发", "doc-url": "https://wot-design-uni.cn/component/signature.html#events"}, {"name": "clear", "description": "清空签名时触发", "doc-url": "https://wot-design-uni.cn/component/signature.html#events"}]}}, {"name": "wd-skeleton", "source": {"symbol": "WdSkeleton"}, "description": "用于等待加载内容所展示的占位图形组合，有动态效果加载效果，减少用户等待焦虑。", "doc-url": "https://wot-design-uni.cn/component/skeleton.html#skeleton", "props": [{"name": "theme", "description": "骨架图风格", "doc-url": "https://wot-design-uni.cn/component/skeleton.html#attributes", "type": [{"name": "SkeletonTheme", "source": {"symbol": "SkeletonTheme"}}], "attribute-value": {"type": "of-match"}, "values": [{"name": "text avatar paragraph image"}]}, {"name": "row-col", "description": "用于设置行列数量、宽度高度、间距等<br />【示例一】`[1, 1, 2]` 表示输出三行骨架图，第一行一列，第二行一列，第三行两列。 <br />【示例二】`[1, 1, { width: '100px' }]` 表示自定义第三行的宽度为 `100px`。 <br />【示例三】`[1, 2, [{ width, height }, { width, height, marginLeft }]]` 表示第三行有两列，且自定义宽度、高度和间距", "doc-url": "https://wot-design-uni.cn/component/skeleton.html#attributes", "type": [{"name": "SkeletonRowCol", "source": {"symbol": "SkeletonRowCol"}}]}, {"name": "loading", "description": "是否为加载状态，如果是则显示骨架图，如果不是则显示加载完成的内容", "doc-url": "https://wot-design-uni.cn/component/skeleton.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "animation", "description": "动画效果", "doc-url": "https://wot-design-uni.cn/component/skeleton.html#attributes", "type": [{"name": "SkeletonAnimation", "source": {"symbol": "SkeletonAnimation"}}], "attribute-value": {"type": "of-match"}, "values": [{"name": "gradient flashed"}]}]}, {"name": "wd-slider", "source": {"symbol": "WdSlider"}, "description": "支持单向滑块和双向滑块。", "doc-url": "https://wot-design-uni.cn/component/slider.html#slider", "props": [{"name": "v-model", "description": "滑块值，如果为array，则为双向滑块", "doc-url": "https://wot-design-uni.cn/component/slider.html#attributes", "type": ["number", "array"]}, {"name": "hide-min-max", "description": "是否显示左右的最大最小值", "doc-url": "https://wot-design-uni.cn/component/slider.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "hide-label", "description": "是否显示当前滑块值", "doc-url": "https://wot-design-uni.cn/component/slider.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "disabled", "description": "是否禁用", "doc-url": "https://wot-design-uni.cn/component/slider.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "max", "description": "最大值", "doc-url": "https://wot-design-uni.cn/component/slider.html#attributes", "type": ["number"], "default": "100"}, {"name": "min", "description": "最小值，允许负数`(1.2.19)`", "doc-url": "https://wot-design-uni.cn/component/slider.html#attributes", "type": ["number"], "default": "0"}, {"name": "step", "description": "步进值", "doc-url": "https://wot-design-uni.cn/component/slider.html#attributes", "type": ["number"], "default": "1"}, {"name": "active-color", "description": "进度条激活背景颜色", "doc-url": "https://wot-design-uni.cn/component/slider.html#attributes", "type": ["string"], "default": "linear-gradient(315deg, rgba(81,124,240,1) 0%,rgba(118,158,245,1) 100%)"}, {"name": "inactive-color", "description": "进度条未激活背景颜色", "doc-url": "https://wot-design-uni.cn/component/slider.html#attributes", "type": ["string"], "default": "#e5e5e5"}], "js": {"events": [{"name": "dragstart", "description": "开始移动时触发", "doc-url": "https://wot-design-uni.cn/component/slider.html#events"}, {"name": "dragmove", "description": "移动滑块时触发", "doc-url": "https://wot-design-uni.cn/component/slider.html#events"}, {"name": "dragend", "description": "移动结束时触发", "doc-url": "https://wot-design-uni.cn/component/slider.html#events"}]}}, {"name": "wd-sort-button", "source": {"symbol": "WdSortButton"}, "description": "用于展示排序按钮，支持升序、降序、重置三种状态。", "doc-url": "https://wot-design-uni.cn/component/sort-button.html#sortbutton", "props": [{"name": "v-model", "description": "选中的箭头方向：1 升序，0 重置状态，-1 降序。", "doc-url": "https://wot-design-uni.cn/component/sort-button.html#attributes", "type": ["number"], "default": "0或-1", "attribute-value": {"type": "of-match"}, "values": [{"name": "-1,0,1"}]}, {"name": "title", "description": "排序按钮展示的文案。", "doc-url": "https://wot-design-uni.cn/component/sort-button.html#attributes", "type": ["string"]}, {"name": "allow-reset", "description": "展示双箭头时，允许手动重置按钮。", "doc-url": "https://wot-design-uni.cn/component/sort-button.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "desc-first", "description": "优先切换为降序，不开启则默认优先切换为升序", "doc-url": "https://wot-design-uni.cn/component/sort-button.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "line", "description": "展示下划线，当只有一个排序按钮时，应该不展示下划线", "doc-url": "https://wot-design-uni.cn/component/sort-button.html#attributes", "type": ["boolean"], "default": "true"}], "js": {"events": [{"name": "change", "description": "监听排序修改", "doc-url": "https://wot-design-uni.cn/component/sort-button.html#events"}]}}, {"name": "wd-status-tip", "source": {"symbol": "WdStatusTip"}, "description": "一般用于兜底占位展示。", "doc-url": "https://wot-design-uni.cn/component/status-tip.html#statustip", "props": [{"name": "image", "description": "缺省图片类型，支持传入图片 URL", "doc-url": "https://wot-design-uni.cn/component/status-tip.html#attributes", "type": ["string"], "default": "network", "attribute-value": {"type": "enum"}, "values": [{"name": "search"}, {"name": "network"}, {"name": "content"}, {"name": "collect"}, {"name": "comment"}, {"name": "halo"}, {"name": "message"}]}, {"name": "image-size", "description": "图片大小，默认单位为 `px`", "doc-url": "https://wot-design-uni.cn/component/status-tip.html#attributes", "type": ["string", "number", {"name": "ImageSize", "source": {"symbol": "ImageSize"}}]}, {"name": "tip", "description": "提示文案", "doc-url": "https://wot-design-uni.cn/component/status-tip.html#attributes", "type": ["string"]}, {"name": "image-mode", "description": "预览图片的 mode 属性", "doc-url": "https://wot-design-uni.cn/component/status-tip.html#attributes", "type": [{"name": "ImageMode", "source": {"symbol": "ImageMode"}}], "default": "aspectFit"}, {"name": "url-prefix", "description": "图片路径前缀，指向图片所在目录，用于拼接图片 URL。", "doc-url": "https://wot-design-uni.cn/component/status-tip.html#attributes", "type": ["string"], "default": "https://registry.npmmirror.com/wot-design-uni-assets//files/"}]}, {"name": "wd-steps", "source": {"symbol": "WdSteps"}, "description": "用于引导用户按照流程完成任务或向用户展示当前状态。", "doc-url": "https://wot-design-uni.cn/component/steps.html#steps", "props": [{"name": "active", "description": "步骤进度", "doc-url": "https://wot-design-uni.cn/component/steps.html#steps-attributes", "type": ["number"], "default": "0"}, {"name": "vertical", "description": "垂直方向", "doc-url": "https://wot-design-uni.cn/component/steps.html#steps-attributes", "type": ["boolean"], "default": "false"}, {"name": "dot", "description": "点状步骤条", "doc-url": "https://wot-design-uni.cn/component/steps.html#steps-attributes", "type": [{"name": "dot", "source": {"symbol": "dot"}}], "default": "false"}, {"name": "space", "description": "步骤条间距，默认为自动计算", "doc-url": "https://wot-design-uni.cn/component/steps.html#steps-attributes", "type": ["string"]}, {"name": "align-center", "description": "是否水平居中，只对横向步骤条有效", "doc-url": "https://wot-design-uni.cn/component/steps.html#steps-attributes", "type": ["boolean"], "default": "false"}]}, {"name": "wd-step", "source": {"symbol": "WdStep"}, "description": "用于引导用户按照流程完成任务或向用户展示当前状态。", "doc-url": "https://wot-design-uni.cn/component/steps.html#step", "props": [{"name": "title", "description": "标题，如果没有则为默认文案。当只有标题而没有描述时，标题的字号会小2号", "doc-url": "https://wot-design-uni.cn/component/steps.html#step-attributes", "type": ["string"]}, {"name": "stitle-slots", "description": "<s> 使用 title 插槽时需要设置该属性</s>，已废弃，直接使用title插槽即可", "doc-url": "https://wot-design-uni.cn/component/steps.html#step-attributes", "type": ["boolean"], "default": "false"}, {"name": "description", "description": "描述", "doc-url": "https://wot-design-uni.cn/component/steps.html#step-attributes", "type": ["string"]}, {"name": "sdescription-slots", "description": "<s>使用 description 插槽时需要设置该属性</s>，已废弃，直接使用description插槽即可", "doc-url": "https://wot-design-uni.cn/component/steps.html#step-attributes", "type": ["boolean"], "default": "false"}, {"name": "icon", "description": "图标", "doc-url": "https://wot-design-uni.cn/component/steps.html#step-attributes", "type": ["string"]}, {"name": "sicon-slots", "description": "<s>使用 icon 插槽时需要设置该属性</s>，已废弃，直接使用icon插槽即可", "doc-url": "https://wot-design-uni.cn/component/steps.html#step-attributes", "type": ["boolean"], "default": "false"}, {"name": "status", "description": "步骤状态", "doc-url": "https://wot-design-uni.cn/component/steps.html#step-attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "finished"}, {"name": "process"}, {"name": "error"}]}]}, {"name": "wd-sticky", "source": {"symbol": "WdSticky"}, "description": "粘性布局组件，用于在页面滚动时将元素固定在指定位置。", "doc-url": "https://wot-design-uni.cn/component/sticky.html#sticky", "props": [{"name": "z-index", "description": "堆叠顺序", "doc-url": "https://wot-design-uni.cn/component/sticky.html#sticky-attributes", "type": ["number"], "default": "1"}, {"name": "offset-top", "description": "吸顶距离", "doc-url": "https://wot-design-uni.cn/component/sticky.html#sticky-attributes", "type": ["number"], "default": "0"}]}, {"name": "wd-swipe-action", "source": {"symbol": "WdSwipeAction"}, "description": "常用于单元格左右滑删除等手势操作。", "doc-url": "https://wot-design-uni.cn/component/swipe-action.html#swipeaction", "props": [{"name": "v-model", "description": "滑动按钮的状态", "doc-url": "https://wot-design-uni.cn/component/swipe-action.html#attributes", "type": ["string"], "default": "close", "attribute-value": {"type": "enum"}, "values": [{"name": "left"}, {"name": "close"}, {"name": "right"}]}, {"name": "disabled", "description": "是否禁用滑动操作", "doc-url": "https://wot-design-uni.cn/component/swipe-action.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "before-close", "description": "关闭滑动按钮前的钩子函数", "doc-url": "https://wot-design-uni.cn/component/swipe-action.html#attributes", "type": ["function"]}], "js": {"events": [{"name": "click", "description": "当滑动按钮打开时，点击整个滑动操作容器触发 click 事件", "doc-url": "https://wot-design-uni.cn/component/swipe-action.html#events"}]}}, {"name": "wd-swiper", "source": {"symbol": "WdSwiper"}, "description": "用于创建轮播，它支持水平和垂直方向的滑动，可以自定义样式和指示器位置，支持视频和图片资源的轮播，支持设置轮播标题和自定义标题样式。", "doc-url": "https://wot-design-uni.cn/component/swiper.html#swiper", "props": [{"name": "autoplay", "description": "是否自动播放", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "v-model:current", "description": "控制当前轮播在哪一项（下标）", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": ["number"], "default": "0"}, {"name": "direction", "description": "轮播滑动方向", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": [{"name": "DirectionType", "source": {"symbol": "DirectionType"}}], "default": "horizontal", "attribute-value": {"type": "of-match"}, "values": [{"name": "horizontal, vertical"}]}, {"name": "display-multiple-items", "description": "同时显示的滑块数量", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": ["number"], "default": "1"}, {"name": "duration", "description": "滑动动画时长", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": ["number"], "default": "300"}, {"name": "easing-function", "description": "切换缓动动画类型（微信小程序、快手小程序、京东小程序）", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": [{"name": "EasingType", "source": {"symbol": "EasingType"}}], "default": "default"}, {"name": "height", "description": "轮播的高度", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": ["string", "number"], "default": "192"}, {"name": "interval", "description": "轮播间隔时间", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": ["number"], "default": "5000"}, {"name": "list", "description": "图片列表", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": ["string[]", {"name": "SwiperList[]", "source": {"symbol": "SwiperList"}}]}, {"name": "loop", "description": "是否循环播放", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "next-margin", "description": "后边距", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": ["string", "number"], "default": "0"}, {"name": "indicator-position", "description": "指示器展示位置", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": [{"name": "IndicatorPositionType", "source": {"symbol": "IndicatorPositionType"}}], "default": "bottom", "attribute-value": {"type": "of-match"}, "values": [{"name": "left, top-left, top, top-right, bottom-left, bottom, bottom-right, right"}]}, {"name": "previous-margin", "description": "前边距", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": ["string", "number"], "default": "0"}, {"name": "snap-to-edge", "description": "边距是否应用到第一个、最后一个元素", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "indicator", "description": "指示器全部配置", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": [{"name": "SwiperIndicatorProps", "source": {"symbol": "SwiperIndicatorProps"}}, "boolean"], "default": "true"}, {"name": "image-mode", "description": "图片裁剪、缩放的模式", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": ["string"], "default": "aspectFill", "attribute-value": {"type": "of-match"}, "values": [{"name": "参考官方文档[mode](https:"}, {"name": ""}, {"name": "uniapp.dcloud.net.cn"}, {"name": "component"}, {"name": "image.html#mode-%E6%9C%89%E6%95%88%E5%80%BC)"}]}, {"name": "autoplay-video", "description": "视频是否自动播放，默认自动播放", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "stop-previous-video", "description": "切换轮播项时是否停止上一个视频的播放，默认切换时停止播放上一个视频", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "stop-autoplay-when-video-play", "description": "视频播放时是否停止自动轮播", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "custom-style", "description": "外部自定义样式", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": ["string"]}, {"name": "value-key", "description": "选项对象中，value 对应的 key", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": ["string"], "default": "value"}, {"name": "text-key", "description": "选项对象中，标题 text 对应的 key", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": ["string"], "default": "text"}, {"name": "adjust-height", "description": "自动以指定滑块的高度为整个容器的高度。当 vertical 为 true 时，默认不调整，仅支付宝小程序支持。", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": ["string"], "default": "highest", "attribute-value": {"type": "of-match"}, "values": [{"name": "first'"}, {"name": "'current'"}, {"name": "'highest'"}, {"name": "'none"}]}, {"name": "adjust-vertical-height", "description": "vertical 为 true 时强制使 adjust-height 生效。仅支付宝小程序支持。", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": ["boolean"], "default": "false"}, {"name": " muted", "description": "视频是否静音播放", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "video-loop", "description": "视频是否循环播放", "doc-url": "https://wot-design-uni.cn/component/swiper.html#attributes", "type": ["boolean"], "default": "true"}], "js": {"events": [{"name": "click", "description": "点击轮播项时触发", "doc-url": "https://wot-design-uni.cn/component/swiper.html#events"}, {"name": "change", "description": "轮播切换时触发", "doc-url": "https://wot-design-uni.cn/component/swiper.html#events"}]}}, {"name": "wd-switch", "source": {"symbol": "WdSwitch"}, "description": "用来打开或关闭选项。", "doc-url": "https://wot-design-uni.cn/component/switch.html#switch", "props": [{"name": "v-model", "description": "绑定值", "doc-url": "https://wot-design-uni.cn/component/switch.html#attributes", "type": ["boolean", "string", "number"]}, {"name": "disabled", "description": "禁用", "doc-url": "https://wot-design-uni.cn/component/switch.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "active-value", "description": "打开时的值", "doc-url": "https://wot-design-uni.cn/component/switch.html#attributes", "type": ["boolean", "string", "number"], "default": "true"}, {"name": "inactive-value", "description": "关闭时的值", "doc-url": "https://wot-design-uni.cn/component/switch.html#attributes", "type": ["boolean", "string", "number"], "default": "false"}, {"name": "active-color", "description": "打开时的背景色", "doc-url": "https://wot-design-uni.cn/component/switch.html#attributes", "type": ["string"], "default": "#4D80F0"}, {"name": "inactive-color", "description": "关闭时的背景色，默认为白色，所以有灰色边框，如果设置了该值，则会自动去除灰色边框", "doc-url": "https://wot-design-uni.cn/component/switch.html#attributes", "type": ["string"], "default": "#fff"}, {"name": "size", "description": "开关大小，可以为任何单位的字符串尺寸", "doc-url": "https://wot-design-uni.cn/component/switch.html#attributes", "type": ["string", "number"], "default": "28px"}, {"name": "before-change", "description": "修改前钩子", "doc-url": "https://wot-design-uni.cn/component/switch.html#attributes", "type": ["function"]}], "js": {"events": [{"name": "change", "description": "值修改事件", "doc-url": "https://wot-design-uni.cn/component/switch.html#events"}]}}, {"name": "wd-tabbar-item", "source": {"symbol": "WdTabbarItem"}, "description": "底部导航栏，用于在不同页面之间进行切换。", "doc-url": "https://wot-design-uni.cn/component/tabbar.html#tabbaritem", "props": [{"name": "title", "description": "标签页的标题", "doc-url": "https://wot-design-uni.cn/component/tabbar.html#tabbaritem-attributes", "type": ["string"]}, {"name": "name", "description": "唯一标识符", "doc-url": "https://wot-design-uni.cn/component/tabbar.html#tabbaritem-attributes", "type": ["string", "number"]}, {"name": "icon", "description": "图标", "doc-url": "https://wot-design-uni.cn/component/tabbar.html#tabbaritem-attributes", "type": ["string"]}, {"name": "value", "description": "徽标显示值", "doc-url": "https://wot-design-uni.cn/component/tabbar.html#tabbaritem-attributes", "type": ["number", "string"]}, {"name": "is-dot", "description": "是否点状徽标", "doc-url": "https://wot-design-uni.cn/component/tabbar.html#tabbaritem-attributes", "type": ["boolean"], "default": "false"}, {"name": "max", "description": "徽标最大值", "doc-url": "https://wot-design-uni.cn/component/tabbar.html#tabbaritem-attributes", "type": ["number"], "default": "99"}, {"name": "badge-props", "description": "自定义徽标的属性，传入的对象会被透传给 [Badge 组件的 props](/component/badge#attributes)", "doc-url": "https://wot-design-uni.cn/component/tabbar.html#tabbaritem-attributes", "type": [{"name": "BadgeProps", "source": {"symbol": "BadgeProps"}}]}]}, {"name": "wd-tabbar", "source": {"symbol": "WdTabbar"}, "description": "底部导航栏，用于在不同页面之间进行切换。", "doc-url": "https://wot-design-uni.cn/component/tabbar.html#tabbar", "props": [{"name": "v-model", "description": "选中标签的索引值或者名称", "doc-url": "https://wot-design-uni.cn/component/tabbar.html#attributes", "type": ["number", "string"], "default": "0"}, {"name": "fixed", "description": "是否固定在底部", "doc-url": "https://wot-design-uni.cn/component/tabbar.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "safe-area-inset-bottom", "description": "是否设置底部安全距离（iPhone X 类型的机型）", "doc-url": "https://wot-design-uni.cn/component/tabbar.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "bordered", "description": "是否显示顶部边框", "doc-url": "https://wot-design-uni.cn/component/tabbar.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "shape", "description": "标签栏的形状", "doc-url": "https://wot-design-uni.cn/component/tabbar.html#attributes", "type": [{"name": "<PERSON><PERSON>r<PERSON>ha<PERSON>", "source": {"symbol": "<PERSON><PERSON>r<PERSON>ha<PERSON>"}}], "default": "default", "attribute-value": {"type": "of-match"}, "values": [{"name": "default'"}, {"name": "'round"}]}, {"name": "active-color", "description": "激活标签的颜色", "doc-url": "https://wot-design-uni.cn/component/tabbar.html#attributes", "type": ["string"]}, {"name": "inactive-color", "description": "未激活标签的颜色", "doc-url": "https://wot-design-uni.cn/component/tabbar.html#attributes", "type": ["string"]}, {"name": "placeholder", "description": "固定在底部时，是否在标签位置生成一个等高的占位元素", "doc-url": "https://wot-design-uni.cn/component/tabbar.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "z-index", "description": "tabbar组件的层级", "doc-url": "https://wot-design-uni.cn/component/tabbar.html#attributes", "type": ["number"], "default": "500"}], "js": {"events": [{"name": "change", "description": "tabbar标签切换时触发", "doc-url": "https://wot-design-uni.cn/component/tabbar.html#events"}]}}, {"name": "wd-table-column", "source": {"symbol": "WdTableColumn"}, "description": "用于展示多条结构类似的数据， 可对数据进行排序等操作。", "doc-url": "https://wot-design-uni.cn/component/table.html#tablecolumn", "props": [{"name": "prop", "description": "字段名称,对应列内容的字段名", "doc-url": "https://wot-design-uni.cn/component/table.html#tablecolumn-attributes", "type": ["string"]}, {"name": "label", "description": "显示的标题", "doc-url": "https://wot-design-uni.cn/component/table.html#tablecolumn-attributes", "type": ["string"]}, {"name": "width", "description": "对应列的宽度，单位为 px", "doc-url": "https://wot-design-uni.cn/component/table.html#tablecolumn-attributes", "type": ["number", "string"], "default": "100"}, {"name": "sortable", "description": "是否开启列排序", "doc-url": "https://wot-design-uni.cn/component/table.html#tablecolumn-attributes", "type": ["boolean"], "default": "false"}, {"name": "fixed", "description": "是否固定本列", "doc-url": "https://wot-design-uni.cn/component/table.html#tablecolumn-attributes", "type": ["boolean"], "default": "false"}, {"name": "align", "description": "列的对齐方式", "doc-url": "https://wot-design-uni.cn/component/table.html#tablecolumn-attributes", "type": [{"name": "AlignType", "source": {"symbol": "AlignType"}}], "default": "left", "attribute-value": {"type": "of-match"}, "values": [{"name": "left, center, right"}]}]}, {"name": "wd-table", "source": {"symbol": "WdTable"}, "description": "用于展示多条结构类似的数据， 可对数据进行排序等操作。", "doc-url": "https://wot-design-uni.cn/component/table.html#table", "props": [{"name": "data", "description": "显示的数据", "doc-url": "https://wot-design-uni.cn/component/table.html#attributes", "type": ["Array"]}, {"name": "border", "description": "是否带有边框", "doc-url": "https://wot-design-uni.cn/component/table.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "stripe", "description": "是否为斑马纹表", "doc-url": "https://wot-design-uni.cn/component/table.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "height", "description": "Table 的高度，无默认值，设置后自动开启固定表头。", "doc-url": "https://wot-design-uni.cn/component/table.html#attributes", "type": ["number", "string"]}, {"name": "row-height", "description": "行高", "doc-url": "https://wot-design-uni.cn/component/table.html#attributes", "type": ["number", "string"], "default": "50"}, {"name": "show-header", "description": "是否显示表头", "doc-url": "https://wot-design-uni.cn/component/table.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "ellipsis", "description": "是否超出 2 行隐藏", "doc-url": "https://wot-design-uni.cn/component/table.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "index", "description": "是否显示索引列，可传入`boolean`也可传入 column 配置", "doc-url": "https://wot-design-uni.cn/component/table.html#attributes", "type": ["boolean", {"name": "TableColumnProps", "source": {"symbol": "TableColumnProps"}}], "default": "false"}, {"name": "fixed-header", "description": "是否固定表头，需要结合`height`才可以实现固定表头的效果。", "doc-url": "https://wot-design-uni.cn/component/table.html#attributes", "type": ["boolean"], "default": "true"}], "js": {"events": [{"name": "sort-method", "description": "指定数据按照哪个属性进行排序，仅当 sortable 设置为 true 的时候有效", "doc-url": "https://wot-design-uni.cn/component/table.html#events"}, {"name": "row-click", "description": "当某一行被点击时会触发该事件", "doc-url": "https://wot-design-uni.cn/component/table.html#events"}]}}, {"name": "wd-tabs", "source": {"symbol": "WdTabs"}, "description": "标签页组件，用于在不同的内容区域之间进行切换。", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tabs", "props": [{"name": "v-model", "description": "绑定值", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tabs-attributes", "type": ["string", "number"]}, {"name": "slidable-num", "description": "可滑动的标签数阈值，`slidable`设置为`auto`时生效", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tabs-attributes", "type": ["number"], "default": "6"}, {"name": "map-num", "description": "显示导航地图的标签数阈值", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tabs-attributes", "type": ["number"], "default": "10"}, {"name": "map-title", "description": "导航地图标题", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tabs-attributes", "type": ["string"]}, {"name": "sticky", "description": "粘性布局", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tabs-attributes", "type": ["boolean"], "default": "false"}, {"name": "offset-top", "description": "粘性布局时距离窗口顶部距离", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tabs-attributes", "type": ["number"], "default": "0"}, {"name": "swipeable", "description": "开启手势滑动", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tabs-attributes", "type": ["boolean"], "default": "false"}, {"name": "auto-line-width", "description": "底部条宽度跟随文字，指定`lineWidth`时此选项不生效", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tabs-attributes", "type": ["boolean"], "default": "false"}, {"name": "line-width", "description": "底部条宽度，单位像素", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tabs-attributes", "type": ["number"], "default": "19"}, {"name": "line-height", "description": "底部条高度，单位像素", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tabs-attributes", "type": ["number"], "default": "3"}, {"name": "color", "description": "文字颜色", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tabs-attributes", "type": ["string"]}, {"name": "inactive-color", "description": "非活动标签文字颜色", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tabs-attributes", "type": ["string"]}, {"name": "animated", "description": "是否开启切换标签内容时的转场动画", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tabs-attributes", "type": ["boolean"], "default": "false"}, {"name": "duration", "description": "切换动画过渡时间，单位毫秒", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tabs-attributes", "type": ["number"], "default": "300"}, {"name": "slidable", "description": "是否开启滚动导航", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tabs-attributes", "type": [{"name": "TabsSlidable", "source": {"symbol": "TabsSlidable"}}], "default": "auto", "attribute-value": {"type": "of-match"}, "values": [{"name": "always"}]}, {"name": "badge-props", "description": "自定义徽标的属性，传入的对象会被透传给 [Badge 组件的 props](/component/badge#attributes)", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tabs-attributes", "type": [{"name": "BadgeProps", "source": {"symbol": "BadgeProps"}}]}], "js": {"events": [{"name": "change", "description": "绑定值变化时触发", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tabs-events"}, {"name": "click", "description": "点击标题时触发", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tabs-events"}, {"name": "disabled", "description": "点击禁用的标题时触发", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tabs-events"}]}}, {"name": "wd-tab", "source": {"symbol": "WdTab"}, "description": "标签页组件，用于在不同的内容区域之间进行切换。", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tab", "props": [{"name": "name", "description": "标签页名称", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tab-attributes", "type": ["string"]}, {"name": "title", "description": "标题", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tab-attributes", "type": ["string"]}, {"name": "disabled", "description": "禁用", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tab-attributes", "type": ["boolean"], "default": "false"}, {"name": "lazy", "description": "延迟渲染，默认开启，开启`animated`后此选项始终为`false`", "doc-url": "https://wot-design-uni.cn/component/tabs.html#tab-attributes", "type": ["boolean"], "default": "true"}]}, {"name": "wd-tag", "source": {"symbol": "WdTag"}, "description": "用于标记状态或者概括主要内容。", "doc-url": "https://wot-design-uni.cn/component/tag.html#tag", "props": [{"name": "type", "description": "标签类型", "doc-url": "https://wot-design-uni.cn/component/tag.html#attributes", "type": ["string"], "default": "default", "attribute-value": {"type": "enum"}, "values": [{"name": "default"}, {"name": "primary"}, {"name": "danger"}, {"name": "warning"}, {"name": "success"}]}, {"name": "plain", "description": "幽灵类型", "doc-url": "https://wot-design-uni.cn/component/tag.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "mark", "description": "标记类型", "doc-url": "https://wot-design-uni.cn/component/tag.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "round", "description": "圆角类型", "doc-url": "https://wot-design-uni.cn/component/tag.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "icon", "description": "左侧图标", "doc-url": "https://wot-design-uni.cn/component/tag.html#attributes", "type": ["string"]}, {"name": "color", "description": "文字颜色", "doc-url": "https://wot-design-uni.cn/component/tag.html#attributes", "type": ["string"]}, {"name": "bg-color", "description": "背景色和边框色", "doc-url": "https://wot-design-uni.cn/component/tag.html#attributes", "type": ["string"]}, {"name": "closable", "description": "可关闭(只对圆角类型支持)", "doc-url": "https://wot-design-uni.cn/component/tag.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "use-icon-slot", "description": "开启图标插槽", "doc-url": "https://wot-design-uni.cn/component/tag.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "dynamic", "description": "是否为新增标签", "doc-url": "https://wot-design-uni.cn/component/tag.html#attributes", "type": ["boolean"], "default": "false"}], "js": {"events": [{"name": "click", "description": "标签点击时触发", "doc-url": "https://wot-design-uni.cn/component/tag.html#events"}, {"name": "close", "description": "点击关闭按钮时触发", "doc-url": "https://wot-design-uni.cn/component/tag.html#events"}, {"name": "confirm", "description": "新增标签输入内容确定后触发", "doc-url": "https://wot-design-uni.cn/component/tag.html#events"}]}}, {"name": "wd-text", "source": {"symbol": "WdText"}, "description": "文本组件，用于展示文本信息。", "doc-url": "https://wot-design-uni.cn/component/text.html#text", "props": [{"name": "type", "description": "主题类型", "doc-url": "https://wot-design-uni.cn/component/text.html#attributes", "type": ["string"], "default": "default", "attribute-value": {"type": "enum"}, "values": [{"name": "primary'"}, {"name": "'error'"}, {"name": "'warning'"}, {"name": "'success"}]}, {"name": "text", "description": "文字", "doc-url": "https://wot-design-uni.cn/component/text.html#attributes", "type": ["string", "number"]}, {"name": "size", "description": "字体大小", "doc-url": "https://wot-design-uni.cn/component/text.html#attributes", "type": ["string"]}, {"name": "mode", "description": "文本处理的匹配模式", "doc-url": "https://wot-design-uni.cn/component/text.html#attributes", "type": ["string"], "default": "text", "attribute-value": {"type": "enum"}, "values": [{"name": "text-普通文本'"}, {"name": "'date - 日期'"}, {"name": "'phone - 手机号'"}, {"name": "'name - 姓名'"}, {"name": "'price - 金额"}]}, {"name": "bold", "description": "是否粗体，默认 normal", "doc-url": "https://wot-design-uni.cn/component/text.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "format", "description": "是否脱敏", "doc-url": "https://wot-design-uni.cn/component/text.html#attributes", "type": ["boolean"], "default": "false", "attribute-value": {"type": "of-match"}, "values": [{"name": "当 mode 为 phone 和 name 时生效"}]}, {"name": "color", "description": "文字颜色", "doc-url": "https://wot-design-uni.cn/component/text.html#attributes", "type": ["string"]}, {"name": "lines", "description": "文本显示的行数，如果设置，超出此行数，将会显示省略号。最大值为 5。", "doc-url": "https://wot-design-uni.cn/component/text.html#attributes", "type": ["Number"]}, {"name": "line-height", "description": "文本行高", "doc-url": "https://wot-design-uni.cn/component/text.html#attributes", "type": ["string"]}, {"name": "decoration", "description": "文字装饰，下划线，中划线等", "doc-url": "https://wot-design-uni.cn/component/text.html#attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "underline"}, {"name": "line-through"}, {"name": "overline"}]}, {"name": "prefix", "description": "前置插槽", "doc-url": "https://wot-design-uni.cn/component/text.html#attributes", "type": ["string"]}, {"name": "suffix", "description": "后置插槽", "doc-url": "https://wot-design-uni.cn/component/text.html#attributes", "type": ["string"]}], "js": {"events": [{"name": "click", "description": "标签点击时触发", "doc-url": "https://wot-design-uni.cn/component/text.html#events"}]}}, {"name": "wd-textarea", "source": {"symbol": "WdTextarea"}, "description": "用于输入多行文本信息。", "doc-url": "https://wot-design-uni.cn/component/textarea.html#textarea", "props": [{"name": "v-model", "description": "绑定值", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["string", "number"]}, {"name": "placeholder", "description": "占位文本", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["string"], "default": "请输入..."}, {"name": "placeholder-style", "description": "原生属性，指定 placeholder 的样式", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["string"]}, {"name": "placeholder-class", "description": "原生属性，指定 placeholder 的样式类", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["string"]}, {"name": "disabled", "description": "原生属性，禁用", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "maxlength", "description": "原生属性，最大输入长度，设置为 -1 时不限制最大长度", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["number"]}, {"name": "auto-focus", "description": "原生属性，自动聚焦，拉起键盘", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "focus", "description": "原生属性，获取焦点", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "auto-height", "description": "原生属性，是否自动增高（设置时 style.height 不生效）", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "fixed", "description": "在 position:fixed 区域时需要设置为 true", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "cursor-spacing", "description": "原生属性，指定光标与键盘的距离（取 textarea 底部距离和该值的最小值）", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["number"], "default": "0"}, {"name": "cursor", "description": "原生属性，指定 focus 时的光标位置", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["number"], "default": "-1"}, {"name": "confirm-type", "description": "设置键盘右下角按钮的文字", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "done"}, {"name": "go"}, {"name": "next"}, {"name": "search"}, {"name": "send"}]}, {"name": "confirm-hold", "description": "点击键盘右下角按钮时是否保持键盘不收起", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "show-confirm-bar", "description": "是否显示键盘上方\"完成\"栏", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "selection-start", "description": "原生属性，光标起始位置（需与 selection-end 搭配使用）", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["number"], "default": "-1"}, {"name": "selection-end", "description": "原生属性，光标结束位置（需与 selection-start 搭配使用）", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["number"], "default": "-1"}, {"name": "adjust-position", "description": "原生属性，键盘弹起时是否自动上推页面", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "disable-default-padding", "description": "原生属性，是否去掉 iOS 默认内边距", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "hold-keyboard", "description": "原生属性，focus 时点击页面不收起键盘", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "show-password", "description": "显示为密码框", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "clearable", "description": "显示清空按钮", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "readonly", "description": "只读", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "prefix-icon", "description": "前置图标（使用 icon 组件类名）", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["string"]}, {"name": "show-word-limit", "description": "显示字数限制（需设置 maxlength）", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "label", "description": "设置左侧标题", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["string"]}, {"name": "label-width", "description": "设置左侧标题宽度", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["string"], "default": "33%"}, {"name": "size", "description": "设置输入框大小", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["string"]}, {"name": "error", "description": "设置输入框错误状态（红色标识）", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "center", "description": "有 label 时设置标题和输入框垂直居中（默认顶部居中）", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "no-border", "description": "非 cell 类型下是否隐藏下划线", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "required", "description": "cell 类型下必填样式", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "prop", "description": "表单域 `model` 字段名（表单校验必填）", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["string"]}, {"name": "rules", "description": "表单验证规则", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": [{"name": "FormItemRule[]", "source": {"symbol": "FormItemRule"}}], "default": "[]"}, {"name": "clear-trigger", "description": "显示清除图标的时机：always（输入框非空时展示）/ focus（聚焦且非空时展示）", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": [{"name": "InputClearTrigger", "source": {"symbol": "InputClearTrigger"}}], "default": "always", "attribute-value": {"type": "of-match"}, "values": [{"name": "focus"}, {"name": "always"}]}, {"name": "focus-when-clear", "description": "点击清除按钮时是否聚焦输入框", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "ignore-composition-event", "description": "是否忽略文本合成系统事件处理（为 false 时触发 composition 相关事件，且在合成期间触发 input 事件）", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "inputmode", "description": "输入数据类型提示", "doc-url": "https://wot-design-uni.cn/component/textarea.html#attributes", "type": [{"name": "InputMode", "source": {"symbol": "InputMode"}}], "default": "text"}], "js": {"events": [{"name": "input", "description": "监听输入框 input 事件", "doc-url": "https://wot-design-uni.cn/component/textarea.html#events"}, {"name": "focus", "description": "监听输入框 focus 事件", "doc-url": "https://wot-design-uni.cn/component/textarea.html#events"}, {"name": "blur", "description": "监听输入框 blur 事件", "doc-url": "https://wot-design-uni.cn/component/textarea.html#events"}, {"name": "clear", "description": "监听输入框清空按钮事件", "doc-url": "https://wot-design-uni.cn/component/textarea.html#events"}, {"name": "linechange", "description": "监听输入框行数变化", "doc-url": "https://wot-design-uni.cn/component/textarea.html#events"}, {"name": "confirm", "description": "点击完成时， 触发 confirm 事件", "doc-url": "https://wot-design-uni.cn/component/textarea.html#events"}, {"name": "keyboardheightchange", "description": "键盘高度发生变化的时候触发此事件", "doc-url": "https://wot-design-uni.cn/component/textarea.html#events"}, {"name": "clickprefixicon", "description": "点击前置图标时触发", "doc-url": "https://wot-design-uni.cn/component/textarea.html#events"}, {"name": "clicksuffixicon", "description": "点击后置图标时触发", "doc-url": "https://wot-design-uni.cn/component/textarea.html#events"}]}}, {"name": "wd-toast", "source": {"symbol": "WdToast"}, "description": "轻提示组件，用于消息通知、加载提示、操作结果提示等场景，支持函数式调用。", "doc-url": "https://wot-design-uni.cn/component/toast.html#toast", "props": [{"name": "selector", "description": "选择器", "doc-url": "https://wot-design-uni.cn/component/toast.html#attributes", "type": ["string"]}, {"name": "msg", "description": "提示信息", "doc-url": "https://wot-design-uni.cn/component/toast.html#attributes", "type": ["string"]}, {"name": "direction", "description": "排列方向", "doc-url": "https://wot-design-uni.cn/component/toast.html#attributes", "type": ["string"], "default": "horizontal", "attribute-value": {"type": "enum"}, "values": [{"name": "vertical"}, {"name": "horizontal"}]}, {"name": "icon-name", "description": "图标类型", "doc-url": "https://wot-design-uni.cn/component/toast.html#attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "success"}, {"name": "error"}, {"name": "warning"}, {"name": "loading"}, {"name": "info"}]}, {"name": "icon-size", "description": "图标大小", "doc-url": "https://wot-design-uni.cn/component/toast.html#attributes", "type": ["number"]}, {"name": "loading-type", "description": "加载类型", "doc-url": "https://wot-design-uni.cn/component/toast.html#attributes", "type": ["string"], "default": "outline", "attribute-value": {"type": "enum"}, "values": [{"name": "outline"}, {"name": "ring"}]}, {"name": "loading-color", "description": "加载颜色", "doc-url": "https://wot-design-uni.cn/component/toast.html#attributes", "type": ["string"], "default": "#4D80F0"}, {"name": "loading-size", "description": "加载大小", "doc-url": "https://wot-design-uni.cn/component/toast.html#attributes", "type": ["number"]}, {"name": "icon-color", "description": "图标颜色", "doc-url": "https://wot-design-uni.cn/component/toast.html#attributes", "type": ["string"]}, {"name": "position", "description": "提示信息框的位置", "doc-url": "https://wot-design-uni.cn/component/toast.html#attributes", "type": ["string"], "default": "middle-top", "attribute-value": {"type": "enum"}, "values": [{"name": "top"}, {"name": "middle-top"}, {"name": "middle"}, {"name": "bottom"}]}, {"name": "z-index", "description": "层级", "doc-url": "https://wot-design-uni.cn/component/toast.html#attributes", "type": ["number"], "default": "100"}, {"name": "cover", "description": "是否存在遮罩层", "doc-url": "https://wot-design-uni.cn/component/toast.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "icon-class", "description": "图标类名", "doc-url": "https://wot-design-uni.cn/component/toast.html#attributes", "type": ["string"]}, {"name": "class-prefix", "description": "类名前缀，用于使用自定义图标", "doc-url": "https://wot-design-uni.cn/component/toast.html#attributes", "type": ["string"], "default": "wd-icon"}, {"name": "opened", "description": "完全展示后的回调函数", "doc-url": "https://wot-design-uni.cn/component/toast.html#attributes", "type": ["Function"]}, {"name": "closed", "description": "完全关闭时的回调函数", "doc-url": "https://wot-design-uni.cn/component/toast.html#attributes", "type": ["Function"]}]}, {"name": "wd-tooltip", "source": {"symbol": "WdTooltip"}, "description": "常用于展示提示信息。", "doc-url": "https://wot-design-uni.cn/component/tooltip.html#tooltip", "props": [{"name": "show", "description": "状态是否可见", "doc-url": "https://wot-design-uni.cn/component/tooltip.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "content", "description": "显示的内容，也可以通过 `slot#content` 传入", "doc-url": "https://wot-design-uni.cn/component/tooltip.html#attributes", "type": ["string", "array"]}, {"name": "placement", "description": "Tooltip 的出现位置", "doc-url": "https://wot-design-uni.cn/component/tooltip.html#attributes", "type": ["string"], "default": "bottom", "attribute-value": {"type": "enum"}, "values": [{"name": "top"}, {"name": "top-start"}, {"name": "top-end"}, {"name": "bottom"}, {"name": "bottom-start"}, {"name": "bottom-end"}, {"name": "left"}, {"name": "left-start"}, {"name": "left-end"}, {"name": "right"}, {"name": "right-start"}, {"name": "right-end"}]}, {"name": "disabled", "description": "Tooltip 是否可用", "doc-url": "https://wot-design-uni.cn/component/tooltip.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "visible-arrow", "description": "是否显示 Tooltip 箭头", "doc-url": "https://wot-design-uni.cn/component/tooltip.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "offset", "description": "出现位置的偏移量", "doc-url": "https://wot-design-uni.cn/component/tooltip.html#attributes", "type": ["number", "number[]"], "default": "{x:0, y:0}"}, {"name": "show-close", "description": "是否显示 Tooltip 内部的关闭按钮", "doc-url": "https://wot-design-uni.cn/component/tooltip.html#attributes", "type": ["boolean"], "default": "false"}], "js": {"events": [{"name": "open", "description": "显示时触发", "doc-url": "https://wot-design-uni.cn/component/tooltip.html#events"}, {"name": "close", "description": "隐藏时触发", "doc-url": "https://wot-design-uni.cn/component/tooltip.html#events"}, {"name": "change", "description": "显隐值变化时触发", "doc-url": "https://wot-design-uni.cn/component/tooltip.html#events"}]}}, {"name": "wd-transition", "source": {"symbol": "WdTransition"}, "description": "用于在元素进入或离开时应用过渡效果。", "doc-url": "https://wot-design-uni.cn/component/transition.html#transition", "props": [{"name": "show", "description": "是否展示组件", "doc-url": "https://wot-design-uni.cn/component/transition.html#attributes", "type": ["boolean"]}, {"name": "name", "description": "动画类型", "doc-url": "https://wot-design-uni.cn/component/transition.html#attributes", "type": ["string"], "attribute-value": {"type": "enum"}, "values": [{"name": "TransitionName"}]}, {"name": "duration", "description": "动画执行时间", "doc-url": "https://wot-design-uni.cn/component/transition.html#attributes", "type": ["number", "boolean"], "default": "300(ms)"}, {"name": "custom-style", "description": "自定义样式", "doc-url": "https://wot-design-uni.cn/component/transition.html#attributes", "type": ["string"]}, {"name": "disable-touch-move", "description": "是否阻止触摸滚动", "doc-url": "https://wot-design-uni.cn/component/transition.html#attributes", "type": ["boolean"], "default": "false"}], "js": {"events": [{"name": "beforeenter", "description": "进入前触发", "doc-url": "https://wot-design-uni.cn/component/transition.html#events"}, {"name": "enter", "description": "进入时触发", "doc-url": "https://wot-design-uni.cn/component/transition.html#events"}, {"name": "afterenter", "description": "进入后触发", "doc-url": "https://wot-design-uni.cn/component/transition.html#events"}, {"name": "beforeleave", "description": "离开前触发", "doc-url": "https://wot-design-uni.cn/component/transition.html#events"}, {"name": "leave", "description": "离开时触发", "doc-url": "https://wot-design-uni.cn/component/transition.html#events"}, {"name": "afterleave", "description": "离开后触发", "doc-url": "https://wot-design-uni.cn/component/transition.html#events"}]}}, {"name": "wd-upload", "source": {"symbol": "WdUpload"}, "description": "图片、视频和文件上传组件", "doc-url": "https://wot-design-uni.cn/component/upload.html#upload", "props": [{"name": "v-model:file", "description": "上传的文件列表, 例如: [{ name: 'food.jpg', url: 'https://xxx.cdn.com/xxx.jpg' }]", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["array"], "default": "[]"}, {"name": "action", "description": "必选参数，上传的地址", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["string"]}, {"name": "header", "description": "设置上传的请求头部", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["object"]}, {"name": "multiple", "description": "是否支持多选文件", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["boolean"]}, {"name": "disabled", "description": "是否禁用", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "reupload", "description": "是否开启覆盖上传，开启后会关闭图片预览", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "limit", "description": "最大允许上传个数", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["number"]}, {"name": "show-limit-num", "description": "限制上传个数的情况下，是否展示当前上传的个数", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["boolean"], "default": "false"}, {"name": "max-size", "description": "文件大小限制，单位为`byte`", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["number"]}, {"name": "source-type", "description": "选择图片的来源，chooseImage 接口详细参数，查看[官方手册](https://uniapp.dcloud.net.cn/api/media/image.html#chooseimage)", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["array", "string"], "default": "['album', 'camera']"}, {"name": "size-type", "description": "所选的图片的尺寸，chooseImage 接口详细参数，查看[官方手册](https://uniapp.dcloud.net.cn/api/media/image.html#chooseimage)", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["array", "string"], "default": "['original', 'compressed']"}, {"name": "name", "description": "文件对应的 key，开发者在服务端可以通过这个 key 获取文件的二进制内容，uploadFile 接口详细参数，查看[官方手册](https://uniapp.dcloud.net.cn/api/request/network-file#uploadfile)", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["string"], "default": "file"}, {"name": "form-data", "description": "HTTP 请求中其他额外的 form data，uploadFile 接口详细参数，查看[官方手册](https://uniapp.dcloud.net.cn/api/request/network-file#uploadfile)", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["object"]}, {"name": "header", "description": "HTTP 请求 Header，Header 中不能设置 Referer，uploadFile 接口详细参数，查看[官方手册](https://uniapp.dcloud.net.cn/api/request/network-file#uploadfile)", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["object"]}, {"name": "on-preview-fail", "description": "预览失败执行操作", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["function({ index, imgList })"]}, {"name": "before-upload", "description": "上传文件之前的钩子，参数为上传的文件和文件列表，若返回 false 或者返回 Promise 且被 reject，则停止上传。", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["function({ files, fileList, resolve })"]}, {"name": "before-choose", "description": "选择图片之前的钩子，参数为文件列表，若返回 false 或者返回 Promise 且被 reject，则停止上传。", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["function({ fileList, resolve })"]}, {"name": "before-remove", "description": "删除文件之前的钩子，参数为要删除的文件和文件列表，若返回 false 或者返回 Promise 且被 reject，则停止上传。", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["function({ file, fileList, resolve })"]}, {"name": "before-preview", "description": "图片预览前的钩子，参数为预览的图片下标和图片列表，若返回 false 或者返回 Promise 且被 reject，则停止上传。", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["function({file, index, imgList, resolve })"]}, {"name": "build-form-data", "description": "构建上传`formData`的钩子，参数为上传的文件、待处理的`formData`，返回值为处理后的`formData`，若返回 false 或者返回 Promise 且被 reject，则停止上传。", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["function({ file, formData, resolve })"]}, {"name": "loading-type", "description": "[加载中图标类型](/component/loading)", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["string"], "default": "circular-ring"}, {"name": "loading-color", "description": "[加载中图标颜色](/component/loading)", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["string"], "default": "#ffffff"}, {"name": "loading-size", "description": "[加载中图标尺寸](/component/loading)", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["string"], "default": "24px"}, {"name": "status-key", "description": "file 数据结构中，status 对应的 key", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["string"], "default": "status"}, {"name": "image-mode", "description": "预览图片的 mode 属性", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": [{"name": "ImageMode", "source": {"symbol": "ImageMode"}}], "default": "aspectFit"}, {"name": "accept", "description": "接受的文件类型", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": [{"name": "UploadFileType", "source": {"symbol": "UploadFileType"}}], "default": "image", "attribute-value": {"type": "of-match"}, "values": [{"name": "image video media file all"}]}, {"name": "compressed", "description": "是否压缩视频，当 accept 为 video | media 时生效", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "max-duration", "description": "拍摄视频最长拍摄时间，当 accept 为 video | media 时生效，单位秒", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["Number"], "default": "60"}, {"name": "camera", "description": "使用前置或者后置相机，当 accept 为 video | media 时生效", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": [{"name": "UploadCameraType", "source": {"symbol": "UploadCameraType"}}], "default": "back", "attribute-value": {"type": "of-match"}, "values": [{"name": "front"}]}, {"name": "success-status", "description": "接口响应的成功状态（statusCode）值", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["number"], "default": "200"}, {"name": "auto-upload", "description": "是否选择文件后自动上传。为 false 时应手动调用 submit() 方法开始上传", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "upload-method", "description": "自定义上传方法", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": [{"name": "UploadMethod", "source": {"symbol": "UploadMethod"}}]}, {"name": "extension", "description": "根据文件拓展名过滤(H5支持全部类型过滤,微信小程序支持all和file时过滤,其余平台不支持)", "doc-url": "https://wot-design-uni.cn/component/upload.html#attributes", "type": ["string[]"]}], "js": {"events": [{"name": "success", "description": "上传成功时触发", "doc-url": "https://wot-design-uni.cn/component/upload.html#events"}, {"name": "fail", "description": "上传失败时触发", "doc-url": "https://wot-design-uni.cn/component/upload.html#events"}, {"name": "progress", "description": "上传中时触发", "doc-url": "https://wot-design-uni.cn/component/upload.html#events"}, {"name": "chooseerror", "description": "选择图片失败时触发", "doc-url": "https://wot-design-uni.cn/component/upload.html#events"}, {"name": "change", "description": "上传列表修改时触发", "doc-url": "https://wot-design-uni.cn/component/upload.html#events"}, {"name": "remove", "description": "移除图片时触发", "doc-url": "https://wot-design-uni.cn/component/upload.html#events"}, {"name": "oversize", "description": "文件大小超过限制时触发", "doc-url": "https://wot-design-uni.cn/component/upload.html#events"}]}}, {"name": "wd-watermark", "source": {"symbol": "WdWatermark"}, "description": "在页面或组件上添加指定的图片或文字，可用于版权保护、品牌宣传等场景。", "doc-url": "https://wot-design-uni.cn/component/watermark.html#watermark", "props": [{"name": "content", "description": "显示内容", "doc-url": "https://wot-design-uni.cn/component/watermark.html#attributes", "type": ["string"]}, {"name": "image", "description": "显示图片的地址，支持网络图片和base64（钉钉小程序支持网络图片）", "doc-url": "https://wot-design-uni.cn/component/watermark.html#attributes", "type": ["string"]}, {"name": "image-height", "description": "图片高度", "doc-url": "https://wot-design-uni.cn/component/watermark.html#attributes", "type": ["number"], "default": "100"}, {"name": "image-width", "description": "图片宽度", "doc-url": "https://wot-design-uni.cn/component/watermark.html#attributes", "type": ["number"], "default": "100"}, {"name": "gutter-x", "description": "X轴间距，单位px", "doc-url": "https://wot-design-uni.cn/component/watermark.html#attributes", "type": ["number"], "default": "0"}, {"name": "gutter-y", "description": "Y轴间距，单位px", "doc-url": "https://wot-design-uni.cn/component/watermark.html#attributes", "type": ["number"], "default": "0"}, {"name": "width", "description": "canvas画布宽度，单位px", "doc-url": "https://wot-design-uni.cn/component/watermark.html#attributes", "type": ["number"], "default": "100"}, {"name": "height", "description": "canvas画布高度，单位px", "doc-url": "https://wot-design-uni.cn/component/watermark.html#attributes", "type": ["number"], "default": "100"}, {"name": "full-screen", "description": "是否为全屏水印", "doc-url": "https://wot-design-uni.cn/component/watermark.html#attributes", "type": ["boolean"], "default": "true"}, {"name": "color", "description": "水印字体颜色", "doc-url": "https://wot-design-uni.cn/component/watermark.html#attributes", "type": ["string"], "default": "#8c8c8c"}, {"name": "size", "description": "水印字体大小，单位px", "doc-url": "https://wot-design-uni.cn/component/watermark.html#attributes", "type": ["number"], "default": "14"}, {"name": "font-style", "description": "水印字体样式（仅微信、支付宝和h5支持）", "doc-url": "https://wot-design-uni.cn/component/watermark.html#attributes", "type": ["string"], "default": "normal", "attribute-value": {"type": "enum"}, "values": [{"name": "normal"}, {"name": "italic"}, {"name": "oblique"}]}, {"name": "font-weight", "description": "水印字体的粗细（仅微信、支付宝和h5支持）", "doc-url": "https://wot-design-uni.cn/component/watermark.html#attributes", "type": ["string"], "default": "normal", "attribute-value": {"type": "enum"}, "values": [{"name": "normal"}, {"name": "bold"}, {"name": "bolder"}]}, {"name": "font-family", "description": "水印字体系列（仅微信、支付宝和h5支持）", "doc-url": "https://wot-design-uni.cn/component/watermark.html#attributes", "type": ["string"], "default": "PingFang SC"}, {"name": "rotate", "description": "水印旋转角度", "doc-url": "https://wot-design-uni.cn/component/watermark.html#attributes", "type": ["number"], "default": "-25"}, {"name": "z-index", "description": "自定义层级", "doc-url": "https://wot-design-uni.cn/component/watermark.html#attributes", "type": ["number"], "default": "1100"}, {"name": "opacity", "description": "自定义透明度，取值 0~1", "doc-url": "https://wot-design-uni.cn/component/watermark.html#attributes", "type": ["number"], "default": "0.5"}]}]}}}